# 博主监控页面需要实现的功能

## 需要实现的组件
- 批量添加监控任务弹框
  - 输入框，输入博主ID，多个博主ID用换行隔开
  - 按钮，点击按钮，批量添加监控任务
- 博主监控任务列表，列表需要存在的参数：
  - 任务ID
  - 博主名称
  - 博主ID
  - 博主头像
  - 博主粉丝数
  - 博主获赞数
  - 作品数
  - 上次更新时间
  - 操作按钮
- 博主作品列表，列表实现参考Note.vue中列表项的实现

## 交互要求
- 添加任务的交互
    - 点击添加监控任务按钮，弹出批量添加监控任务弹框
    - 点击确认添加后，弹出执行进度条，遍历获取每个链接的博主信息，博主信息获取成功则创建任务，获取失败则跳过，最终显示成功添加的监控任务数量
- 点击任务列表项中的作品数，弹出博主作品列表


## 界面要求
- 界面布局要简洁优雅
- 界面UI设计要美观大方
- 界面配色要符合主题
- 界面交互要符合用户习惯


## 修改要求
- 任务拆分成多个子任务，一步一步完成
- 一个页面中涉及多个组件，要使用组件化开发，每个组件独立一份代码文件
- 如果涉及代码文件过长，可以分段完成