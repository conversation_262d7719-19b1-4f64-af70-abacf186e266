# 小红书工具箱 - 多媒体处理模块设计方案

## 模块概述

基于现有项目架构和多媒体工具模块的技术方案，设计一个完整集成的**多媒体处理中心**，为小红书内容创作者提供音视频转换、字幕提取、图片处理等一站式多媒体处理服务。

## 需求分析

### 核心需求
1. **音视频转换**: MP4转MP3、格式转换、质量调整
2. **字幕提取**: 基于ASR技术从音频提取文字和SRT字幕
3. **图片处理**: 批量下载、格式转换、CDN优化
4. **批量处理**: 支持多文件并行处理
5. **进度监控**: 实时显示处理进度和状态

### 用户场景
- 内容创作者需要将视频提取为音频素材
- 需要为视频内容生成字幕文件
- 批量处理小红书下载的媒体文件
- 优化图片格式和质量以适应不同平台

## 技术架构设计

### 1. 后端架构 (Electron 主进程)

```
electron/main/
├── ipc/
│   └── media.ts                    # 多媒体IPC处理器
├── services/
│   ├── media-worker.ts             # 多媒体处理服务
│   ├── ffmpeg-service.ts           # FFmpeg封装服务
│   └── asr-service.ts              # ASR语音识别服务
└── utils/
    ├── media-utils.ts              # 媒体文件工具
    └── asr/
        ├── BaseASR.ts              # ASR基类
        ├── BcutASR.ts              # 哔哩哔哩ASR实现
        ├── ASRData.ts              # ASR数据模型
        └── ASRDataSeg.ts           # ASR片段模型
```

### 2. 前端架构 (Vue 3 渲染进程)

```
src/
├── views/
│   └── Media.vue                   # 多媒体处理主页面
├── components/
│   └── media/
       ├── MediaUploader.vue        # 文件上传组件
       ├── ConversionPanel.vue      # 转换面板
       ├── ProcessingQueue.vue      # 处理队列
       ├── ASRPanel.vue             # 语音识别面板
       ├── ImageProcessor.vue       # 图片处理组件
       └── BatchProcessor.vue       # 批量处理组件
└── stores/
    └── media-store.ts              # 多媒体状态管理
```

## 功能模块设计

### 1. 音视频转换模块

#### 功能特性
- **格式转换**: MP4→MP3, AVI→MP4, MOV→MP4等
- **质量设置**: 音频码率选择(128k, 192k, 320k)
- **批量转换**: 支持多文件队列处理
- **进度监控**: 实时显示转换进度

#### UI设计
```vue
<template>
  <el-card class="conversion-card">
    <template #header>
      <div class="card-header">
        <el-icon><VideoPlay/></el-icon>
        <span>音视频转换</span>
      </div>
    </template>
    
    <!-- 文件上传区域 -->
    <div class="upload-area">
      <el-upload
        drag
        multiple
        :auto-upload="false"
        :file-list="fileList"
        accept=".mp4,.avi,.mov,.mkv,.flv"
      >
        <div class="upload-content">
          <el-icon class="upload-icon"><Plus/></el-icon>
          <div class="upload-text">拖拽视频文件到此处</div>
          <div class="upload-hint">支持 MP4, AVI, MOV, MKV, FLV 格式</div>
        </div>
      </el-upload>
    </div>
    
    <!-- 转换设置 -->
    <div class="conversion-settings">
      <el-row :gutter="16">
        <el-col :span="8">
          <el-select v-model="outputFormat" placeholder="输出格式">
            <el-option label="MP3" value="mp3"/>
            <el-option label="MP4" value="mp4"/>
            <el-option label="WAV" value="wav"/>
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-select v-model="quality" placeholder="音质选择">
            <el-option label="标准(128k)" value="128k"/>
            <el-option label="高质量(192k)" value="192k"/>
            <el-option label="极高质量(320k)" value="320k"/>
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-button type="primary" @click="startConversion">
            开始转换
          </el-button>
        </el-col>
      </el-row>
    </div>
  </el-card>
</template>
```

### 2. 语音识别(ASR)模块

#### 功能特性
- **文字提取**: 从音频文件提取文字内容
- **字幕生成**: 生成SRT格式字幕文件
- **多语言支持**: 中文、英文语音识别
- **结果缓存**: 避免重复处理相同文件

#### UI设计
```vue
<template>
  <el-card class="asr-card">
    <template #header>
      <div class="card-header">
        <el-icon><Microphone/></el-icon>
        <span>语音识别</span>
      </div>
    </template>
    
    <!-- 音频文件上传 -->
    <div class="audio-upload">
      <el-upload
        drag
        :auto-upload="false"
        accept=".mp3,.wav,.m4a,.aac"
      >
        <div class="upload-content">
          <el-icon class="upload-icon"><Microphone/></el-icon>
          <div class="upload-text">上传音频文件进行语音识别</div>
          <div class="upload-hint">支持 MP3, WAV, M4A, AAC 格式</div>
        </div>
      </el-upload>
    </div>
    
    <!-- 识别设置 -->
    <div class="asr-settings">
      <el-row :gutter="16">
        <el-col :span="12">
          <el-select v-model="language" placeholder="识别语言">
            <el-option label="中文" value="zh"/>
            <el-option label="英文" value="en"/>
            <el-option label="自动识别" value="auto"/>
          </el-select>
        </el-col>
        <el-col :span="12">
          <el-checkbox-group v-model="outputFormats">
            <el-checkbox label="txt">文本文件</el-checkbox>
            <el-checkbox label="srt">字幕文件</el-checkbox>
          </el-checkbox-group>
        </el-col>
      </el-row>
    </div>
    
    <!-- 识别结果 -->
    <div class="asr-result" v-if="asrResult">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="文本内容" name="text">
          <el-input
            type="textarea"
            :rows="8"
            v-model="asrResult.text"
            readonly
          />
        </el-tab-pane>
        <el-tab-pane label="字幕格式" name="srt">
          <el-input
            type="textarea"
            :rows="8"
            v-model="asrResult.srt"
            readonly
          />
        </el-tab-pane>
      </el-tabs>
      
      <div class="result-actions">
        <el-button @click="copyToClipboard">复制内容</el-button>
        <el-button type="primary" @click="saveToFile">保存文件</el-button>
      </div>
    </div>
  </el-card>
</template>
```

### 3. 图片处理模块

#### 功能特性
- **批量下载**: 支持小红书图片批量下载
- **格式转换**: JPG, PNG, WebP格式互转
- **尺寸调整**: 批量调整图片尺寸
- **CDN优化**: 小红书CDN地址优化

#### UI设计
```vue
<template>
  <el-card class="image-processor-card">
    <template #header>
      <div class="card-header">
        <el-icon><Picture/></el-icon>
        <span>图片处理</span>
      </div>
    </template>
    
    <!-- 图片上传区域 -->
    <div class="image-upload">
      <el-upload
        drag
        multiple
        list-type="picture-card"
        :auto-upload="false"
        accept=".jpg,.jpeg,.png,.webp"
      >
        <div class="upload-content">
          <el-icon class="upload-icon"><Plus/></el-icon>
          <div class="upload-text">上传图片文件</div>
        </div>
      </el-upload>
    </div>
    
    <!-- 处理选项 -->
    <div class="image-options">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-select v-model="outputFormat" placeholder="输出格式">
            <el-option label="保持原格式" value="original"/>
            <el-option label="JPG" value="jpg"/>
            <el-option label="PNG" value="png"/>
            <el-option label="WebP" value="webp"/>
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-input-number
            v-model="quality"
            :min="10"
            :max="100"
            placeholder="图片质量"
          />
        </el-col>
        <el-col :span="6">
          <el-checkbox v-model="resizeEnabled">调整尺寸</el-checkbox>
        </el-col>
        <el-col :span="6">
          <el-input-number
            v-model="maxWidth"
            :disabled="!resizeEnabled"
            placeholder="最大宽度"
          />
        </el-col>
      </el-row>
    </div>
  </el-card>
</template>
```

### 4. 批量处理模块

#### 功能特性
- **任务队列**: 多文件并行处理队列
- **进度监控**: 实时显示每个任务的处理进度
- **错误处理**: 失败任务重试机制
- **结果管理**: 处理结果统计和文件管理

#### UI设计
```vue
<template>
  <el-card class="batch-processor-card">
    <template #header>
      <div class="card-header">
        <el-icon><List/></el-icon>
        <span>批量处理队列</span>
        <div class="header-actions">
          <el-button size="small" @click="startAll">全部开始</el-button>
          <el-button size="small" @click="pauseAll">暂停全部</el-button>
          <el-button size="small" @click="clearCompleted">清理完成</el-button>
        </div>
      </div>
    </template>
    
    <!-- 任务列表 -->
    <div class="task-list">
      <div
        v-for="task in tasks"
        :key="task.id"
        class="task-item"
      >
        <div class="task-info">
          <div class="task-name">{{ task.fileName }}</div>
          <div class="task-type">{{ task.type }}</div>
          <div class="task-status" :class="task.status">
            {{ getStatusText(task.status) }}
          </div>
        </div>
        
        <div class="task-progress">
          <el-progress
            :percentage="task.progress"
            :status="task.status === 'error' ? 'exception' : undefined"
          />
        </div>
        
        <div class="task-actions">
          <el-button
            size="small"
            v-if="task.status === 'pending'"
            @click="startTask(task.id)"
          >
            开始
          </el-button>
          <el-button
            size="small"
            v-if="task.status === 'processing'"
            @click="pauseTask(task.id)"
          >
            暂停
          </el-button>
          <el-button
            size="small"
            v-if="task.status === 'error'"
            @click="retryTask(task.id)"
          >
            重试
          </el-button>
          <el-button
            size="small"
            type="danger"
            @click="removeTask(task.id)"
          >
            删除
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 统计信息 -->
    <div class="batch-stats">
      <el-row :gutter="16">
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-number">{{ stats.total }}</div>
            <div class="stat-label">总任务</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-number">{{ stats.completed }}</div>
            <div class="stat-label">已完成</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-number">{{ stats.processing }}</div>
            <div class="stat-label">处理中</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-number">{{ stats.failed }}</div>
            <div class="stat-label">失败</div>
          </div>
        </el-col>
      </el-row>
    </div>
  </el-card>
</template>
```

## 样式设计规范

### 1. 主题色彩
```scss
// 多媒体模块专用色彩
$media-primary: #ff2442;        // 主色调(继承项目主色)
$media-secondary: #722ed1;      // 多媒体辅助色(紫色)
$media-success: #52c41a;        // 处理成功色
$media-warning: #faad14;        // 处理警告色
$media-error: #ff4d4f;          // 处理错误色

// 功能模块色彩
$video-color: #1890ff;          // 视频相关
$audio-color: #13c2c2;          // 音频相关
$image-color: #eb2f96;          // 图片相关
$text-color: #52c41a;           // 文本相关
```

### 2. 卡片样式
```scss
.media-card {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  margin-bottom: 16px;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  }
  
  .card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #262626;
    
    .el-icon {
      color: $media-primary;
      font-size: 18px;
    }
  }
}
```

### 3. 上传区域样式
```scss
.upload-area {
  .el-upload-dragger {
    background: linear-gradient(135deg, #f6f9ff 0%, #f0f5ff 100%);
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: $media-primary;
      background: linear-gradient(135deg, #fff2f4 0%, #fff0f1 100%);
    }
  }
  
  .upload-content {
    padding: 24px;
    text-align: center;
    
    .upload-icon {
      font-size: 48px;
      color: #d9d9d9;
      margin-bottom: 16px;
    }
    
    .upload-text {
      font-size: 16px;
      color: #262626;
      margin-bottom: 8px;
    }
    
    .upload-hint {
      font-size: 12px;
      color: #8c8c8c;
    }
  }
}
```

### 4. 进度显示样式
```scss
.task-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin-bottom: 8px;
  background: #fafafa;
  transition: all 0.2s ease;
  
  &:hover {
    background: #ffffff;
    border-color: #d9d9d9;
  }
  
  .task-info {
    flex: 1;
    margin-right: 16px;
    
    .task-name {
      font-weight: 500;
      color: #262626;
      margin-bottom: 4px;
    }
    
    .task-type {
      font-size: 12px;
      color: #8c8c8c;
    }
    
    .task-status {
      font-size: 12px;
      padding: 2px 6px;
      border-radius: 4px;
      
      &.pending { background: #f0f0f0; color: #8c8c8c; }
      &.processing { background: #e6f7ff; color: #1890ff; }
      &.completed { background: #f6ffed; color: #52c41a; }
      &.error { background: #fff2f0; color: #ff4d4f; }
    }
  }
  
  .task-progress {
    flex: 2;
    margin-right: 16px;
  }
  
  .task-actions {
    display: flex;
    gap: 8px;
  }
}
```

## 路由配置

```typescript
// src/router/index.ts
{
  path: '/media',
  name: 'Media',
  component: () => import('@/views/Media.vue'),
  meta: {
    title: '多媒体处理',
    icon: 'VideoPlay',
    requiresAuth: false
  }
}
```

## IPC接口设计

```typescript
// electron/main/ipc/media.ts
export interface MediaIpcHandlers {
  // 音视频转换
  'convert-video': (inputPath: string, outputPath: string, options: ConvertOptions) => Promise<ApiResponse<string>>;
  'get-conversion-progress': (taskId: string) => Promise<ApiResponse<number>>;
  
  // 语音识别
  'extract-audio-text': (audioPath: string, language: string) => Promise<ApiResponse<ASRResult>>;
  'generate-subtitle': (audioPath: string) => Promise<ApiResponse<string>>;
  
  // 图片处理
  'process-images': (imagePaths: string[], options: ImageProcessOptions) => Promise<ApiResponse<string[]>>;
  'optimize-images': (imagePaths: string[]) => Promise<ApiResponse<string[]>>;
  
  // 批量处理
  'create-batch-task': (tasks: BatchTask[]) => Promise<ApiResponse<string>>;
  'get-batch-progress': (batchId: string) => Promise<ApiResponse<BatchProgress>>;
  'cancel-batch-task': (batchId: string) => Promise<ApiResponse<boolean>>;
}
```

## 状态管理设计

```typescript
// src/stores/media-store.ts
export interface MediaState {
  // 转换任务
  conversionTasks: ConversionTask[];
  
  // ASR任务
  asrTasks: ASRTask[];
  
  // 图片处理任务
  imageProcessTasks: ImageProcessTask[];
  
  // 批量任务
  batchTasks: BatchTask[];
  
  // 全局设置
  settings: {
    outputDirectory: string;
    defaultQuality: string;
    autoCleanCompleted: boolean;
    maxConcurrentTasks: number;
  };
  
  // 统计数据
  stats: {
    totalProcessed: number;
    totalSize: number;
    totalTime: number;
  };
}
```

## 集成方案

### 1. 不重复现有功能
- **图片下载**: 复用现有的小红书图片下载逻辑
- **文件管理**: 使用现有的文件系统IPC处理器
- **进度显示**: 复用现有的ProgressBar组件
- **状态管理**: 与现有Pinia store集成

### 2. 技术依赖管理
```json
{
  "dependencies": {
    "fluent-ffmpeg": "^2.1.3",
    "@ffmpeg-installer/ffmpeg": "^1.1.0",
    "sharp": "^0.33.0",
    "node-schedule": "^2.1.1"
  }
}
```

### 3. 与现有架构集成
- **IPC注册**: 在`electron/main/ipcHandlers.ts`中注册媒体处理器
- **服务启动**: 在主进程启动时初始化媒体服务
- **菜单集成**: 在应用菜单中添加多媒体处理入口
- **快捷操作**: 在其他页面添加快捷处理按钮

## 实施计划

### 第一阶段: 基础架构 (1-2天)
1. 创建基础目录结构和文件
2. 配置IPC处理器和路由
3. 搭建主页面框架和基础组件

### 第二阶段: 核心功能 (3-4天)
1. 实现音视频转换功能
2. 集成ASR语音识别功能
3. 开发图片处理功能

### 第三阶段: 批量处理 (2-3天)
1. 实现批量任务队列
2. 添加进度监控和错误处理
3. 完善用户交互和反馈

### 第四阶段: 优化完善 (1-2天)
1. 性能优化和错误处理
2. UI/UX优化和测试
3. 文档完善和部署准备

## 预期效果

1. **用户体验**: 提供一站式多媒体处理解决方案
2. **功能完整**: 覆盖音视频转换、字幕提取、图片处理的完整链路
3. **性能优秀**: 支持批量并行处理，充分利用系统资源
4. **界面美观**: 保持与现有项目的设计一致性
5. **扩展性强**: 模块化设计，便于后续功能扩展

这个设计方案充分考虑了现有项目的架构特点和设计规范，确保新模块能够无缝集成，同时提供强大的多媒体处理能力。