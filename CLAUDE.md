# CLAUDE.md

此文件为 Claude Code (claude.ai/code) 在此代码库中工作时提供指导。

## 项目概述

这是"KOL小红书工具箱应用"，一个基于 Electron 的桌面应用程序，用于分析和监控小红书内容。该应用提供笔记分析、博主监控、批量处理和数据导出功能。

## 开发命令

### 开发
- `npm run dev` - 启动开发服务器，支持热重载
- `npm run build` - 构建应用程序（TypeScript 编译 + Vite 构建）
- `npm run preview` - 预览构建后的应用程序

### 构建分发版本
- `npm run build:mac` - 构建 macOS 应用
- `npm run build:mac-universal` - 构建通用 macOS 应用（Intel + Apple Silicon）
- `npm run build:win` - 构建 Windows 应用
- `npm run build:all` - 同时构建 macOS 和 Windows 版本

### 构建注意事项
- 在构建分发版本之前，始终先运行 `npm run build`
- 分发配置位于 `electron-builder.yml`
- 构建的 electron 代码输出到 `dist-electron/`
- Vite 构建输出到 `dist/`

## 架构

### 技术栈
- **前端**: Vue 3 + TypeScript + Vite
- **UI 框架**: Element Plus
- **状态管理**: Pinia
- **桌面框架**: Electron
- **构建工具**: Vite 配合 electron 插件

### 核心目录结构
```
electron/main/          # Electron 主进程
├── api/               # 小红书 API 集成和网页抓取
├── ipc/               # IPC 处理器（模块化）
├── services/          # 后台服务（监控、工作器）
├── store/             # Electron-store 持久化存储
└── utils/             # 工具函数

src/                   # Vue 前端（渲染进程）
├── components/        # Vue 组件
├── views/             # 页面组件
└── router/            # Vue Router 设置
```

### IPC 架构
应用使用模块化 IPC 系统，处理器按域划分：
- `ipc/xhs.ts` - 小红书 API 和爬虫操作
- `ipc/monitor.ts` - 监控任务管理
- `ipc/excel.ts` - Excel 导入/导出功能
- `ipc/app.ts` - 应用级操作（登录、登出等）

IPC 处理器在 `ipcHandlers.ts` 中注册，监控处理器有 10 秒延迟。

### 核心服务
- **XhsService** (`services/xhs-worker.ts`) - 主要的小红书数据获取服务
- **MonitorService** (`services/monitor-service.ts`) - 任务调度和监控服务
- **ExcelService** (`services/excel-worker.ts`) - Excel 处理服务

### API 集成
- **WebXhsAPI** (`api/web-xhs-api.ts`) - 主要的基于网页的小红书 API 客户端
- **XhsSpider** (`api/xhs-spider.ts`) - 网页爬虫功能
- 使用隐藏的浏览器窗口进行身份验证和数据获取

### 窗口管理
应用管理多个窗口：
- 主窗口 - 主要 UI
- 登录窗口 - 隐藏的小红书认证窗口（可切换）
- 窗口状态在 `windows.ts` 和 `appState.ts` 中管理

### 数据模型
`api/models/` 中的关键 TypeScript 接口：
- `Note` - 小红书笔记/帖子数据结构
- `WebNote` - 网页特定的笔记格式
- `User` - 用户资料信息

## 路由功能概览

- `/`（首页）- 单个笔记分析和媒体下载
- `/note` - 按关键词批量获取笔记
- `/batch` - 从 Excel 文件批量处理
- `/monitor` - 实时监控仪表板
  - 笔记监控任务
  - 博主监控任务
- `/check` 和 `/user_check` - 数据验证工具

## 开发说明

### 认证流程
应用使用隐藏的登录窗口打开小红书网页界面进行基于 Cookie 的认证。Cookie 通过 `electron-store` 和 `tough-cookie` 管理。

### 监控系统
后台监控使用 `node-schedule` 进行任务调度。监控任务持久化存储在 electron-store 中，可以追踪单个笔记和博主资料。

### 文件处理
Excel 操作使用 `node-xlsx`。图片处理使用 `sharp`。所有文件操作都通过 IPC 传递给主进程。

### 错误处理
大多数异步操作返回 `{success: boolean, data?: any, error?: string}` 格式，以便在主进程和渲染进程之间进行一致的错误处理。