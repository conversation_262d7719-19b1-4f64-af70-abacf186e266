# 小红书博主笔记批量获取功能Electron原生方案（无Puppeteer）

## 1. 方案概述

本方案通过Electron自带的BrowserWindow和webContents接口，无需集成Puppeteer，即可实现“通过小红书博主主页链接，批量获取该博主所有笔记链接与详情”的能力，适合页面结构简单、反爬不强的场景。

---

## 2. 核心流程

### 2.1 用户操作流程
1. 用户在Electron应用界面输入或粘贴小红书博主主页链接（如：https://www.xiaohongshu.com/user/profile/xxxxxx）。
2. 点击"获取全部笔记"按钮。
3. 应用自动打开隐藏的BrowserWindow，模拟滚动加载全部笔记。
4. 自动提取所有笔记的ID、Token、标题、封面等信息。
5. 展示在界面上，支持导出/下载/进一步处理。

### 2.2 技术实现流程

#### 1）主进程（main）
- 监听渲染进程的"获取笔记"请求。
- 创建隐藏的BrowserWindow，打开目标博主主页。
- 自动滚动页面，确保所有笔记加载完毕。
- 注入JS脚本，提取`window.__INITIAL_STATE__`中的所有笔记数据。
- 结构化返回所有笔记信息。
- 通过IPC将数据发送回渲染进程。

#### 2）渲染进程（renderer）
- 提供输入框、按钮、进度提示、结果展示等UI。
- 通过IPC向主进程发起"获取笔记"请求。
- 接收主进程返回的笔记数据，渲染到页面。
- 支持导出为Excel/CSV、批量下载等操作。

---

## 3. 关键代码结构

### 3.1 主进程（main.js）
```js
const { ipcMain, BrowserWindow } = require('electron');

ipcMain.handle('fetch-xhs-user-notes', async (event, userProfileUrl) => {
  const win = new BrowserWindow({
    show: false,
    webPreferences: { nodeIntegration: false, contextIsolation: true }
  });
  await win.loadURL(userProfileUrl);

  // 自动滚动加载全部笔记
  await win.webContents.executeJavaScript(`
    new Promise((resolve) => {
      let totalHeight = 0;
      const distance = 1000;
      const timer = setInterval(() => {
        const scrollHeight = document.body.scrollHeight;
        window.scrollBy(0, distance);
        totalHeight += distance;
        if(totalHeight >= scrollHeight){
          clearInterval(timer);
          setTimeout(resolve, 1000); // 等待数据加载
        }
      }, 500);
      setTimeout(resolve, 8000); // 最多滚动8秒
    });
  `);

  // 提取所有笔记数据
  const notes = await win.webContents.executeJavaScript(`
    (function() {
      const notesRaw = window.__INITIAL_STATE__?.user?.notes?._rawValue || [];
      return notesRaw.map(item => ({
        id: item.id,
        title: item.title,
        cover: item.cover?.url,
        xsecToken: item.xsecToken,
      }));
    })();
  `);

  win.destroy();
  return notes;
});
```

### 3.2 渲染进程（renderer.js/tsx）
```js
// 伪代码示例
const handleFetchNotes = async () => {
  setLoading(true);
  const notes = await window.electron.invoke('fetch-xhs-user-notes', userProfileUrl);
  setNotes(notes);
  setLoading(false);
};
```

---

## 4. 注意事项
- 该方案适合页面结构简单、反爬不强的场景。
- 若遇到页面加载不全、反爬、需要登录等复杂情况，可再考虑集成Puppeteer。
- 建议设置User-Agent、Cookie等参数，模拟真实浏览器环境，避免反爬。
- 数据量大时注意内存与性能优化。
- 隐藏的BrowserWindow建议及时销毁，避免资源泄漏。

---

## 5. 扩展建议
- 支持多线程/队列批量处理多个博主。
- 支持笔记内容、图片、视频的进一步批量下载。
- 支持导出为多种格式（Excel、CSV、JSON等）。
- UI可视化进度与异常提示。
- 如遇反爬、页面结构变动等问题，可平滑切换到Puppeteer方案。

---

## 6. 参考资料
- [Electron官方文档](https://www.electronjs.org/)
- [小红书网页版结构分析](https://www.xiaohongshu.com/) 
