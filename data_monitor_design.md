# 数据监控模块设计方案

## 1. 核心需求回顾

*   **模块名称：** 数据监控
*   **功能类型：** 笔记监控、博主监控
*   **笔记监控：**
    *   设置多篇笔记进行监控。
    *   定时监控笔记数据：点赞数、评论数、转发数、阅读量。
*   **博主监控：**
    *   设置多个博主进行监控。
    *   定时监控博主最新发布的笔记。
*   **数据获取：** 参考 `xhs-worker.ts` 中的方法。
*   **数据存储：** 优先使用 Electron-store，未来考虑 SQLite。
*   **监控频率：** 可设置为每小时、每天、每周、每月。
*   **通知机制：** 暂时不需要。
*   **数据展示：** 需要美观的图表展示。
*   **任务管理：** 支持暂停、恢复、删除监控任务。
*   **UI风格：** 与现有应用保持一致。

## 2. 技术选型

*   **前端框架：** Vue 3 (已使用)
*   **UI 组件库：** Element Plus (已使用)
*   **状态管理：** Vuex 或 Pinia (根据现有项目情况决定，若无大型状态管理，可使用 Vue 3 Composition API 自行管理)
*   **数据持久化：** Electron-store (根据需求，未来可迁移至 SQLite)
*   **图表库：** ECharts (功能强大，社区活跃，与 Vue 3 结合良好)
*   **定时任务：** Electron 主进程中的 `setInterval` 或 `node-schedule` 库 (更灵活的定时任务管理)
*   **数据抓取：** 沿用 `electron/main/services/xhs-worker.ts` 中 `XhsSpider` 的能力。

## 3. 模块架构

我们将采用前后端分离（Electron 主进程/渲染进程）的架构，并遵循模块化设计原则。

```mermaid
graph TD
    A[用户界面 - 渲染进程] --> B{IPC 通信}
    B --> C[主进程 - 数据监控服务]
    C --> D[Electron-store 数据存储]
    C --> E[XhsSpider 数据抓取]
    C --> F[定时任务调度]

    subgraph 渲染进程 (Vue 3)
        A1[数据监控页面] --> A2[笔记监控组件]
        A1 --> A3[博主监控组件]
        A2 --> A4[任务列表]
        A2 --> A5[数据图表]
        A3 --> A6[任务列表]
        A3 --> A7[数据图表]
    end

    subgraph 主进程 (Node.js)
        C1[MonitorService] --> C2[NoteMonitorTask]
        C1 --> C3[BloggerMonitorTask]
        C1 --> D
        C1 --> E
        C1 --> F
    end
```

## 4. 实现思路

### 4.1. 主进程 (Backend)

*   **`MonitorService` (新服务):**
    *   负责管理所有监控任务的生命周期（创建、启动、暂停、恢复、删除）。
    *   与渲染进程进行 IPC 通信，接收任务配置和发送监控结果。
    *   初始化并管理 `NoteMonitorTask` 和 `BloggerMonitorTask` 实例。
    *   处理数据持久化逻辑，将任务配置和历史数据存储到 Electron-store。
    *   提供接口供渲染进程查询历史监控数据。
*   **`NoteMonitorTask` (新类):**
    *   每个实例代表一个笔记监控任务。
    *   包含笔记ID、监控频率、上次监控时间等信息。
    *   内部使用定时器 (`setInterval` 或 `node-schedule`) 按照设定的频率触发数据抓取。
    *   调用 `XhsSpider` 的方法获取笔记的最新数据（点赞、评论、转发、阅读量）。
    *   将抓取到的数据与历史数据进行比对，并存储新的数据点。
*   **`BloggerMonitorTask` (新类):**
    *   每个实例代表一个博主监控任务。
    *   包含博主ID、监控频率、上次监控时间、上次最新笔记ID等信息。
    *   内部使用定时器触发数据抓取。
    *   调用 `XhsSpider` 的方法获取博主最新发布的笔记列表。
    *   比对最新笔记列表与上次记录，发现新笔记后记录。
    *   存储新笔记信息。
*   **数据存储：**
    *   使用 Electron-store 存储监控任务的配置（`monitor-tasks.json`）和每个任务的历史监控数据（例如，`note-data-<noteId>.json`，`blogger-data-<bloggerId>.json`）。
    *   数据结构设计：
        *   **任务配置：**
            ```json
            {
              "noteTasks": [
                {
                  "id": "task_note_123",
                  "type": "note",
                  "noteId": "xxxxxxxx",
                  "name": "笔记A监控",
                  "frequency": "hourly", // hourly, daily, weekly, monthly
                  "lastRunTime": "2023-10-26T10:00:00Z",
                  "status": "running" // running, paused
                }
              ],
              "bloggerTasks": [
                {
                  "id": "task_blogger_456",
                  "type": "blogger",
                  "bloggerId": "yyyyyyy",
                  "name": "博主B监控",
                  "frequency": "daily",
                  "lastRunTime": "2023-10-25T12:00:00Z",
                  "status": "running",
                  "lastKnownNoteIds": ["note_id_z1", "note_id_z2"] // 用于判断新笔记
                }
              ]
            }
            ```
        *   **笔记历史数据：**
            ```json
            // note-data-xxxxxxxx.json
            {
              "noteId": "xxxxxxxx",
              "dataPoints": [
                {
                  "timestamp": "2023-10-26T10:00:00Z",
                  "likes": 100,
                  "comments": 20,
                  "forwards": 5,
                  "reads": 1000
                },
                {
                  "timestamp": "2023-10-26T11:00:00Z",
                  "likes": 105,
                  "comments": 22,
                  "forwards": 6,
                  "reads": 1050
                }
              ]
            }
            ```
        *   **博主历史数据：**
            ```json
            // blogger-data-yyyyyyy.json
            {
              "bloggerId": "yyyyyyy",
              "newNotes": [
                {
                  "timestamp": "2023-10-26T10:30:00Z",
                  "noteId": "new_note_id_1",
                  "title": "博主B最新笔记1",
                  "url": "http://...",
                  "cover": "http://..."
                },
                {
                  "timestamp": "2023-10-26T14:00:00Z",
                  "noteId": "new_note_id_2",
                  "title": "博主B最新笔记2",
                  "url": "http://...",
                  "cover": "http://..."
                }
              ]
            }
            ```
*   **IPC 通信：** 定义清晰的 IPC 接口，用于渲染进程与主进程之间的数据交互。
    *   渲染进程 -> 主进程：添加任务、暂停任务、恢复任务、删除任务、获取任务列表、获取历史数据。
    *   主进程 -> 渲染进程：任务状态更新、新数据点通知（可选，目前需求不需要）。

### 4.2. 渲染进程 (Frontend)

*   **路由配置：** 在 `src/router/index.ts` 中添加新的路由 `/monitor`。
*   **新页面：** 创建 `src/views/Monitor.vue` 作为数据监控的主页面。
*   **组件拆分：**
    *   `Monitor.vue` 页面内部包含两个主要 Tab 或区域：`NoteMonitor.vue` 和 `BloggerMonitor.vue`。
    *   每个监控类型内部包含：
        *   **任务配置区域：** 用于添加新的监控任务（输入笔记ID/博主ID，选择频率）。
        *   **任务列表：** 展示当前所有监控任务，包含任务名称、ID、状态、上次运行时间、操作（暂停/恢复/删除/查看详情）。
        *   **数据展示区域：** 用于展示选定任务的历史数据图表和详细列表。
*   **UI 设计 (与现有应用风格保持一致):**
    *   **导航栏：** 在 `src/App.vue` 的 `el-menu` 中添加一个新的 `el-menu-item`，图标可以使用 Element Plus 提供的 `Trend` 或 `DataAnalysis`。
    *   **页面布局：** 沿用 `App.vue` 中 `el-container`, `el-header`, `el-main` 的布局。
    *   **卡片风格：** 广泛使用 `el-card` 组件，并应用 `global.scss` 中定义的 `$border-radius-card`, `$box-shadow-light` 等样式。
    *   **表单元素：** 使用 Element Plus 的 `el-input`, `el-select`, `el-button` 等，保持圆角和颜色风格。
    *   **表格：** 使用 `el-table` 展示任务列表和详细数据，应用 `global.scss` 中的表格优化样式。
    *   **图表：** 使用 ECharts 绘制折线图、柱状图等，展示笔记数据（点赞、评论、转发、阅读量）随时间的变化趋势。博主监控可以展示新笔记发布的时间线。
    *   **颜色：** 统一使用 `global.scss` 中定义的 `$primary-color` (小红书品牌红色) 及其辅助色、中性色。
    *   **间距和字体：** 遵循 `global.scss` 中定义的 `$spacing-*` 和 `$font-family`。

### 4.3. 数据获取与 `xhs-worker.ts` 的集成

*   在主进程的 `MonitorService` 或 `NoteMonitorTask`/`BloggerMonitorTask` 中，直接实例化 `XhsService` (或通过依赖注入)。
*   调用 `XhsService` 中已有的方法：
    *   `getNoteInfo(input: string)`: 用于获取笔记的详细信息，包括点赞、评论、转发、阅读量等。
    *   `getUserInfo(userId: string)`: 用于获取博主的用户信息。
    *   `getNotesByKeyword(item: any)` 或 `getNotes()` (如果能获取博主所有笔记): 用于获取博主最新发布的笔记列表。需要根据 `XhsSpider` 的实际能力来确定如何获取博主最新笔记。如果 `getNotesByKeyword` 只能按关键词搜索，可能需要 `XhsSpider` 额外提供按用户ID获取最新笔记的接口，或者通过多次调用 `getNotesByKeyword` 并筛选。

## 5. UI 设计草图 (概念)

### 5.1. 侧边栏导航更新 (`src/App.vue`)

在现有导航菜单中添加“数据监控”入口：

```html
<!-- src/App.vue -->
<el-menu-item index="/monitor">
  <el-icon>
    <Trend /> <!-- 或 <DataAnalysis /> -->
  </el-icon>
  <span>数据监控</span>
</el-menu-item>
```

### 5.2. 数据监控主页面 (`src/views/Monitor.vue`)

采用 Tab 切换或左右布局，分为“笔记监控”和“博主监控”两个子视图。

```mermaid
graph TD
    A[数据监控页面 (Monitor.vue)] --> B{Tab 切换 / 左右布局}
    B --> C[笔记监控 (NoteMonitor.vue)]
    B --> D[博主监控 (BloggerMonitor.vue)]
```

### 5.3. 笔记监控页面 (`src/views/NoteMonitor.vue`)

```mermaid
graph TD
    A[笔记监控页面] --> B[添加监控任务区域]
    A --> C[监控任务列表]
    A --> D[笔记数据图表展示]

    subgraph 添加监控任务区域
        B1[输入笔记链接/ID]
        B2[选择监控频率 (小时/天/周/月)]
        B3[添加任务按钮]
    end

    subgraph 监控任务列表
        C1[任务名称]
        C2[笔记ID]
        C3[监控频率]
        C4[上次运行时间]
        C5[状态 (运行中/暂停)]
        C6[操作 (暂停/恢复/删除/查看详情)]
    end

    subgraph 笔记数据图表展示
        D1[图表类型选择 (折线图)]
        D2[数据指标选择 (点赞/评论/转发/阅读量)]
        D3[时间范围选择]
        D4[ECharts 图表区域]
        D5[详细数据列表 (可选)]
    end
```

### 5.4. 博主监控页面 (`src/views/BloggerMonitor.vue`)

```mermaid
graph TD
    A[博主监控页面] --> B[添加监控任务区域]
    A --> C[监控任务列表]
    A --> D[博主最新笔记列表/时间线]

    subgraph 添加监控任务区域
        B1[输入博主主页链接/ID]
        B2[选择监控频率 (小时/天/周/月)]
        B3[添加任务按钮]
    end

    subgraph 监控任务列表
        C1[任务名称]
        C2[博主ID]
        C3[监控频率]
        C4[上次运行时间]
        C5[状态 (运行中/暂停)]
        C6[操作 (暂停/恢复/删除/查看详情)]
    end

    subgraph 博主最新笔记列表/时间线
        D1[最新笔记列表 (标题, 发布时间, 链接, 封面图)]
        D2[时间线图 (可选，展示新笔记发布时间点)]
    end