/**
 * URL处理工具类，提供URL解析和处理相关的功能
 */
import { BrowserWindow } from "electron";
import { XhsUrlInfo } from "../api/models/api-types";

/**
 * 从文本中提取URL
 * @param text 包含URL的文本
 * @returns 提取的URL，如果没有找到则返回空字符串
 */
export function extractUrlFromText(text: string): string {
    const urlReg = /(https?:\/\/[^\s]+)/;
    const match = text.match(urlReg);

    if (match && match[0]) {
        return match[0];
    }

    return "";
}

/**
 * 通过短链接获取完整的URL
 * @param shortLink 小红书短链接
 * @returns Promise<string> 解析后的完整URL
 */
export function getNoteUrlByShortLink(shortLink: string): Promise<string> {
    return new Promise((resolve) => {
        const redirectWindow = new BrowserWindow({
            show: false, // 不显示窗口
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
            },
        });

        // 监听重定向事件
        redirectWindow.webContents.on("will-redirect", (event, url) => {
            console.log("Redirecting to:", url);
            resolve(url);
            redirectWindow.close(); // 完成重定向后关闭窗口
        });

        // 加载短链接以触发重定向
        redirectWindow.loadURL(shortLink);

        // 设置超时处理
        setTimeout(() => {
            if (!redirectWindow.isDestroyed()) {
                console.log("Redirect timeout, closing window");
                redirectWindow.close();
                resolve("");
            }
        }, 10000); // 10秒超时
    });
}

/**
 * 从URL中解析笔记ID和相关token
 * @param url 小红书URL
 * @returns Promise<XhsUrlInfo> 包含笔记ID和token的对象
 */
export async function getNoteIdAndTokensByUrl(url: string): Promise<XhsUrlInfo> {
    let reg;
    let content = url;
    let xsecSource: string | null = null;
    let xsecToken: string | null = null;

    if (url.includes("xhslink.com")) {
        reg = /(https?:\/\/xhslink\.com\/[a-zA-Z]+\/[a-zA-Z0-9]+)/;
        const shortLink = reg.exec(url)?.[1];
        console.log(`短链接: ${shortLink}`);
        content = await getNoteUrlByShortLink(shortLink || "");
        reg = /\/discovery\/item\/([^\/?]+)/;
    } else if (url.includes("explore")) {
        reg = /\/explore\/([^\/?]+)/;
    } else if (url.includes("discovery")) {
        reg = /\/discovery\/item\/([^\/?]+)/;
    } else {
        reg = /(\w+)/;
    }

    // 解析URL参数
    const urlParams = new URLSearchParams(content.split("?")[1] || "");
    xsecSource = urlParams.get("xsec_source");
    xsecToken = urlParams.get("xsec_token");

    // 从URL中提取笔记ID
    const arr = reg.exec(content);
    if (arr && arr.length > 1) {
        return { noteId: arr[1], xsecSource, xsecToken };
    }

    return { noteId: "", xsecSource, xsecToken };
}

/**
 * 从文本中提取笔记ID和Token
 * @param text 包含小红书链接的文本
 * @returns Promise<XhsUrlInfo> 包含笔记ID和token的对象
 */
export async function getNoteIdAndTokensFromText(text: string): Promise<XhsUrlInfo> {
    const url = extractUrlFromText(text);

    if (url) {
        console.log(`从文本中提取的URL: ${url}`);
        return getNoteIdAndTokensByUrl(url);
    }

    return { noteId: "", xsecSource: null, xsecToken: null };
} 