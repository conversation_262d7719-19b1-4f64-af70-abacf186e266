export class ASRDataSeg {
    start: number;
    end: number;
    text: string;

    constructor(start: number, end: number, text: string) {
        this.start = start;
        this.end = end;
        this.text = text;
    }

    /**
     * 获取时长（秒）
     */
    getDuration(): number {
        return this.end - this.start;
    }

    /**
     * 格式化时间为 HH:MM:SS,mmm 格式（SRT字幕格式）
     */
    formatTime(seconds: number): string {
        // 需要先除以1000
        seconds = seconds / 1000;
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        const milliseconds = Math.floor((seconds % 1) * 1000);

        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${milliseconds.toString().padStart(3, '0')}`;
    }

    /**
     * 转换为SRT字幕格式的行
     */
    toSrtLine(index: number): string {
        const startTime = this.formatTime(this.start);
        const endTime = this.formatTime(this.end);
        return `${index}\n${startTime} --> ${endTime}\n${this.text}\n`;
    }

    /**
     * 检查是否与另一个片段有时间重叠
     */
    overlaps(other: ASRDataSeg): boolean {
        return this.start < other.end && this.end > other.start;
    }

    /**
     * 合并两个连续的片段
     */
    merge(other: ASRDataSeg): ASRDataSeg {
        const newStart = Math.min(this.start, other.start);
        const newEnd = Math.max(this.end, other.end);
        const newText = this.text + ' ' + other.text;
        return new ASRDataSeg(newStart, newEnd, newText.trim());
    }

    /**
     * 获取片段的简要信息
     */
    getSummary(): string {
        return `[${this.formatTime(this.start)} - ${this.formatTime(this.end)}] ${this.text.substring(0, 50)}${this.text.length > 50 ? '...' : ''}`;
    }
}