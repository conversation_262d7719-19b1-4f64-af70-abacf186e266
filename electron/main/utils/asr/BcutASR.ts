import axios from 'axios';
import fs from 'fs';
import path from 'path';
import { BaseASR } from './BaseASR';
import { ASRData } from './ASRData';
import { ASRDataSeg } from './ASRDataSeg';

const API_BASE_URL = "https://member.bilibili.com/x/bcut/rubick-interface";

// API Endpoints
const API_REQ_UPLOAD = `${API_BASE_URL}/resource/create`;
const API_COMMIT_UPLOAD = `${API_BASE_URL}/resource/create/complete`;
const API_CREATE_TASK = `${API_BASE_URL}/task`;
const API_QUERY_RESULT = `${API_BASE_URL}/task/result`;

export class BcutASR extends BaseASR {
    resourceId?: string;
    taskId?: string;
    inBossKey?: string;
    uploadId?: string;
    uploadUrls: string[] = [];
    perSize?: number;
    clips?: number;
    etags: string[] = [];
    downloadUrl?: string;
    language: string = 'zh'; // 默认中文

    constructor(audioFile: string, useCache: boolean = true, language: string = 'zh') {
        super(audioFile, useCache);
        this.language = language;
        this.headers = {
            ...this.headers,
            'Accept': 'application/json',
            'Referer': 'https://www.bilibili.com/',
            'Origin': 'https://www.bilibili.com'
        };
    }

    /**
     * 上传音频文件
     */
    async upload(): Promise<void> {
        console.log(`[BcutASR] 开始上传音频文件: ${this.audioFile}`);

        // 验证音频文件
        this.validateAudioFile();

        const fileBinary = fs.readFileSync(this.audioFile);
        const fileName = path.basename(this.audioFile);
        const fileExt = path.extname(fileName).slice(1).toLowerCase();

        const payload = {
            type: 2,
            name: fileName,
            size: fileBinary.length,
            ResourceFileType: fileExt,
            model_id: '8',
        };

        console.log(`[BcutASR] 请求上传许可，文件大小: ${(fileBinary.length / 1024 / 1024).toFixed(2)}MB`);

        try {
            const resp = await axios.post(API_REQ_UPLOAD, payload, {
                headers: this.headers,
                timeout: 30000 // 30秒超时
            });

            if (resp.data.code !== 0) {
                throw new Error(`上传请求失败: ${resp.data.message}`);
            }

            const respData = resp.data.data;

            this.inBossKey = respData.in_boss_key;
            this.resourceId = respData.resource_id;
            this.uploadId = respData.upload_id;
            this.uploadUrls = respData.upload_urls;
            this.perSize = respData.per_size;
            this.clips = this.uploadUrls.length;

            if (this.perSize === undefined) {
                throw new Error('上传配置错误：分片大小未定义');
            }

            console.log(`[BcutASR] 上传配置成功，总大小: ${Math.floor(respData.size / 1024)}KB，分片数: ${this.clips}，每片大小: ${Math.floor(this.perSize / 1024)}KB`);

            await this.uploadPart(fileBinary);
            await this.commitUpload();
        } catch (error: unknown) {
            console.error(`[BcutASR] 上传失败:`, error);
            throw new Error(`音频上传失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }

    /**
     * 分片上传文件
     */
    async uploadPart(fileBinary: Buffer): Promise<void> {
        console.log(`[BcutASR] 开始分片上传，共 ${this.clips} 个分片`);

        for (let clip = 0; clip < (this.clips ?? 0); clip++) {
            const startRange = clip * (this.perSize ?? 0);
            const endRange = Math.min((clip + 1) * (this.perSize ?? 0), fileBinary.length);

            console.log(`[BcutASR] 上传分片 ${clip + 1}/${this.clips}: ${startRange}-${endRange} (${(endRange - startRange) / 1024}KB)`);

            try {
                const resp = await axios.put(
                    this.uploadUrls[clip],
                    fileBinary.slice(startRange, endRange),
                    {
                        headers: {
                            'Content-Type': 'application/octet-stream',
                        },
                        timeout: 60000, // 60秒超时
                        maxBodyLength: Infinity,
                        maxContentLength: Infinity
                    }
                );

                const etag = resp.headers['etag'] || resp.headers['ETag'];
                if (!etag) {
                    throw new Error(`分片 ${clip} 上传失败：未获取到ETag`);
                }

                this.etags.push(etag.replace(/"/g, '')); // 移除引号
                console.log(`[BcutASR] 分片 ${clip + 1} 上传成功: ${etag}`);
            } catch (error: unknown) {
                console.error(`[BcutASR] 分片 ${clip} 上传失败:`, error);
                throw new Error(`分片上传失败: ${error instanceof Error ? error.message : '未知错误'}`);
            }
        }

        console.log(`[BcutASR] 所有分片上传完成`);
    }

    /**
     * 提交上传完成
     */
    async commitUpload(): Promise<void> {
        console.log(`[BcutASR] 提交上传完成`);

        const data = {
            InBossKey: this.inBossKey,
            ResourceId: this.resourceId,
            Etags: this.etags.join(','),
            UploadId: this.uploadId,
            model_id: '8',
        };

        try {
            const resp = await axios.post(API_COMMIT_UPLOAD, data, {
                headers: this.headers,
                timeout: 30000
            });

            if (resp.data.code !== 0) {
                throw new Error(`提交上传失败: ${resp.data.message}`);
            }

            this.downloadUrl = resp.data.data.download_url;
            console.log(`[BcutASR] 上传提交成功，获取下载链接`);
        } catch (error: unknown) {
            console.error(`[BcutASR] 提交上传失败:`, error);
            throw new Error(`提交上传失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }

    /**
     * 创建ASR任务
     */
    async createTask(): Promise<void> {
        if (!this.downloadUrl) {
            throw new Error('下载链接不可用，需要先完成文件上传');
        }

        console.log(`[BcutASR] 创建ASR识别任务`);

        try {
            const response = await axios.post(
                API_CREATE_TASK,
                {
                    resource: this.downloadUrl,
                    model_id: '8'
                },
                {
                    headers: this.headers,
                    timeout: 30000
                }
            );

            if (response.data.code !== 0) {
                throw new Error(`创建任务失败: ${response.data.message}`);
            }

            this.taskId = response.data.data.task_id;
            console.log(`[BcutASR] ASR任务创建成功: ${this.taskId}`);
        } catch (error: unknown) {
            console.error(`[BcutASR] 创建任务失败:`, error);
            throw new Error(`创建ASR任务失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }

    /**
     * 获取识别结果
     */
    async getResult(): Promise<any> {
        if (!this.taskId) {
            throw new Error('任务ID不可用，需要先创建任务');
        }

        try {
            const response = await axios.get(API_QUERY_RESULT, {
                params: { model_id: 7, task_id: this.taskId },
                headers: this.headers,
                timeout: 30000
            });

            const resultData = response.data;

            if (resultData.code !== 0) {
                throw new Error(`查询结果失败: ${resultData.message}`);
            }

            // 处理结果数据
            if (!resultData.data.result) {
                return resultData;
            }

            try {
                if (typeof resultData.data.result === 'string') {
                    resultData.data.result = JSON.parse(resultData.data.result);
                }
            } catch (error) {
                console.error('[BcutASR] 解析结果数据失败:', error);
                resultData.data.result = {};
            }

            return resultData;
        } catch (error: unknown) {
            console.error(`[BcutASR] 获取结果失败:`, error);
            throw new Error(`获取识别结果失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }

    /**
     * 执行完整的ASR流程
     */
    async run(): Promise<ASRData | undefined> {
        console.log(`[BcutASR] 开始执行ASR识别流程`);

        // 检查缓存
        const cachedResult = this.getCachedResult();
        if (cachedResult) {
            console.log(`[BcutASR] 使用缓存结果`);
            return cachedResult;
        }

        try {
            // 执行ASR流程
            await this.upload();
            await this.createTask();

            // 轮询获取结果
            console.log(`[BcutASR] 开始轮询获取识别结果`);
            const maxAttempts = 300; // 最多等待5分钟

            for (let i = 0; i < maxAttempts; i++) {
                const result = await this.getResult();

                console.log(`[BcutASR] 第 ${i + 1} 次查询，状态: ${result.data.state}`);

                if (result.data.state === 4) { // 完成状态
                    console.log(`[BcutASR] 识别完成`);
                    const segments = this._makeSegments(result.data.result);
                    const asrData = new ASRData(segments, this.audioFile, this.language);

                    // 缓存结果
                    this.setCachedResult(asrData);

                    return asrData;
                } else if (result.data.state === 3) { // 失败状态
                    throw new Error(`ASR识别失败: ${result.data.result || '未知错误'}`);
                } else if (result.data.state === 1 || result.data.state === 2) { // 处理中
                    await new Promise((resolve) => setTimeout(resolve, 1000)); // 等待1秒
                } else {
                    console.warn(`[BcutASR] 未知状态: ${result.data.state}`);
                    await new Promise((resolve) => setTimeout(resolve, 2000)); // 等待2秒
                }
            }

            throw new Error('ASR任务在预期时间内未完成，请稍后重试');
        } catch (error) {
            console.error(`[BcutASR] ASR流程失败:`, error);
            throw error;
        }
    }

    /**
     * 将API响应转换为ASR数据段
     */
    _makeSegments(respData: any): ASRDataSeg[] {
        if (!respData || !respData.utterances || !Array.isArray(respData.utterances)) {
            console.warn(`[BcutASR] 响应数据格式异常:`, respData);
            return [];
        }

        return respData.utterances.map((u: {
            transcript: string;
            start_time: number;
            end_time: number;
        }) => {
            // BcutASR的参数顺序是 start, end, text
            return new ASRDataSeg(u.start_time, u.end_time, u.transcript);
        }).filter(seg => seg.text && seg.text.trim().length > 0); // 过滤空文本
    }

    /**
     * 取消正在进行的任务
     */
    async cancelTask(): Promise<void> {
        if (this.taskId) {
            console.log(`[BcutASR] 取消任务: ${this.taskId}`);
            // 哔哩哔哩API可能没有取消接口，这里只是清理本地状态
            this.taskId = undefined;
        }
    }

    /**
     * 获取任务进度（如果API支持）
     */
    async getProgress(): Promise<number> {
        if (!this.taskId) {
            return 0;
        }

        try {
            const result = await this.getResult();

            switch (result.data.state) {
                case 1: // 排队中
                    return 10;
                case 2: // 处理中
                    return 50;
                case 4: // 完成
                    return 100;
                case 3: // 失败
                    return -1;
                default:
                    return 0;
            }
        } catch (error) {
            console.error(`[BcutASR] 获取进度失败:`, error);
            return 0;
        }
    }
}