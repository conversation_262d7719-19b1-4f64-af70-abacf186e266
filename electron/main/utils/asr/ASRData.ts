import { ASRDataSeg } from './ASRDataSeg';

export class ASRData {
    segments: ASRDataSeg[];
    audioFile: string;
    language?: string;
    totalDuration?: number;

    constructor(segments: ASRDataSeg[], audioFile?: string, language?: string) {
        this.segments = segments;
        this.audioFile = audioFile || '';
        this.language = language;
        this.totalDuration = this.calculateTotalDuration();
    }

    /**
     * 检查是否有数据
     */
    hasData(): boolean {
        return this.segments.length > 0;
    }

    /**
     * 计算总时长
     */
    private calculateTotalDuration(): number {
        if (this.segments.length === 0) return 0;
        return Math.max(...this.segments.map(seg => seg.end));
    }

    /**
     * 转换为纯文本格式
     */
    toTxt(): string {
        return this.segments.map((seg) => seg.text).join('\n');
    }

    /**
     * 转换为SRT字幕格式
     */
    toSrt(): string {
        return this.segments
            .map((seg, index) => seg.toSrtLine(index + 1))
            .join('\n');
    }

    /**
     * 转换为VTT字幕格式
     */
    toVtt(): string {
        const header = 'WEBVTT\n\n';
        const content = this.segments
            .map((seg) => {
                const startTime = this.formatVttTime(seg.start);
                const endTime = this.formatVttTime(seg.end);
                return `${startTime} --> ${endTime}\n${seg.text}\n`;
            })
            .join('\n');
        return header + content;
    }

    /**
     * 格式化VTT时间格式
     */
    private formatVttTime(seconds: number): string {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        const milliseconds = Math.floor((seconds % 1) * 1000);

        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}.${milliseconds.toString().padStart(3, '0')}`;
    }

    /**
     * 按时间范围筛选片段
     */
    filterByTimeRange(startTime: number, endTime: number): ASRData {
        const filteredSegments = this.segments.filter(seg => 
            seg.start >= startTime && seg.end <= endTime
        );
        return new ASRData(filteredSegments, this.audioFile, this.language);
    }

    /**
     * 按关键词搜索片段
     */
    searchByKeyword(keyword: string): ASRDataSeg[] {
        const lowerKeyword = keyword.toLowerCase();
        return this.segments.filter(seg => 
            seg.text.toLowerCase().includes(lowerKeyword)
        );
    }

    /**
     * 合并相邻的短片段
     */
    mergeShortSegments(minDuration: number = 2): ASRData {
        if (this.segments.length === 0) return this;

        const mergedSegments: ASRDataSeg[] = [];
        let currentSegment = this.segments[0];

        for (let i = 1; i < this.segments.length; i++) {
            const nextSegment = this.segments[i];
            
            // 如果当前片段太短，尝试与下一个合并
            if (currentSegment.getDuration() < minDuration && 
                nextSegment.start - currentSegment.end < 1) { // 间隔小于1秒
                currentSegment = currentSegment.merge(nextSegment);
            } else {
                mergedSegments.push(currentSegment);
                currentSegment = nextSegment;
            }
        }
        
        mergedSegments.push(currentSegment);
        return new ASRData(mergedSegments, this.audioFile, this.language);
    }

    /**
     * 获取统计信息
     */
    getStatistics(): {
        segmentCount: number;
        totalDuration: number;
        averageSegmentLength: number;
        totalCharacters: number;
        wordsPerMinute: number;
    } {
        const totalCharacters = this.segments.reduce((sum, seg) => sum + seg.text.length, 0);
        const totalWords = this.segments.reduce((sum, seg) => sum + seg.text.split(/\s+/).length, 0);
        const durationInMinutes = (this.totalDuration || 0) / 60;
        
        return {
            segmentCount: this.segments.length,
            totalDuration: this.totalDuration || 0,
            averageSegmentLength: this.segments.length > 0 ? 
                this.segments.reduce((sum, seg) => sum + seg.getDuration(), 0) / this.segments.length : 0,
            totalCharacters,
            wordsPerMinute: durationInMinutes > 0 ? totalWords / durationInMinutes : 0
        };
    }

    /**
     * 导出为JSON格式
     */
    toJson(): string {
        return JSON.stringify({
            audioFile: this.audioFile,
            language: this.language,
            totalDuration: this.totalDuration,
            segments: this.segments.map(seg => ({
                start: seg.start,
                end: seg.end,
                text: seg.text,
                duration: seg.getDuration()
            })),
            statistics: this.getStatistics()
        }, null, 2);
    }

    /**
     * 从JSON格式导入
     */
    static fromJson(jsonStr: string): ASRData {
        const data = JSON.parse(jsonStr);
        const segments = data.segments.map((segData: any) => 
            new ASRDataSeg(segData.start, segData.end, segData.text)
        );
        return new ASRData(segments, data.audioFile, data.language);
    }
}