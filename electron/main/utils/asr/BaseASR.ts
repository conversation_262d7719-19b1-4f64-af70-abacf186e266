import fs from 'fs';
import path from 'path';
import { ASRData } from './ASRData';
import { ASRDataSeg } from './ASRDataSeg';

export abstract class BaseASR {
    audioFile: string;
    headers: { [key: string]: string };
    useCache: boolean;
    cache: { [key: string]: any } = {};
    cacheDir: string;

    constructor(audioFile: string, useCache: boolean = true) {
        this.audioFile = audioFile;
        this.useCache = useCache;
        this.headers = {
            'User-Agent': 'Bilibili/1.0.0 (https://www.bilibili.com)',
            'Content-Type': 'application/json',
        };
        
        // 设置缓存目录为应用程序目录下的cache文件夹
        this.cacheDir = path.join(__dirname, '../../../cache');
        
        // 确保缓存目录存在
        if (!fs.existsSync(this.cacheDir)) {
            fs.mkdirSync(this.cacheDir, { recursive: true });
        }
        
        // 加载缓存
        this.loadCache();
    }

    /**
     * 执行ASR识别的抽象方法
     */
    abstract run(): Promise<ASRData | undefined>;

    /**
     * 将响应数据转换为片段的抽象方法
     */
    abstract _makeSegments(respData: any): ASRDataSeg[];

    /**
     * 获取缓存键
     */
    protected getKey(): string {
        const fileName = path.basename(this.audioFile);
        const fileStats = fs.existsSync(this.audioFile) ? fs.statSync(this.audioFile) : null;
        const fileSize = fileStats ? fileStats.size : 0;
        const modifiedTime = fileStats ? fileStats.mtime.getTime() : 0;
        
        return `${this.constructor.name}-${fileName}-${fileSize}-${modifiedTime}`;
    }

    /**
     * 加载缓存
     */
    protected loadCache(): void {
        if (!this.useCache) return;
        
        try {
            const cacheFilePath = this.getCacheFilePath();
            if (fs.existsSync(cacheFilePath)) {
                const cacheContent = fs.readFileSync(cacheFilePath, 'utf-8');
                this.cache = JSON.parse(cacheContent);
                console.log(`[BaseASR] 加载缓存成功: ${Object.keys(this.cache).length} 个条目`);
            }
        } catch (error) {
            console.error('[BaseASR] 加载缓存失败:', error);
            this.cache = {};
        }
    }

    /**
     * 保存缓存
     */
    protected saveCache(): void {
        if (!this.useCache) return;
        
        try {
            const cacheFilePath = this.getCacheFilePath();
            fs.writeFileSync(cacheFilePath, JSON.stringify(this.cache, null, 2), 'utf-8');
            console.log(`[BaseASR] 保存缓存成功: ${Object.keys(this.cache).length} 个条目`);
        } catch (error) {
            console.error('[BaseASR] 保存缓存失败:', error);
        }
    }

    /**
     * 获取缓存文件路径
     */
    private getCacheFilePath(): string {
        return path.join(this.cacheDir, 'asr_cache.json');
    }

    /**
     * 检查缓存中是否存在结果
     */
    protected getCachedResult(): ASRData | undefined {
        const key = this.getKey();
        const cachedData = this.cache[key];
        
        if (cachedData) {
            console.log(`[BaseASR] 使用缓存结果: ${key}`);
            const segments = cachedData.segments.map((segData: any) => 
                new ASRDataSeg(segData.start, segData.end, segData.text)
            );
            return new ASRData(segments, this.audioFile, cachedData.language);
        }
        
        return undefined;
    }

    /**
     * 缓存结果
     */
    protected setCachedResult(result: ASRData): void {
        if (!this.useCache) return;
        
        const key = this.getKey();
        this.cache[key] = {
            audioFile: result.audioFile,
            language: result.language,
            totalDuration: result.totalDuration,
            segments: result.segments.map(seg => ({
                start: seg.start,
                end: seg.end,
                text: seg.text
            })),
            timestamp: Date.now()
        };
        
        this.saveCache();
        console.log(`[BaseASR] 缓存结果: ${key}`);
    }

    /**
     * 清理过期缓存
     */
    clearExpiredCache(maxAgeMs: number = 30 * 24 * 60 * 60 * 1000): void { // 默认30天
        const now = Date.now();
        let cleanedCount = 0;
        
        for (const key in this.cache) {
            const cachedItem = this.cache[key];
            if (cachedItem.timestamp && (now - cachedItem.timestamp) > maxAgeMs) {
                delete this.cache[key];
                cleanedCount++;
            }
        }
        
        if (cleanedCount > 0) {
            this.saveCache();
            console.log(`[BaseASR] 清理过期缓存: ${cleanedCount} 个条目`);
        }
    }

    /**
     * 获取缓存统计信息
     */
    getCacheStats(): {
        totalEntries: number;
        cacheSize: number;
        oldestEntry?: number;
        newestEntry?: number;
    } {
        const entries = Object.values(this.cache);
        const timestamps = entries
            .map((entry: any) => entry.timestamp)
            .filter(t => t !== undefined);
        
        return {
            totalEntries: Object.keys(this.cache).length,
            cacheSize: JSON.stringify(this.cache).length,
            oldestEntry: timestamps.length > 0 ? Math.min(...timestamps) : undefined,
            newestEntry: timestamps.length > 0 ? Math.max(...timestamps) : undefined
        };
    }

    /**
     * 验证音频文件
     */
    protected validateAudioFile(): boolean {
        if (!fs.existsSync(this.audioFile)) {
            throw new Error(`音频文件不存在: ${this.audioFile}`);
        }
        
        const ext = path.extname(this.audioFile).toLowerCase();
        const supportedFormats = ['.mp3', '.wav', '.m4a', '.aac', '.flac', '.ogg'];
        
        if (!supportedFormats.includes(ext)) {
            throw new Error(`不支持的音频格式: ${ext}。支持的格式: ${supportedFormats.join(', ')}`);
        }
        
        const stats = fs.statSync(this.audioFile);
        const maxSize = 100 * 1024 * 1024; // 100MB
        
        if (stats.size > maxSize) {
            throw new Error(`音频文件过大: ${(stats.size / 1024 / 1024).toFixed(2)}MB，最大支持 ${maxSize / 1024 / 1024}MB`);
        }
        
        return true;
    }

    /**
     * 获取音频文件信息
     */
    getAudioFileInfo(): {
        fileName: string;
        filePath: string;
        fileSize: number;
        format: string;
        lastModified: Date;
    } {
        if (!fs.existsSync(this.audioFile)) {
            throw new Error(`音频文件不存在: ${this.audioFile}`);
        }
        
        const stats = fs.statSync(this.audioFile);
        
        return {
            fileName: path.basename(this.audioFile),
            filePath: this.audioFile,
            fileSize: stats.size,
            format: path.extname(this.audioFile).slice(1).toLowerCase(),
            lastModified: stats.mtime
        };
    }
}