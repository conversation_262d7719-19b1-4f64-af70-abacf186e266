import moment from "moment";
import * as fs from "fs";
import * as util from "util";


interface WdjProxyConfig {
    app_key: string
}


export class WdjProxy {

    private config: WdjProxyConfig

    constructor(config?: WdjProxyConfig) {
        this.config = config ? config : { app_key: '' }
    }

    async getIp() {
        let proxy = ''
        let ip;
        let date = moment()
        if (fs.existsSync('proxy.json')) {
            ip = JSON.parse(String(fs.readFileSync('proxy.json')))
            const end = moment(ip.expire_time)
            if (date.valueOf() > end.valueOf()) {
                ip = undefined
            }
        }
        if (!ip) {
            ip = await this.callRemoteIp()
            fs.writeFileSync('proxy.json', JSON.stringify(ip))
        }
        proxy = util.format('http://%s:%s', ip.ip, ip.port)
        // // logger.s('代理ip为：%s', proxy)
        return proxy

    }

    private callRemoteIp(): Promise<any> {
        const url = `https://h.wandouip.com/get/ip-list?app_key=${this.config.app_key}&pack=0&num=20&xy=1&type=2&lb=\\r\\n&mr=1&`
        return new Promise(resolve => {
            // const j = request.jar()
            // request.defaults({ jar: j }).get(url, (err: any, response: Response, body: any) => {
            //     if (err) resolve(undefined)
            //     let result = JSON.parse(body)
            //     if (result.code == 200) {
            //         const ips = result.data
            //         // logger.s('ips:%s', JSON.stringify(result.data))
            //         let iMin = ips.shift();
            //         ips.forEach((ip: any) => {
            //             iMin = moment(ip.expire_time) > moment(iMin.expire_time) ? ip : iMin;
            //         });
            //         // logger.s('最晚过期的ip为：%s', JSON.stringify(iMin))
            //         resolve(iMin)
            //     } else {
            //         // logger.s('获取ip失败！')
            //         resolve(undefined)
            //     }
            // })

        })
    }

}
