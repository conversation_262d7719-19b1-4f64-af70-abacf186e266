/**
 * 将中文数字表示转换为实际数字
 * @param value 需要转换的数值字符串
 * @returns 转换后的数字
 */
export function convertChineseNumberToNumeric(value: string | number): number {
  // 如果已经是数字，直接返回
  if (typeof value === 'number') return value;
  
  // 如果是空或非字符串，返回0
  if (!value || typeof value !== 'string') return 0;

  // 去除空格
  value = value.trim();

  // 处理特殊情况
  if (value === '0' || value === '') return 0;

  // 中文数字映射
  const chineseNumberMap: { [key: string]: number } = {
    '零': 0, '一': 1, '二': 2, '三': 3, '四': 4, 
    '五': 5, '六': 6, '七': 7, '八': 8, '九': 9
  };

  // 处理万、亿等单位
  const unitMap: { [key: string]: number } = {
    '万': 10000,
    '亿': 100000000
  };

  // 处理带有单位的数字字符串
  const matchResult = value.match(/^(\d+(?:\.\d+)?|\d*(?:\.\d+)?)([万亿])?$/);
  if (matchResult) {
    const numericPart = parseFloat(matchResult[1]);
    const unit = matchResult[2];

    if (unit) {
      return numericPart * unitMap[unit];
    }
    return numericPart;
  }

  // 处理中文数字
  const chineseMatchResult = value.match(/^(\d+)?(万|亿)?$/);
  if (chineseMatchResult) {
    const numericPart = chineseMatchResult[1] ? parseInt(chineseMatchResult[1]) : 0;
    const unit = chineseMatchResult[2];

    if (unit) {
      return numericPart * unitMap[unit];
    }
    return numericPart;
  }

  // 尝试直接解析
  try {
    const parsedValue = parseInt(value, 10);
    if (!isNaN(parsedValue)) return parsedValue;
  } catch {}

  // 如果无法转换，返回0
  return 0;
}

/**
 * 安全地将交互数据转换为数字
 * @param value 需要转换的值
 * @returns 转换后的数字，转换失败返回0
 */
export function safeParseInteractCount(value: string | number | undefined): number {
  if (value === undefined) return 0;
  return convertChineseNumberToNumeric(value);
} 