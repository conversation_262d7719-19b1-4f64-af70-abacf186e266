export class Ut {

    /**
     * 异步延迟
     * @param start
     * @param end
     */
    static random(start: number, end: number) {
        if (end > start) {
            return Math.floor(Math.random() * (end - start)) + start
        } else {
            return start
        }
    }
    /**
     * 异步延迟
     * @param {number} time 延迟的时间,单位毫秒
     */
    static sleep(time = 0): Promise<void> {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve();
            }, time);
        })
    }
}

