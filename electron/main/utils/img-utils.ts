import axios from "axios";
import fs, { createWriteStream } from "fs";
import path from "path";
import sharp from "sharp";

const img_cdns = [
  "https://sns-img-qc.xhscdn.com",
  "https://sns-img-hw.xhscdn.com",
  "https://sns-img-bd.xhscdn.com",
  "https://sns-img-qn.xhscdn.com",
];

const video_cdns = [
  // "https://sns-video-qc.xhscdn.com",
  "https://sns-video-hw.xhscdn.com",
  "https://sns-video-bd.xhscdn.com",
  // "https://sns-video-qn.xhscdn.com",
];

function getRandomItem(array) {
  // random array index
  const index = Math.floor(Math.random() * array.length);
  return array[index];
}

export function getImgUrlByTraceId(traceId, format = "png") {
  return `${getRandomItem(img_cdns)}/${traceId}?imageView2/format/${format}`;
}

export function getImgUrlByUrl(url, format = "jpg") {
  const traceId = getTraceId(url);
  if (traceId && !url.includes("notes_pre_post")) {
    return `${getRandomItem(
      img_cdns
    )}/${traceId}?imageView2/format/${format}/v3`;
  } else if (url.includes("notes_pre_post")) {
    return `${getRandomItem(
      img_cdns
    )}/notes_pre_post/${traceId}?imageView2/format/${format}/v3`;
  }
  return url;
}


export function getTraceId(imgUrl) {
  return imgUrl.split("/").slice(5).join("/").split("!")[0];
}

export function getImgsUrlFromNote(note) {
  const imgs = note.image_list;
  if (!imgs.length) {
    return [];
  }
  return imgs.map((img) => {
    console.log(`img live photo: ${img.live_photo}`);

    if (!img.live_photo) {
      return getImgUrlByTraceId(getTraceId(img.url_default));
    } else {
      return img.url_default
    }
  }
  );
}

export function getVideoUrlByKey(key) {
  return `${getRandomItem(video_cdns)}/${key}`;
}

export function getSearchId() {
  const e = BigInt(Date.now()) << BigInt(64);
  const t = BigInt(Math.floor(Math.random() * 2147483646));
  return (e + t).toString(36);
}

export async function downloadFile(url, filepath, filename) {
  try {
    // 使用 axios 获取图片的响应流
    const response = await axios({
      method: "GET",
      url: url,
      responseType: "stream",
    });

    // 确保目标文件夹存在
    if (!fs.existsSync(filepath)) {
      fs.mkdirSync(filepath, { recursive: true });
    }

    // 创建写入流
    const writer = fs.createWriteStream(path.resolve(filepath, filename));

    // 管道图片数据到文件
    response.data.pipe(writer);

    return new Promise<void>((resolve, reject) => {
      writer.on("finish", resolve);
      writer.on("error", reject);
    });
  } catch (error) {
    console.error("下载图片时发生错误:", error);
  }
}

export async function downloadAndConvert(
  url: string,
  filepath: string,
  filename: string,
  format: string = 'jpg',
  quality: number = 85,
  maxWidth?: number
) {
  // 确保目标文件夹存在
  if (!fs.existsSync(filepath)) {
    fs.mkdirSync(filepath, { recursive: true });
  }

  try {
    // 修改文件扩展名以匹配转换后的格式
    const fileNameWithoutExt = filename.split('.')[0];
    const finalFileName = `${fileNameWithoutExt}.${format}`;
    const filePath = path.resolve(filepath, finalFileName);

    // 对于远程URL，继续使用原来的下载和转换逻辑
    const response = await axios({
      method: 'GET',
      url: url,
      responseType: 'arraybuffer',
    });

    const imageBuffer = Buffer.from(response.data);

    // 使用sharp进行格式转换
    let transformer = sharp(imageBuffer);

    // 如果指定了最大宽度，调整图片大小
    if (maxWidth && maxWidth > 0) {
      transformer = transformer.resize({
        width: maxWidth,
        withoutEnlargement: true // 避免放大小图片
      });
    }

    // 根据目标格式设置输出选项
    switch (format.toLowerCase()) {
      case 'jpg':
      case 'jpeg':
        await transformer.jpeg({ quality }).toFile(filePath);
        break;
      case 'png':
        await transformer.png({ quality }).toFile(filePath);
        break;
      case 'webp':
        await transformer.webp({ quality }).toFile(filePath);
        break;
      case 'avif':
        await transformer.avif({ quality }).toFile(filePath);
        break;
      default:
        // 默认转为jpg
        await transformer.jpeg({ quality }).toFile(filePath);
    }

    return filePath; // 返回保存的文件路径
  } catch (error: unknown) {
    // 给error添加类型注解
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(`下载或转换图片失败: ${errorMessage}`);
    throw error;
  }
}

/**
 * 转换本地图片文件到指定格式
 * @param filePath 本地文件路径（可能是file://开头或普通路径）
 * @param outputDir 输出文件夹
 * @param format 目标格式 (jpg, png, webp, avif)
 * @param quality 图片质量(1-100)，默认85
 * @param maxWidth 最大宽度，如果指定则调整图片大小
 * @returns 转换后的文件路径
 */
export async function convertLocalFile(
  filePath: string,
  outputDir: string,
  format: string = 'jpg',
  quality: number = 85,
  maxWidth?: number
): Promise<string> {
  try {
    // 处理file://协议的路径
    let normalizedPath = filePath;
    if (filePath.startsWith('file://')) {
      normalizedPath = filePath.replace(/^file:\/\//, '');
      // 在macOS上，file:///Users/<USER>/Users/<USER>
      // 在Windows上，file:///C:/... 会变成 C:/...
    }

    // 确保文件存在
    if (!fs.existsSync(normalizedPath)) {
      throw new Error(`文件不存在: ${normalizedPath}`);
    }

    // 读取文件并转换格式
    let transformer = sharp(normalizedPath);

    // 如果指定了最大宽度，调整图片大小
    if (maxWidth && maxWidth > 0) {
      transformer = transformer.resize({
        width: maxWidth,
        withoutEnlargement: true // 避免放大小图片
      });
    }

    // 去掉原有扩展名 并添加新的扩展名
    const outputPath = path.join(outputDir, path.basename(normalizedPath, path.extname(normalizedPath)) + `_${quality}` + `.${format}`);

    // 根据目标格式设置输出选项
    switch (format.toLowerCase()) {
      case 'jpg':
      case 'jpeg':
        await transformer.jpeg({ quality }).toFile(outputPath);
        break;
      case 'png':
        await transformer.png({ quality }).toFile(outputPath);
        break;
      case 'webp':
        await transformer.webp({ quality }).toFile(outputPath);
        break;
      case 'avif':
        await transformer.avif({ quality }).toFile(outputPath);
        break;
      default:
        // 默认转为jpg
        await transformer.jpeg({ quality }).toFile(outputPath);
    }

    return outputPath;
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(`本地图片转换失败: ${errorMessage}`);
    throw error;
  }
}