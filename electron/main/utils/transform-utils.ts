/**
 * 数据转换工具类，负责API响应数据到应用模型的转换
 */
import { XhsImage, XhsImageStream, XhsNote, XhsUser, XhsUserTag, XhsVideo } from "../api/models/note";
import { XhsApiImageData, XhsNoteApiResponse, XhsUserApiResponse } from "../api/models/api-types";
import { camelToSnake } from "./object-utils";
import { safeParseInteractCount } from "./number-utils";
import { getVideoUrlByKey } from "./img-utils";
import { XhsWebNote } from "../api/models/web-types";

/**
 * 转换API数据到小红书用户模型
 * 
 * @param userId 用户ID
 * @param jsonData API返回的用户数据
 * @returns 转换后的XhsUser对象
 */
export function transformDataToXhsUser(userId: string, jsonData: XhsUserApiResponse): XhsUser {
    // 转换为蛇形命名风格的键名
    const data = camelToSnake(jsonData) as any;
    const xhsUser: XhsUser = {};
    const xhsUserTags: XhsUserTag[] = [];

    // 基本信息
    if (data.basic_info) {
        xhsUser.user_id = userId;
        xhsUser.ip_location = data.basic_info.ip_location;
        xhsUser.desc = data.basic_info.desc;
        xhsUser.avatar = data.basic_info.images; // 或 imageb，根据需要选择
        xhsUser.nickname = data.basic_info.nickname;
        xhsUser.red_id = data.basic_info.red_id;
        xhsUser.gender = data.basic_info.gender;
    }

    // 互动信息
    if (data.interactions) {
        data.interactions.forEach((interaction: any) => {
            switch (interaction.type) {
                case "follows":
                    xhsUser.follows = parseInt(interaction.count, 10);
                    break;
                case "fans":
                    xhsUser.fans = parseInt(interaction.count, 10);
                    break;
                case "interaction":
                    xhsUser.interaction = parseInt(interaction.count, 10);
                    break;
            }
        });
    }

    // 标签信息
    if (data.tags) {
        data.tags.forEach((tag: any) => {
            xhsUserTags.push({
                name: tag.name,
                tagType: tag.tagType,
                icon: tag.icon,
            });
        });
    }

    xhsUser.tags = xhsUserTags;
    return xhsUser;
}

/**
 * 转换API数据到小红书笔记模型
 * 
 * @param jsonData API返回的笔记数据
 * @returns 转换后的XhsNote对象
 */
export function transformDataToXhsNote(jsonData: XhsNoteApiResponse): XhsNote {
    // 获取笔记卡片数据
    const noteData = jsonData.items[0].note_card;

    // 构建XhsNote对象
    const xhsNote: XhsNote = {
        id: noteData.note_id,
        collected: noteData.interact_info.collected,
        collected_count: safeParseInteractCount(noteData.interact_info.collected_count),
        comments_count: safeParseInteractCount(noteData.interact_info.comment_count),
        desc: noteData.desc,
        images_list: noteData.image_list.map((img) => transformImageList(img)),
        last_update_time: noteData.last_update_time,
        liked_count: safeParseInteractCount(noteData.interact_info.liked_count),
        shared_count: safeParseInteractCount(noteData.interact_info.share_count),
        time: noteData.time,
        title: noteData.title,
        type: noteData.type,
        user: transformUser(noteData.user),
        video: transformVideo(noteData),
    };

    return xhsNote;
}



/**
 * 转换图片数据
 * 
 * @param imageData API返回的图片数据
 * @returns 转换后的XhsImage对象
 */
export function transformImageList(imageData: XhsApiImageData): XhsImage {
    // 使用url_default作为原始图片地址
    const originalUrl = imageData.url_default;

    // 处理stream对象，确保符合XhsImageStream类型
    let stream: XhsImageStream | undefined = undefined;
    if (imageData.stream) {
        stream = {
            h264: imageData.stream.h264 || [],
            h265: imageData.stream.h265 || [],
            h266: [], // 添加必需的h266属性
            av1: imageData.stream.av1 || []
        };
    }

    return {
        live_photo: imageData.live_photo,
        file_id: imageData.file_id,
        height: imageData.height,
        width: imageData.width,
        original: originalUrl,
        trace_id: imageData.trace_id,
        info_list: imageData.info_list.map((info) => ({
            image_scene: info.image_scene,
            url: info.url,
        })),
        url_default: imageData.url_default,
        url_pre: imageData.url_pre,
        stream: stream
    };
}

/**
 * 转换用户数据
 * 
 * @param userData API返回的用户数据
 * @returns 转换后的XhsUser对象
 */
export function transformUser(userData: { user_id: string; avatar: string; nickname: string }): XhsUser {
    return {
        user_id: userData.user_id,
        avatar: userData.avatar,
        nickname: userData.nickname,
    };
}

/**
 * 转换视频数据
 * 
 * @param noteData 包含视频信息的笔记数据
 * @returns 转换后的XhsVideo对象，如果没有视频则返回undefined
 */
export function transformVideo(noteData: any): XhsVideo | undefined {
    if (!noteData.video) {
        return undefined;
    }

    const videoData = noteData.video;
    const imageData = noteData.image_list.map((img: XhsApiImageData) => transformImageList(img));

    return {
        id: String(videoData.media.video_id),
        duration: videoData.capa.duration,
        first_frame: imageData[0].url_default,
        thumbnail: imageData[0].url_pre,
        origin_video_key: videoData.consumer.origin_video_key,
        url: getVideoUrlByKey(videoData.consumer.origin_video_key),
    };
}

/**
 * 获取第一个可用的视频URL
 * 
 * @param media 包含视频流信息的媒体对象
 * @returns 视频URL或undefined
 */
export function getFirstAvailableVideoUrl(media: any): string | undefined {
    if (!media || !media.stream) {
        return undefined;
    }

    // 按顺序检查 h264, h265, av1 数组
    const streamTypes = ["h264", "h265", "av1"];

    for (const type of streamTypes) {
        const streamArray = media.stream[type];
        if (streamArray && streamArray.length > 0) {
            // 如果找到非空数组，则返回该数组第一个元素的 master_url
            return streamArray[0].master_url;
        }
    }

    // 如果所有类型都为空，则返回 undefined
    return undefined;
}


// 转换WebNote笔记数据为XhsNote
export function transformWebNoteToXhsNote(webNote: XhsWebNote): XhsNote {
    const xhsNote: XhsNote = {
        id: webNote.id,
        title: webNote.noteCard.displayTitle,
        desc: "",
        images_list: [{
            file_id: webNote.noteCard.cover.fileId,
            height: webNote.noteCard.cover.height,
            width: webNote.noteCard.cover.width,
            original: webNote.noteCard.cover.urlDefault,
            trace_id: webNote.noteCard.cover.traceId,
            url_default: webNote.noteCard.cover.urlDefault,
            url_pre: webNote.noteCard.cover.urlPre,
            stream: undefined,
            info_list: webNote.noteCard.cover.infoList.map((info) => ({
                image_scene: info.imageScene,
                url: info.url,
            })),
        }],
        last_update_time: 0,
        liked_count: safeParseInteractCount(webNote.noteCard.interactInfo.likedCount),
        shared_count: 0,
        time: new Date().getTime(),
        type: webNote.noteCard.type,
        user: {
            user_id: webNote.noteCard.user.userId,
            avatar: webNote.noteCard.user.avatar,
            nickname: webNote.noteCard.user.nickname,
        },
        video: undefined,
        collected: false,
        collected_count: 0,
        comments_count: 0,
        xsecToken: webNote.xsecToken,
    };
    return xhsNote;
}