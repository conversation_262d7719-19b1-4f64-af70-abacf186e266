/**
 * 对象工具类，提供对象处理相关的工具方法
 */

/**
 * 将对象中的键名从驼峰命名转换为蛇形命名
 * @param obj 需要转换的对象
 * @param visited 已访问对象的集合，用于检测循环引用
 * @returns 转换后的对象
 */
export function camelToSnake(obj: any, visited = new WeakSet()): any {
    // 处理基本类型、null或undefined
    if (obj === null || obj === undefined || typeof obj !== 'object') {
        return obj;
    }

    // 处理循环引用
    if (visited.has(obj)) {
        return obj; // 或者返回一个标记值表示循环引用
    }

    // 添加到已访问集合中
    visited.add(obj);

    // 处理数组
    if (Array.isArray(obj)) {
        return obj.map(v => camelToSnake(v, visited));
    }

    // 处理特殊对象类型
    if (obj instanceof Date || obj instanceof RegExp || obj instanceof Error) {
        return obj;
    }

    // 处理普通对象
    const result: any = {};
    for (const key of Object.keys(obj)) {
        let snakeKey = key;

        // 首字母特殊处理，避免前导下划线
        if (/^[A-Z]/.test(key)) {
            snakeKey = key.charAt(0).toLowerCase() + key.slice(1);
        }

        // 转换其余部分
        snakeKey = snakeKey.replace(/([A-Z])/g, "_$1").toLowerCase();

        result[snakeKey] = camelToSnake(obj[key], visited);
    }

    return result;
}

/**
 * 将对象中的键名从蛇形命名转换为驼峰命名
 * @param obj 需要转换的对象
 * @param visited 已访问对象的集合，用于检测循环引用
 * @returns 转换后的对象
 */
export function snakeToCamel(obj: any, visited = new WeakSet()): any {
    // 处理基本类型、null或undefined
    if (obj === null || obj === undefined || typeof obj !== 'object') {
        return obj;
    }

    // 处理循环引用
    if (visited.has(obj)) {
        return obj;
    }

    // 添加到已访问集合中
    visited.add(obj);

    // 处理数组
    if (Array.isArray(obj)) {
        return obj.map(v => snakeToCamel(v, visited));
    }

    // 处理特殊对象类型
    if (obj instanceof Date || obj instanceof RegExp || obj instanceof Error) {
        return obj;
    }

    // 处理普通对象
    const result: any = {};
    for (const key of Object.keys(obj)) {
        // 将蛇形命名转换为驼峰命名
        const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
        result[camelKey] = snakeToCamel(obj[key], visited);
    }

    return result;
} 