import Store from 'electron-store';
import path from 'path';
import { app } from 'electron';

// 文件路径清理函数
function sanitizePath(input: string): string {
  if (!input) return 'unknown';

  // 删除URL前缀
  let result = input;
  if (result.startsWith('http://') || result.startsWith('https://')) {
    // 提取URL中的主要ID部分，通常在path中第一个问号前
    const urlParts = result.split('?');
    const pathParts = urlParts[0].split('/');
    // 尝试获取最后一个路径部分作为ID
    result = pathParts[pathParts.length - 1] || result;
  }

  // 清理剩余的特殊字符
  return result
    .replace(/[<>:"/\\|?*\x00-\x1F]/g, '_') // 替换无效Windows文件名字符
    .replace(/[\u{1F300}-\u{1F9FF}]/gu, '') // 删除emoji
    .replace(/\s+/g, '_') // 替换空格为下划线
    .replace(/[^\x20-\x7E]/g, '') // 删除非ASCII字符
    .substring(0, 100); // 限制长度以避免路径过长错误
}

// 数据结构定义
export interface MonitorTaskConfig {
  id: string;
  name: string;
  type: 'note' | 'blogger';
  noteUrl?: string;
  bloggerId?: string;
  frequency: number; // 监控频率，单位分钟
  lastRunTime: number; // 上次运行时间戳
  status: 'running' | 'paused' | 'stopped';
  lastKnownNoteIds?: string[]; // 博主任务特有，存储上次已知的笔记ID列表
  data?: {
    // 根据类型存储不同的数据
    // blogger类型
    bloggerId?: string;
    nickname?: string;
    avatar?: string;
    desc?: string;
    gender?: string;
    fans?: number;
    follows?: number;
    interaction?: number;
    notes?: number;
    lastUserInfoUpdate?: number; // 上次更新用户信息的时间戳

    // note类型
    noteId?: string;
    coverUrl?: string;
    title?: string;
    authorAvatar?: string;
    authorName?: string;
    likeCount?: number;
    commentCount?: number;
    collectCount?: number;
    shareCount?: number;
  };
}

export interface NoteDataPoint {
  timestamp: number;
  likes: number;
  comments: number;
  forwards: number;
}

export interface BloggerNewNote {
  noteId: string;
  title: string;
  url: string;
  cover: string;
  type: string;
  createTime: string;
  likeCount: string;
  sticky: boolean;
  index: number;
  deleted?: boolean; // 标记笔记是否已被博主删除
  deletedTime?: string; // 标记删除时间
}

export interface IMonitorTask {
  toConfig(): MonitorTaskConfig;
  start(): Promise<void>;
  pause(): void;
  resume(): void;
  stop(): void;
}

// Electron-store 实例
const monitorTasksStore = new Store<Record<string, MonitorTaskConfig>>({
  name: 'monitor-tasks',
  cwd: path.join(app.getPath('userData'), 'monitor-data'),
  defaults: {}, // 确保默认值是空对象
});

// 存储方法
export class MonitorStore {
  /**
   * 获取所有监控任务配置
   */
  getAllMonitorTasks(): MonitorTaskConfig[] {
    return Object.values(monitorTasksStore.store);
  }

  /**
   * 保存或更新单个监控任务配置
   * @param task 监控任务配置
   */
  saveMonitorTask(task: MonitorTaskConfig): void {
    monitorTasksStore.set(task.id, task);
  }

  /**
   * 删除指定任务的配置
   * @param taskId 任务ID
   */
  deleteMonitorTaskConfig(taskId: string): void {
    monitorTasksStore.delete(taskId);
  }

  /**
   * 获取指定笔记的历史数据点
   * @param noteId 笔记ID
   */
  getNoteHistoryData(noteId: string): NoteDataPoint[] {
    try {
      console.log(`Getting history data for noteId: ${noteId}`);

      // Extract item ID if it's a URL
      let extractedId = noteId;
      if (noteId.includes('item/')) {
        const itemIdMatch = noteId.match(/item\/([^/?&]+)/);
        if (itemIdMatch && itemIdMatch[1]) {
          extractedId = itemIdMatch[1];
          console.log(`Extracted item ID from URL: ${extractedId}`);
        }
      }

      const safeNoteId = sanitizePath(extractedId);
      console.log(`Sanitized noteId for retrieval: ${safeNoteId}`);

      const noteDataStore = new Store<{ data: NoteDataPoint[] }>({
        name: `note-data-${safeNoteId}`,
        cwd: path.join(app.getPath('userData'), 'monitor-data'),
        defaults: { data: [] },
      });

      const data = noteDataStore.get('data', []);
      if (!Array.isArray(data)) {
        console.warn(`Retrieved data is not an array for noteId ${noteId}, returning empty array`);
        return [];
      }

      console.log(`Found ${data.length} data points for noteId ${noteId}`);
      return data;
    } catch (error) {
      console.error(`Failed to get history data for noteId ${noteId}:`, error);
      return [];
    }
  }

  /**
   * 添加笔记数据点
   * @param noteId 笔记ID
   * @param dataPoint 数据点
   */
  addNoteDataPoint(noteId: string, dataPoint: NoteDataPoint): void {
    try {
      console.log(`Adding data point for noteId: ${noteId}`);

      // Extract item ID if it's a URL
      let extractedId = noteId;
      if (noteId.includes('item/')) {
        const itemIdMatch = noteId.match(/item\/([^/?&]+)/);
        if (itemIdMatch && itemIdMatch[1]) {
          extractedId = itemIdMatch[1];
          console.log(`Extracted item ID from URL: ${extractedId}`);
        }
      }

      const safeNoteId = sanitizePath(extractedId);
      console.log(`Sanitized noteId: ${safeNoteId}`);

      const noteDataStore = new Store<{ data: NoteDataPoint[] }>({
        name: `note-data-${safeNoteId}`,
        cwd: path.join(app.getPath('userData'), 'monitor-data'),
        defaults: { data: [] },
      });

      // 确保 data 是一个数组
      const currentData = noteDataStore.get('data', []);
      if (!Array.isArray(currentData)) {
        console.warn(`Data for noteId ${noteId} is not an array, resetting...`);
        noteDataStore.set('data', []);
      }

      // 添加新数据点
      noteDataStore.set('data', [...(Array.isArray(currentData) ? currentData : []), dataPoint]);
      console.log(`Successfully added data point for noteId ${noteId}. Total data points: ${currentData.length + 1}`);
    } catch (error) {
      console.error(`Failed to add data point for noteId ${noteId}:`, error);
    }
  }

  /**
   * 删除指定笔记的历史数据
   * @param noteId 笔记ID
   */
  deleteNoteHistoryData(noteId: string): void {
    try {
      const safeNoteId = sanitizePath(noteId);
      const noteDataStore = new Store<{ data: NoteDataPoint[] }>({
        name: `note-data-${safeNoteId}`,
        cwd: path.join(app.getPath('userData'), 'monitor-data'),
        defaults: { data: [] },
      });
      noteDataStore.clear(); // 清空该笔记的所有历史数据
      noteDataStore.set('data', []); // 确保清空后数据为空数组
    } catch (error) {
      console.error(`Failed to delete history data for noteId ${noteId}:`, error);
    }
  }

  /**
   * 获取指定博主的新笔记列表
   * @param bloggerId 博主ID
   */
  getBloggerNewNotes(bloggerId: string): BloggerNewNote[] {
    const safeBloggerId = sanitizePath(bloggerId);
    const bloggerDataStore = new Store<{ data: BloggerNewNote[] }>({
      name: `blogger-data-${safeBloggerId}`,
      cwd: path.join(app.getPath('userData'), 'monitor-data'),
      defaults: { data: [] },
    });
    const data = bloggerDataStore.get('data', []);
    // 排序逻辑：未删除的笔记按index升序排列，已删除的笔记排在最后
    const sortedData = Array.isArray(data) ? data.sort((a, b) => {
      // 如果一个被删除，一个没被删除，没被删除的排前面
      if (a.deleted && !b.deleted) return 1;
      if (!a.deleted && b.deleted) return -1;
      
      // 如果都被删除或都没被删除，按index排序
      return a.index - b.index;
    }) : [];
    return sortedData;
  }

  /**
   * 更新指定博主的指定笔记数据
   * @param bloggerId 博主ID
   * @param noteId 笔记ID
   * @param newNote 新笔记数据
   */
  updateBloggerNewNote(bloggerId: string, noteId: string, newNote: Partial<BloggerNewNote>): void {
    const safeBloggerId = sanitizePath(bloggerId);
    const bloggerDataStore = new Store<{ data: BloggerNewNote[] }>({
      name: `blogger-data-${safeBloggerId}`,
      cwd: path.join(app.getPath('userData'), 'monitor-data'),
      defaults: { data: [] },
    });
    const currentNotes = bloggerDataStore.get('data', []);
    if (!Array.isArray(currentNotes)) {
      console.warn(`Data for bloggerId ${bloggerId} is not an array, resetting...`);
      bloggerDataStore.set('data', []);
    }
    const existingNote = currentNotes.find(n => n.noteId === noteId);
    if (existingNote) {
      existingNote.likeCount = newNote.likeCount || '0';
      existingNote.title = newNote.title || '';
      existingNote.cover = newNote.cover || '';
      existingNote.sticky = newNote.sticky || false;
      existingNote.index = newNote.index || 0;
      // 如果笔记被重新找到（之前标记为删除），清除删除标记
      if (existingNote.deleted) {
        existingNote.deleted = false;
        existingNote.deletedTime = undefined;
      }
    }
    bloggerDataStore.set('data', currentNotes);
  }

  /**
   * 标记指定博主的指定笔记为已删除
   * @param bloggerId 博主ID
   * @param noteId 笔记ID
   */
  markBloggerNoteAsDeleted(bloggerId: string, noteId: string): void {
    const safeBloggerId = sanitizePath(bloggerId);
    const bloggerDataStore = new Store<{ data: BloggerNewNote[] }>({
      name: `blogger-data-${safeBloggerId}`,
      cwd: path.join(app.getPath('userData'), 'monitor-data'),
      defaults: { data: [] },
    });
    const currentNotes = bloggerDataStore.get('data', []);
    if (!Array.isArray(currentNotes)) {
      console.warn(`Data for bloggerId ${bloggerId} is not an array, resetting...`);
      bloggerDataStore.set('data', []);
      return;
    }
    const existingNote = currentNotes.find(n => n.noteId === noteId);
    if (existingNote && !existingNote.deleted) {
      existingNote.deleted = true;
      existingNote.deletedTime = new Date().toISOString();
      bloggerDataStore.set('data', currentNotes);
      console.log(`Marked note ${noteId} as deleted for blogger ${bloggerId}`);
    }
  }

  /**
   * 添加博主新笔记
   * @param bloggerId 博主ID
   * @param newNote 新笔记数据
   */
  addBloggerNewNote(bloggerId: string, newNote: BloggerNewNote): void {
    try {
      const safeBloggerId = sanitizePath(bloggerId);
      const bloggerDataStore = new Store<{ data: BloggerNewNote[] }>({
        name: `blogger-data-${safeBloggerId}`,
        cwd: path.join(app.getPath('userData'), 'monitor-data'),
        defaults: { data: [] },
      });

      // 确保 data 是一个数组
      const currentNotes = bloggerDataStore.get('data', []);
      if (!Array.isArray(currentNotes)) {
        console.warn(`Data for bloggerId ${bloggerId} is not an array, resetting...`);
        bloggerDataStore.set('data', []);
      }

      // 添加新笔记
      bloggerDataStore.set('data', [...(Array.isArray(currentNotes) ? currentNotes : []), newNote]);
      console.log(`Successfully added new note for bloggerId ${bloggerId}`);
    } catch (error) {
      console.error(`Failed to add new note for bloggerId ${bloggerId}:`, error);
    }
  }

  /**
   * 删除指定博主的新笔记数据
   * @param bloggerId 博主ID
   */
  deleteBloggerNewNotes(bloggerId: string): void {
    try {
      const safeBloggerId = sanitizePath(bloggerId);
      const bloggerDataStore = new Store<{ data: BloggerNewNote[] }>({
        name: `blogger-data-${safeBloggerId}`,
        cwd: path.join(app.getPath('userData'), 'monitor-data'),
        defaults: { data: [] },
      });
      bloggerDataStore.clear(); // 清空该博主的所有新笔记数据
      bloggerDataStore.set('data', []); // 确保清空后数据为空数组
    } catch (error) {
      console.error(`Failed to delete blogger notes for bloggerId ${bloggerId}:`, error);
    }
  }
}

export const monitorStore = new MonitorStore();