import { ipcMain } from 'electron';
import * as fs from 'fs';
import * as path from 'path';
import { XhsService } from '../services/xhs-worker';
import { downloadAndConvert, downloadFile, getImgUrlByTraceId, getImgUrlByUrl, getTraceId } from '../utils/img-utils';

export function registerXhsHandlers(
    ipcMain: Electron.IpcMain,
    xhsService: XhsService,
    fs: typeof import('fs'),
    path: typeof import('path'),
    downloadAndConvert: (url: string, dir: string, filename: string, format?: string, quality?: number, maxWidth?: number) => Promise<string>,
    downloadFile: (url: string, dir: string, filename: string) => Promise<void>,
    getImgUrlByUrl: (url: string, format?: string) => string
): void {

    ipcMain.handle('xhs-note-detail', async (event, input) => {
        console.log(`[IPC] 获取笔记详情: ${input}`);
        return await xhsService.getNoteInfo(input);
    });

    ipcMain.handle('xhs-note-imgs', async (event, item) => {
        console.log(`[IPC] 读取笔记图片: ${item.text}`);
        return await xhsService.readNoteImgs(item.text);
    });

    ipcMain.handle('xhs-note-imgs-save', async (event, item) => {
        console.log(`[IPC] 保存笔记图片到: ${item.dir}`);
        if (item.imgs) {
            if (!fs.existsSync(item.dir)) {
                fs.mkdirSync(item.dir, { recursive: true });
            }
            for (const img of item.imgs) {
                const index = item.imgs.indexOf(img);
                let imgUrl = getImgUrlByTraceId(getTraceId(img.url_default), "jpg");
                if (imgUrl.includes("webp")) {
                    console.log("下载webp格式的图片，需要转换为jpg");
                    await downloadAndConvert(imgUrl, item.dir, `${index + 1}.jpg`);
                } else {
                    console.log("下载jpg格式的图片");
                    await downloadFile(imgUrl, item.dir, `${index + 1}.jpg`);
                }
                if (img.live_photo) {
                    const videoUrl = img.stream.h264[0].master_url;
                    console.log(videoUrl);
                    await downloadFile(videoUrl, item.dir, `${index + 1}.mp4`);
                }
            }
            fs.writeFileSync(`${item.dir}/note.txt`, `${item.title ?? ''}\n\n${item.desc ?? ''}`);
        }
    });

    ipcMain.handle('xhs-notes-save', async (event, item) => {
        console.log(`[IPC] 批量保存笔记到: ${item.dir}`);
        if (item.notes) {
            if (!fs.existsSync(item.dir)) {
                fs.mkdirSync(item.dir, { recursive: true });
            }
            for (const note of item.notes) {
                const title = note.note_card.display_title ?? '';
                const desc = note.note_card.desc ?? '';
                const content = `${title}\n\n${desc}`;
                const dir = `${item.dir}/${title}`;
                if (!fs.existsSync(dir)) {
                    fs.mkdirSync(dir, { recursive: true });
                }
                if (note.note_card.type == 'video') {
                    downloadFile(note.note_card.video_url, dir, `note.mp4`);
                } else if (note.note_card.type == 'normal') {
                    const imgList = note.note_card.image_list;
                    for (const img of imgList) {
                        const imgIndex = imgList.indexOf(img);
                        console.log(`img:【${JSON.stringify(img)}】`);
                        const image = img.info_list[0]
                        if (image) {
                            let original = image.url;
                            if (original.includes("webp")) {
                                console.log("下载webp格式的图片，需要转换为jpg");
                                original = getImgUrlByUrl(original);
                                await downloadAndConvert(original, dir, `${imgIndex + 1}.jpg`);
                            } else {
                                console.log("下载jpg格式的图片");
                                original = getImgUrlByUrl(original);
                                await downloadFile(original, dir, `${imgIndex + 1}.jpg`);
                            }
                        }

                        if (img.live_photo) {
                            const videoUrl = img.stream.h264[0].master_url;
                            console.log(videoUrl);
                            await downloadFile(videoUrl, item.dir, `${imgIndex + 1}.mp4`);
                        }
                        await new Promise(resolve => setTimeout(resolve, 2000));
                    }
                }

                fs.writeFileSync(`${dir}/note.txt`, content);
                await new Promise(resolve => setTimeout(resolve, 5000));
            }
        }
    });

    ipcMain.handle('xhs-note-video-save', (event, item) => {
        console.log(`[IPC] 保存笔记视频到: ${item.dir}`);
        if (item.video) {
            if (!fs.existsSync(item.dir)) {
                fs.mkdirSync(item.dir, { recursive: true });
            }
            downloadFile(item.video.url, item.dir, `note.mp4`);
            fs.writeFileSync(`${item.dir}/note.txt`, `${item.title ?? ''}\n\n${item.desc ?? ''}`);
        }
    });

    ipcMain.handle('xhs-note-check', async (event, item) => {
        console.log(`[IPC] 检测笔记收录情况: ${item.nodeId}, ${item.red_id}`);
        return await xhsService.checkNote(item.nodeId, item.red_id);
    });

    ipcMain.handle('xhs-notes-fetch', async (event, item) => {
        console.log(`[IPC] 批量获取笔记: ${item.keyword}, page ${item.page}, type ${item.type}`);
        return await xhsService.getNotesByKeyword(item);
    });

    ipcMain.handle('xhs-note-check-word', async (event, item) => {
        console.log(`[IPC] 检测笔记敏感词: ${item}`);
        return await xhsService.checkNoteWord(item);
    });

    ipcMain.handle('xhs-note-check-user', async () => {
        console.log(`[IPC] 检测用户`);
        // return await xhsService.checkUser();
    });

    ipcMain.handle('xhs-user-info', async (event, userId) => {
        console.log(`[IPC] 获取用户信息: ${userId}`);
        return await xhsService.getUser(userId);
    });
}