import { getMainWindow } from '../windows';

export function registerUpdaterHandlers(
    ipcMain: Electron.IpcMain,
    startUpdateCheck: (webContents: Electron.WebContents, isManualCheck: boolean) => void,
    startDownload: (info: any) => void,
    quitAndInstall: (filePath: string) => void,
    openAndInstall: (filePath: string) => void
): void {
    ipcMain.on('check-for-updates', (event, isManualCheck = false) => {
        console.log(`[IPC] 检查更新 (手动: ${isManualCheck})`);
        startUpdateCheck(event.sender ?? getMainWindow()?.webContents, isManualCheck);
    });

    ipcMain.on('download-update', (event, info) => {
        console.log(`[IPC] 下载更新`);
        startDownload(info);
    });

    ipcMain.on('quit-and-install', (event, filePath: string) => {
        console.log(`[IPC] 退出并安装更新: ${filePath}`);
        openAndInstall(filePath);
    });
}