import { ipcMain } from 'electron';
import * as fs from 'fs';
import * as path from 'path';

export function registerFileSystemHandlers(
    ipcMain: Electron.IpcMain,
    fs: typeof import('fs'),
    path: typeof import('path')
): void {
    ipcMain.handle("path-dirname", async (event, filePath: string) => {
        console.log(`[IPC] 获取目录名: ${filePath}`);
        return path.dirname(filePath);
    });

    ipcMain.handle("path-basename", async (event, filePath: string) => {
        console.log(`[IPC] 获取文件名: ${filePath}`);
        return path.basename(filePath);
    });

    ipcMain.handle("path-sep", async () => {
        console.log(`[IPC] 获取路径分隔符`);
        return path.sep;
    });

    ipcMain.handle("fs-mkdirSync", async (event, dirPath: string, options?: fs.MakeDirectoryOptions) => {
        console.log(`[IPC] 创建目录: ${dirPath}`);
        fs.mkdirSync(dirPath, options);
    });

    ipcMain.handle("fs-existsSync", async (event, filePath: string) => {
        console.log(`[IPC] 检查文件/目录是否存在: ${filePath}`);
        return fs.existsSync(filePath);
    });

    ipcMain.handle("fs-writeFile", async (event, filePath: string, content: string) => {
        console.log(`[IPC] 写入文件: ${filePath}`);
        try {
            // 确保目录存在
            const dir = path.dirname(filePath);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }

            // 写入文件
            fs.writeFileSync(filePath, content, 'utf8');
            return { success: true };
        } catch (error: any) {
            console.error(`[IPC] 写入文件失败: ${filePath}`, error);
            throw error;
        }
    });

    ipcMain.handle("fs-readFile", async (event, filePath: string) => {
        console.log(`[IPC] 读取文件: ${filePath}`);
        try {
            const content = fs.readFileSync(filePath, 'utf8');
            return content;
        } catch (error: any) {
            console.error(`[IPC] 读取文件失败: ${filePath}`, error);
            throw error;
        }
    });
}