import { ipcMain } from 'electron';
import { MonitorService } from '../services/monitor-service';

export function registerMonitorIpcHandlers(ipcMain: Electron.IpcMain, monitorService: MonitorService) {

  ipcMain.handle('monitor:add-task', async (event, taskConfig) => {
    return monitorService.addMonitorTask(taskConfig);
  });

  ipcMain.handle('monitor:pause-task', async (event, taskId) => {
    return monitorService.pauseMonitorTask(taskId);
  });

  ipcMain.handle('monitor:resume-task', async (event, taskId) => {
    return monitorService.resumeMonitorTask(taskId);
  });

  ipcMain.handle('monitor:delete-task', async (event, taskId) => {
    return monitorService.deleteMonitorTask(taskId);
  });

  ipcMain.handle('monitor:get-tasks', async () => {
    return monitorService.getMonitorTasks();
  });

  ipcMain.handle('monitor:get-history-data', async (event, taskId) => {
    return monitorService.getMonitorHistoryData(taskId);
  });

  ipcMain.handle('monitor:get-history', async (event, taskId) => {
    return monitorService.getMonitorHistoryData(taskId);
  });

  ipcMain.handle('monitor:fetch-note-info', async (event, noteUrl) => {
    return monitorService.fetchNoteInfo(noteUrl);
  });

  ipcMain.handle('monitor:update-task', async (event, taskUpdate) => {
    return monitorService.updateMonitorTask(taskUpdate);
  });

  ipcMain.handle('monitor:get-task', async (event, taskId) => {
    return monitorService.getMonitorTask(taskId);
  });

  // 新增：获取博主笔记列表
  ipcMain.handle('xhs:get-blogger-notes', async (event, bloggerId, options) => {
    console.log(`[IPC] 获取博主笔记列表: ${bloggerId}`, options);
    try {
      const notes = await monitorService.getBloggerNotes(bloggerId, options);
      return {
        notes,
        total: notes.length
      };
    } catch (error) {
      console.error(`获取博主笔记列表失败:`, error);
      return { notes: [], total: 0 };
    }
  });
}