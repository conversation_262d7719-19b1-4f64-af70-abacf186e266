import { MediaService } from '../services/media-worker';
import { MediaIPCHandlers } from './media-handlers';

// 重新导出接口
export type { ConvertOptions, ASROptions, ApiResponse } from './media-handlers';

// 兼容性导出（保持向后兼容）
export interface BatchTask {
  id: string;
  fileName: string;
  filePath: string;
  type: 'video-convert' | 'audio-extract' | 'asr' | 'image-process';
  options: any;
  outputPath: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  progress: number;
  error?: string;
}

export interface BatchProgress {
  batchId: string;
  tasks: BatchTask[];
  totalTasks: number;
  completedTasks: number;
  failedTasks: number;
  overallProgress: number;
}

/**
 * 设置进度事件推送
 * @param mediaService MediaService 实例
 */
export function setupProgressEvents(mediaService: MediaService): void {
  const handlers = new MediaIPCHandlers(mediaService);
  handlers.setupProgressEvents();
}

/**
 * 注册所有媒体相关的 IPC 处理器
 * @param ipcMain Electron IPC Main 实例
 * @param mediaService MediaService 实例
 */
export function registerMediaHandlers(
  ipcMain: Electron.IpcMain,
  mediaService: MediaService
): void {
  console.log('[IPC] 注册媒体处理器...');
  
  const handlers = new MediaIPCHandlers(mediaService);
  handlers.register(ipcMain);
  
  console.log('[IPC] 媒体处理器注册完成');
}