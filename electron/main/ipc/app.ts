import { ipcMain, dialog, shell, BrowserWindow, app, clipboard } from 'electron';
import Store from 'electron-store';
import { handleToggleLoginWindow, handleLogout, getLoginWindow, getMainWindow } from '../windows';

export function registerAppHandlers(
    ipcMain: Electron.IpcMain,
    store: Store,
    dialog: Electron.Dialog,
    shell: Electron.Shell,
    BrowserWindow: typeof Electron.BrowserWindow,
    app: Electron.App,
    clipboard: Electron.Clipboard,
    handleToggleLoginWindow: (action: 'show' | 'hide', url?: string) => void,
    handleLogout: () => Promise<void>,
    getLoginWindow: () => Electron.BrowserWindow | null,
    getMainWindow: () => Electron.BrowserWindow | null
): void {

    // ipcMain.on("toggle-login-window", (event, action: 'show' | 'hide', url?: string) => {
    //     console.log(`[IPC] Toggle login window: ${action}`);
    //     handleToggleLoginWindow(action, url);
    // });

    ipcMain.handle("open-dialog", async (event, params) => {
        console.log(`[IPC] 打开对话框: ${JSON.stringify(params)}`);
        const result = await dialog.showOpenDialog(params);
        return result.filePaths;
    });

    ipcMain.handle("show-item-in-folder", async (event, path) => {
        console.log(`[IPC] 在文件夹中显示项目: ${path}`);
        shell.showItemInFolder(path);
    });

    ipcMain.handle("open-external", async (event, url: string) => {
        console.log(`[IPC] 打开外部链接: ${url}`);
        shell.openExternal(url);
    });

    ipcMain.handle("clipboard-write-text", async (event, text: string) => {
        console.log(`[IPC] 写入剪贴板: ${text}`);
        clipboard.writeText(text);
    });

    ipcMain.handle("get-login-info", async () => {
        console.log(`[IPC] 获取登录信息`);
        try {
            const info = store.get('xhs-login-info');
            console.log('[IPC] 获取登录信息成功:', info);
            return info || { guest: true };
        } catch (error) {
            console.error('[IPC] 获取登录信息失败:', error);
            return { guest: true };
        }
    });

    ipcMain.on("login", async (event) => {
        // This handler seems to initiate the login process which might involve
        // creating/showing the login window. Keeping it here for now, but
        // its implementation might need adjustment based on how xhsService.start() works.
        // await xhsService.start(); // xhsService is not available in this module
    });

    ipcMain.on('logout', async (event) => {
        console.log(`[IPC] 用户退出登录`);
        await handleLogout();
        const mainWindow = getMainWindow();
        if (mainWindow) {
            mainWindow.webContents.send('login-status-changed');
        }
    });

    ipcMain.on('login-status-detected', async (event, loginInfo) => {
        console.log(`[IPC] Detected login status change: ${JSON.stringify(loginInfo)}`);
        store.set('xhs-login-info', loginInfo);
        const loginWin = getLoginWindow();
        if (loginWin && !loginInfo?.guest) {
            loginWin.hide();
        }
        const mainWindow = BrowserWindow.getAllWindows().find((win: BrowserWindow) => win.title === 'kol-xhs');
        if (mainWindow) {
            mainWindow.webContents.send('login-status-changed');
        }
    });
}