import { ipcMain } from 'electron';
import { ExcelService } from '../services/excel-worker';

export function registerExcelHandlers(
    ipcMain: Electron.IpcMain,
    excelService: ExcelService
): void {
    ipcMain.handle('excel-file-parse', async (event, file) => {
        console.log(`[IPC] 解析 Excel 文件: ${file}`);
        return excelService.parseExcelFile(file);
    });

    ipcMain.handle('excel-file-build', async (event, obj) => {
        console.log(`[IPC] 构建 Excel 文件: ${obj.path}`);
        excelService.buildExcelFile(obj.data, obj.path);
    });

    ipcMain.handle('export-to-excel', async (event, { data, path: filePath }) => {
        console.log(`[IPC] 导出数据到 Excel: ${filePath}`);
        return excelService.exportToExcel(data, filePath);
    });
}