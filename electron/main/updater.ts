import { app, dialog, BrowserWindow, WebContents } from 'electron';
import { exec } from 'child_process';
import { autoUpdater, UpdateInfo } from 'electron-updater';
import https from 'https';
import fs from 'fs';
import path from 'path';

// Store the webContents to send messages back to the renderer
let rendererWebContents: WebContents | null = null;
let updateUrl;

// Function to start update check, called by IPC handler
export function startUpdateCheck(webContents: WebContents, isManualCheck = false) {
    rendererWebContents = webContents; // Store webContents

    // TODO: Get updateUrl from configuration or environment variables
    updateUrl = 'https://wy.frp.kolcm.com/updates'; // 原项目中的URL
    autoUpdater.setFeedURL(updateUrl);
    autoUpdater.autoDownload = false;
    // autoUpdater.forceDevUpdateConfig = true; // 根据新项目需要决定是否保留

    // Remove all listeners before adding to prevent duplicates
    autoUpdater.removeAllListeners();

    // Listen for update events
    autoUpdater.on('checking-for-update', () => {
        console.log('正在检查更新...');
        // Notify renderer process
        if (rendererWebContents) {
            rendererWebContents.send('checking-for-update');
        }
    });

    autoUpdater.on('update-available', (info: UpdateInfo) => {
        console.log('有可用更新。');
        // Notify renderer process
        if (rendererWebContents) {
            rendererWebContents.send('update-available', info);
        }

        // Original dialog logic - can be kept or moved to renderer
        // Notify renderer process to show update available dialog
        if (rendererWebContents) {
            rendererWebContents.send('update-available', info, isManualCheck); // Pass isManualCheck
        }
    });

    autoUpdater.on('update-not-available', () => {
        console.log('没有可用更新。');
        // Notify renderer process
        if (rendererWebContents) {
            rendererWebContents.send('update-not-available');
        }

        // Only show "already latest" message on manual check
        if (isManualCheck) {
            // Notify renderer process to show "no update" message
            if (rendererWebContents) {
                rendererWebContents.send('update-not-available', isManualCheck); // Pass isManualCheck
            }
        }
    });

    autoUpdater.on('error', (err) => {
        console.error('自动更新器出错: ' + err);
        // Notify renderer process
        if (rendererWebContents) {
            rendererWebContents.send('update-error', err.message); // Send error message
        }

        // Only show error message on manual check
        if (isManualCheck) {
            // Notify renderer process to show error message
            if (rendererWebContents) {
                rendererWebContents.send('update-error', err.message, isManualCheck); // Pass isManualCheck
            }
        }
    });

    // Note: electron-updater's download-progress and update-downloaded events are not used
    // as we are using custom download logic. Progress and completion are reported by showProgress
    // and the fileStream 'finish' event respectively.

    // Check for updates
    autoUpdater.checkForUpdates();
}

// Function to trigger download (called by IPC handler)
export function startDownload(info: UpdateInfo) {
    console.log('开始下载更新...');
    // Use autoUpdater's download function
    // 获取当前系统平台
    const platform = process.platform;
    console.log('当前系统平台: ' + platform);
    // 获取当前系统架构
    const arch = process.arch;
    console.log('当前系统架构: ' + arch);

    let target = '';
    if (platform === 'darwin') {
        target = 'dmg';
        if (arch === 'arm64') {
            target = 'arm64.dmg';
        }
    } else if (platform === 'win32') {
        target = 'exe';
    } else if (platform === 'linux') {
        target = 'AppImage';
    }

    if (target === '') {
        console.error('不支持的系统平台');
        dialog.showMessageBox({
            type: 'error',
            title: '错误',
            message: '不支持的系统平台',
        });
        return;
    }

    console.log('更新文件类型: ' + target);
    // 获取info。files中url包含target的文件第一个
    const targetFile = info.files.find((file) => file.url.includes(target))?.url;
    if (!targetFile) {
        console.error('找不到匹配的更新文件');
        dialog.showMessageBox({
            type: 'error',
            title: '错误',
            message: '找不到匹配的更新文件',
        });
        return;
    }

    const targetFileUrl = `${updateUrl}/${targetFile}`;

    console.log('更新文件下载地址: ' + targetFileUrl);
    const dmgName = decodeURIComponent(path.basename(targetFile));
    const downloadsPath = app.getPath('downloads');
    console.log('下载路径: ' + downloadsPath);
    const filePath = path.join(downloadsPath, dmgName);

    // Check if file already exists and matches size
    if (fs.existsSync(filePath)) {
        const stats = fs.statSync(filePath);
        const targetFileSize = info.files.find((file) => file.url.includes(target))?.size;
        if (targetFileSize && stats.size === targetFileSize) {
            console.log('文件已存在且大小匹配，跳过下载');
            // Notify renderer process that download is completed (file already exists)
            if (rendererWebContents) {
                rendererWebContents.send('download-completed', filePath);
            }
            return;
        } else {
            console.log('文件已存在但大小不匹配或找不到大小信息，删除旧文件');
            fs.unlinkSync(filePath);
        }
    }

    const fileStream = fs.createWriteStream(filePath);
    const request = https.get(targetFileUrl, (response) => {
        if (response.statusCode !== 200) {
            console.error('下载 dmg 文件出错: ' + response.statusCode);
            dialog.showMessageBox({
                type: 'error',
                title: '错误',
                message: `下载 dmg 文件出错: ${response.statusCode}`,
            });
            return;
        }

        let receivedBytes = 0;
        const totalBytes = parseInt(response.headers['content-length'] || '0', 10);

        response.on('data', (chunk) => {
            receivedBytes += chunk.length;
            showProgress(receivedBytes, totalBytes);
        });

        response.pipe(fileStream);
    });

    request.on('error', (err) => {
        console.error('下载 dmg 文件出错: ' + err);
        fs.unlink(filePath, () => { });
        // Notify renderer process of download error
        if (rendererWebContents) {
            rendererWebContents.send('download-error', '下载文件出错: ' + err.message);
        }
    });

    fileStream.on('finish', () => {
        fileStream.close(() => {
            console.log('dmg 文件下载完成');
            // Notify renderer process that download is completed
            if (rendererWebContents) {
                rendererWebContents.send('download-completed', filePath); // Send file path
            }
        });
    });

    fileStream.on('error', (err) => {
        console.error('下载 dmg 文件出错: ' + err);
        fs.unlink(filePath, () => { });
        // Notify renderer process of download error
        if (rendererWebContents) {
            rendererWebContents.send('download-error', '下载文件出错: ' + err.message);
        }
    });
}

function showProgress(receivedBytes, totalBytes) {
    const progress = (receivedBytes / totalBytes) * 100;
    console.log(`下载进度: ${progress.toFixed(2)}% (${receivedBytes} / ${totalBytes})`);
    // 通过 ipcMain 发送下载进度到渲染进程
    if (rendererWebContents) {
        rendererWebContents.send('download-progress', { progress, receivedBytes, totalBytes });
    }
}

// Function to quit and install (called by IPC handler)
export function quitAndInstall() {
    console.log('退出并安装更新...');
    autoUpdater.quitAndInstall();
}


// Custom install logic from original file (kept for reference but not used by default)
export function openAndInstall(filePath: string) {
    exec(`open "${filePath}"`, (error, stdout, stderr) => {
        if (error) {
            console.error(`执行 open 命令出错: ${error}`);
            // Notify renderer process
            if (rendererWebContents) {
                rendererWebContents.send('install-error', error.message);
            }
            return;
        }
        console.log(`stdout: ${stdout}`);
        console.error(`stderr: ${stderr}`);
        // Fully quit the application after successful execution (assuming open triggers install)
        app.quit();
    });
}