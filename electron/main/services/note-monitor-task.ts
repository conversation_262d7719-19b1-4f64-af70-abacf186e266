import { monitorStore, MonitorTaskConfig, NoteDataPoint, IMonitorTask } from '../store/monitor-store';
import { XhsSpider } from '../api/xhs-spider';
import { v4 } from 'uuid';

export class NoteMonitorTask implements IMonitorTask {
  id: string;
  name: string;
  noteUrl: string;
  frequency: number;
  lastRunTime: number;
  status: 'running' | 'paused' | 'stopped';

  // 笔记数据存储在data对象中
  data: {
    noteId?: string;
    coverUrl?: string;
    title?: string;
    desc?: string;
    authorAvatar?: string;
    authorName?: string;
    likeCount?: number;
    commentCount?: number;
    collectCount?: number;
    shareCount?: number;
  };

  private xhsSpider: XhsSpider;
  private intervalId: NodeJS.Timeout | null = null;
  private errorCount: number = 0; // 错误计数器
  private maxErrorCount: number = 3; // 最大允许错误次数
  private lastErrorTime: number = 0; // 上次发生错误的时间
  private errorCooldownPeriod: number = 30 * 60 * 1000; // 错误冷却时间，30分钟

  constructor(config: MonitorTaskConfig, xhsSpider: XhsSpider) {
    this.id = config.id ?? v4();
    this.noteUrl = config.noteUrl!;
    this.name = config.name;
    this.frequency = config.frequency;
    this.lastRunTime = config.lastRunTime || 0;
    this.status = config.status || 'stopped';

    // 初始化数据对象
    this.data = {
      ...(config.data || {}),
    };

    this.xhsSpider = xhsSpider;
  }


  async start(): Promise<void> {
    if (this.status === 'running' && this.intervalId) {
      console.log(`NoteMonitorTask ${this.name} (ID: ${this.id}) is already running.`);
      return;
    }

    // 重置错误计数器
    this.errorCount = 0;

    this.status = 'running';
    monitorStore.saveMonitorTask(this.toConfig());
    console.log(`NoteMonitorTask ${this.name} (ID: ${this.id}) started.`);

    const now = Date.now();
    const timeSinceLastRun = now - this.lastRunTime;
    const frequencyMs = this.getFrequencyInMs();

    // 如果上次出错，增加一个冷却期，避免立即重试
    let initialDelay = 0;
    if (this.lastErrorTime > 0 && (now - this.lastErrorTime) < this.errorCooldownPeriod) {
      // 使用冷却时间作为初始延迟
      initialDelay = this.errorCooldownPeriod - (now - this.lastErrorTime);
      console.log(`NoteMonitorTask ${this.name} (ID: ${this.id}) will wait ${initialDelay / 1000} seconds before retry due to previous error.`);
    } else if (timeSinceLastRun < frequencyMs) {
      initialDelay = frequencyMs - timeSinceLastRun;
      console.log(`NoteMonitorTask ${this.name} (ID: ${this.id}) will execute first in ${initialDelay / 1000} seconds.`);
    } else {
      // 只有在没有处于错误冷却期时才立即执行
      await this.execute();
    }

    // 确保只设置一个定时器，并在 initialDelay 后开始周期性执行
    this.intervalId = setInterval(() => this.execute(), frequencyMs);
    if (initialDelay > 0) {
      // 如果有初始延迟，则在延迟后执行一次，然后由 setInterval 接管
      setTimeout(() => {
        if (this.status === 'running') { // 确保任务在延迟期间没有被暂停或停止
          this.execute();
        }
      }, initialDelay);
    }
  }

  private getFrequencyInMs(): number {
    // 将频率转换为毫秒，最小为5分钟
    const minFrequency = 5; // 最小5分钟
    let freqInMinutes = typeof this.frequency === 'number' ? this.frequency : 60; // 默认60分钟

    // 如果是字符串频率(hourly/daily/weekly/monthly)，转换为分钟
    if (typeof this.frequency === 'string') {
      switch (this.frequency) {
        case 'hourly': freqInMinutes = 60; break;
        case 'daily': freqInMinutes = 24 * 60; break;
        case 'weekly': freqInMinutes = 7 * 24 * 60; break;
        case 'monthly': freqInMinutes = 30 * 24 * 60; break;
        default: freqInMinutes = 60; // 默认为1小时
      }
    }

    freqInMinutes = Math.max(freqInMinutes, minFrequency);
    return freqInMinutes * 60 * 1000;
  }

  pause(): void {
    if (this.status === 'paused') {
      console.log(`NoteMonitorTask ${this.name} (ID: ${this.id}) is already paused.`);
      return;
    }
    this.status = 'paused';
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    monitorStore.saveMonitorTask(this.toConfig());
    console.log(`NoteMonitorTask ${this.name} (ID: ${this.id}) paused.`);
  }

  async resume(): Promise<void> {
    if (this.status === 'running') {
      console.log(`NoteMonitorTask ${this.name} (ID: ${this.id}) is already running.`);
      return;
    }
    // 重置错误计数器
    this.errorCount = 0;

    this.status = 'running';
    monitorStore.saveMonitorTask(this.toConfig());
    console.log(`NoteMonitorTask ${this.name} (ID: ${this.id}) resumed.`);

    // 恢复时，立即执行一次，并重新设置定时器
    await this.execute();
    this.intervalId = setInterval(() => this.execute(), this.getFrequencyInMs());
  }

  stop(): void {
    if (this.status === 'stopped') {
      console.log(`NoteMonitorTask ${this.name} (ID: ${this.id}) is already stopped.`);
      return;
    }
    this.status = 'stopped';
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    monitorStore.saveMonitorTask(this.toConfig());
    console.log(`NoteMonitorTask ${this.name} (ID: ${this.id}) stopped.`);
  }

  async execute(): Promise<void> {
    console.log(`Executing NoteMonitorTask ${this.name} (ID: ${this.id})...`);
    try {
      // 从链接https://www.xiaohongshu.com/explore/678d034e00000000190332cd?xsec_source=1&xsec_token=ABzxvrx0dSnMlCmDV2KKj_C9XLkXY2xWe_te253Qydtm0= 中提取出noteId，需要去掉?后面参数
      const noteId = this.noteUrl.split('/').pop()?.split('?')[0] || '';
      this.data.noteId = noteId;
      console.log(`Retrieving note data for noteId: ${noteId}`);
      const noteDetail = await this.xhsSpider.getNoteByText(this.noteUrl);
      if (noteDetail) {
        // 更新笔记数据
        this.data.title = noteDetail.title;
        this.data.desc = noteDetail.desc;
        this.data.likeCount = noteDetail.liked_count;
        this.data.commentCount = noteDetail.comments_count;
        this.data.shareCount = noteDetail.shared_count;

        // 如果有用户信息，也同步更新
        if (noteDetail.user) {
          this.data.authorName = noteDetail.user.nickname || noteDetail.user.name;
          this.data.authorAvatar = noteDetail.user.avatar;
        }

        // 如果有图片，更新封面
        if (noteDetail.images_list && noteDetail.images_list.length > 0) {
          this.data.coverUrl = noteDetail.images_list[0].url_pre || noteDetail.images_list[0].url;
        }

        const dataPoint: NoteDataPoint = {
          timestamp: Date.now(),
          likes: noteDetail.liked_count,
          comments: noteDetail.comments_count,
          forwards: noteDetail.shared_count,
        };
        console.log(`Adding data point for noteId: ${noteId}`, dataPoint);
        monitorStore.addNoteDataPoint(noteId, dataPoint);
        console.log(`Note ${this.noteUrl} data point added:`, dataPoint);

        // 成功执行后，重置错误计数
        this.errorCount = 0;
        this.lastErrorTime = 0;

        // 保存更新的笔记信息
        monitorStore.saveMonitorTask(this.toConfig());
      } else {
        console.warn(`No data found for noteId: ${this.noteUrl}`);
      }
      this.lastRunTime = Date.now();
      monitorStore.saveMonitorTask(this.toConfig());
    } catch (error) {
      this.errorCount++;
      this.lastErrorTime = Date.now();
      console.error(`Error executing NoteMonitorTask ${this.id}:`, error);

      // 如果连续错误次数超过阈值，暂停任务
      if (this.errorCount >= this.maxErrorCount) {
        console.error(`NoteMonitorTask ${this.id} failed ${this.errorCount} times consecutively. Pausing task.`);
        this.pause();
        // 可以在这里发送通知给用户，告知任务因多次失败而被暂停
      }
    }
  }

  public toConfig(): MonitorTaskConfig {
    return {
      id: this.id,
      type: 'note',
      noteUrl: this.noteUrl,
      name: this.name,
      frequency: this.frequency,
      lastRunTime: this.lastRunTime,
      status: this.status,
      // 新版数据结构
      data: {
        ...this.data
      },
    };
  }
}