import { monitorStore, MonitorTaskConfig } from '../store/monitor-store';
import { NoteMonitorTask } from './note-monitor-task';
import { BloggerMonitorTask } from './blogger-monitor-task';
import { XhsSpider } from '../api/xhs-spider';
import { ipc<PERSON>ain, BrowserWindow } from 'electron';
import path from 'path';
import fs from 'fs';

type MonitorTaskInstance = NoteMonitorTask | BloggerMonitorTask;

// Helper function to sanitize file paths
function sanitizePath(input: string): string {
  // Remove or replace invalid characters
  return input
    .replace(/[<>:"/\\|?*\x00-\x1F]/g, '_') // Replace invalid Windows filename characters
    .replace(/[\u{1F300}-\u{1F9FF}]/gu, '') // Remove emojis
    .replace(/\s+/g, '_') // Replace spaces with underscores
    .replace(/[^\x20-\x7E]/g, '') // Remove non-ASCII characters
    .substring(0, 255); // Limit length to avoid path too long errors
}

export class MonitorService {
  private tasks: Map<string, MonitorTaskInstance> = new Map();
  private xhsSpider: XhsSpider;

  constructor() {
    this.xhsSpider = new XhsSpider();
    // 延迟30s后加载任务
    this.loadAndRestartTasks();
  }

  // 广播任务更新事件到所有渲染进程窗口
  private broadcastTaskUpdate(eventType: string, taskData: any) {
    const allWindows = BrowserWindow.getAllWindows();
    allWindows.forEach(window => {
      if (!window.isDestroyed()) {
        window.webContents.send('monitor:task-updated', {
          type: eventType,
          data: taskData
        });
      }
    });
  }

  private loadAndRestartTasks() {
    const savedTasks = monitorStore.getAllMonitorTasks();
    console.log(`Loading ${savedTasks.length} saved monitor tasks.`);
    for (const config of savedTasks) {
      try {
        // Ensure name and targetId are present for old tasks
        const processedConfig: MonitorTaskConfig = {
          ...config,
          name: config.name || (config.type === 'note' ? config.noteUrl : config.bloggerId) || `未知任务-${config.id.substring(0, 8)}`,
          noteUrl: config.type === 'note' ? config.noteUrl : undefined,
          bloggerId: config.type === 'blogger' ? config.bloggerId : undefined,
        };

        // Sanitize the name for file paths
        processedConfig.name = sanitizePath(processedConfig.name);

        let task: MonitorTaskInstance;
        if (processedConfig.type === 'note') {
          task = new NoteMonitorTask(processedConfig, this.xhsSpider);
        } else if (processedConfig.type === 'blogger') {
          task = new BloggerMonitorTask(processedConfig, this.xhsSpider);
        } else {
          console.warn(`Unknown task type: ${processedConfig.type}`);
          continue;
        }
        this.tasks.set(processedConfig.id, task);
        if (processedConfig.status === 'running') {
          // 自动恢复运行中的任务
          task.start();
        } else if (processedConfig.status === 'paused') {
          // 保持暂停状态
          task.pause();
        } // stopped等其它状态不自动处理，保持原样
      } catch (error) {
        console.error(`Failed to load task ${config.id}:`, error);
      }
    }
  }

  async addMonitorTask(taskConfig: MonitorTaskConfig) {
    try {
      let task: MonitorTaskInstance;
      if (taskConfig.type === 'note') {
        task = new NoteMonitorTask(taskConfig, this.xhsSpider);
      } else if (taskConfig.type === 'blogger') {
        task = new BloggerMonitorTask(taskConfig, this.xhsSpider);
      } else {
        throw new Error(`Unsupported task type: ${taskConfig.type}`);
      }

      this.tasks.set(task.id, task);
      monitorStore.saveMonitorTask(task.toConfig());
      await task.start();
      
      // 广播任务添加事件到所有渲染进程窗口
      this.broadcastTaskUpdate('task-added', task.toConfig());
      
      return { success: true };
    } catch (error: any) {
      console.error(`Failed to add monitor task:`, error);
      return { success: false, message: error.message || 'Failed to add monitor task' };
    }
  }

  async deleteMonitorTask(taskId: string) {
    const task = this.tasks.get(taskId);
    if (task) {
      try {
        await task.stop();
        this.tasks.delete(taskId);
        monitorStore.deleteMonitorTaskConfig(taskId);

        // 可选：删除该任务对应的历史数据文件
        const taskConfig = task.toConfig();
        if (taskConfig.type === 'note' && taskConfig.noteUrl) {
          const sanitizedNoteId = sanitizePath(taskConfig.noteUrl);
          monitorStore.deleteNoteHistoryData(sanitizedNoteId);
        } else if (taskConfig.type === 'blogger' && taskConfig.bloggerId) {
          const sanitizedBloggerId = sanitizePath(taskConfig.bloggerId);
          monitorStore.deleteBloggerNewNotes(sanitizedBloggerId);
        }
        console.log(`Task ${taskId} and its history data deleted.`);
        return { success: true };
      } catch (error: any) {
        console.error(`Error deleting task ${taskId}:`, error);
        return { success: false, message: error.message || 'Unknown error occurred' };
      }
    } else {
      console.warn(`Task with ID ${taskId} not found.`);
      return { success: false, message: 'Task not found' };
    }
  }

  getMonitorTasks(): MonitorTaskConfig[] {
    return Array.from(this.tasks.values()).map(task => task.toConfig());
  }

  getMonitorHistoryData(taskId: string) {
    console.log(`Getting history data for task ID: ${taskId}`);
    const allTasks = monitorStore.getAllMonitorTasks();
    console.log(`Found ${allTasks.length} total tasks`);

    const taskConfig = allTasks.find(t => t.id === taskId);
    if (!taskConfig) {
      console.warn(`Task with ID ${taskId} not found in stored tasks.`);
      return null;
    }
    console.log(`Found task config:`, taskConfig);

    try {
      if (taskConfig.type === 'note' && taskConfig.noteUrl) {
        const noteId = taskConfig.data?.noteId || ''
        const historyData = monitorStore.getNoteHistoryData(noteId);
        console.log(`Found ${historyData.length} data points for note ${noteId}`);

        // If no data found directly, try to find by looking for similar IDs
        if (historyData.length === 0) {
          console.log(`No data found directly. Trying to find similar IDs...`);
          // Check if the noteId is a URL and extract the item ID part
          if (taskConfig.noteUrl.includes('item/')) {
            const itemIdMatch = taskConfig.noteUrl.match(/item\/([^/?&]+)/);
            if (itemIdMatch && itemIdMatch[1]) {
              const itemId = itemIdMatch[1];
              console.log(`Extracted item ID: ${itemId}`);
              const itemSanitized = sanitizePath(itemId);
              console.log(`Trying with sanitized item ID: ${itemSanitized}`);
              const alternativeData = monitorStore.getNoteHistoryData(itemSanitized);
              if (alternativeData.length > 0) {
                console.log(`Found ${alternativeData.length} data points using item ID`);
                return alternativeData;
              }
            }
          }
        }

        return historyData;
      } else if (taskConfig.type === 'blogger' && taskConfig.bloggerId) {
        const sanitizedBloggerId = sanitizePath(taskConfig.bloggerId);
        console.log(`Retrieving blogger notes for ${taskConfig.bloggerId}, sanitized as ${sanitizedBloggerId}`);
        const bloggerNotes = monitorStore.getBloggerNewNotes(sanitizedBloggerId);
        console.log(`Found ${bloggerNotes.length} new notes for blogger ${sanitizedBloggerId}`);
        return bloggerNotes;
      }

      console.warn(`Task type not supported or missing ID information:`, taskConfig);
      return null;
    } catch (error) {
      console.error(`Error retrieving history data:`, error);
      return [];
    }
  }

  async pauseMonitorTask(taskId: string) {
    try {
      const task = this.tasks.get(taskId);
      if (task) {
        await task.pause();
        // 任务内部会更新状态并保存到 store
        return { success: true };
      } else {
        console.warn(`Task with ID ${taskId} not found.`);
        return { success: false, message: `Task with ID ${taskId} not found.` };
      }
    } catch (error: any) {
      console.error(`Failed to pause task ${taskId}:`, error);
      return { success: false, message: error.message || 'Failed to pause task' };
    }
  }

  async resumeMonitorTask(taskId: string) {
    try {
      const task = this.tasks.get(taskId);
      if (task) {
        await task.resume();
        // 任务内部会更新状态并保存到 store
        return { success: true };
      } else {
        console.warn(`Task with ID ${taskId} not found.`);
        return { success: false, message: `Task with ID ${taskId} not found.` };
      }
    } catch (error: any) {
      console.error(`Failed to resume task ${taskId}:`, error);
      return { success: false, message: error.message || 'Failed to resume task' };
    }
  }


  // 新增：更新任务信息方法
  async updateMonitorTask(taskUpdate: { id: string, name: string, frequency?: number }) {
    try {
      const task = this.tasks.get(taskUpdate.id);
      if (!task) {
        console.warn(`任务 ID ${taskUpdate.id} 不存在.`);
        return {
          success: false,
          message: `任务 ID ${taskUpdate.id} 不存在.`
        };
      }

      // 更新任务名称
      if (taskUpdate.name) {
        task.name = taskUpdate.name;
        // 如果是博主任务，同时更新data中的nickname
        if ((task as any).data && (task as any).data.nickname !== undefined) {
          (task as any).data.nickname = taskUpdate.name;
        }
      }

      // 更新频率（如果提供了）
      let frequencyChanged = false;
      if (taskUpdate.frequency !== undefined && task.frequency !== taskUpdate.frequency) {
        task.frequency = taskUpdate.frequency;
        frequencyChanged = true;
      }

      // 新增：自动补全笔记基础信息
      if ((task as any).noteUrl) {
        const noteUrl = (task as any).noteUrl;
        const noteInfo = await this.fetchNoteInfo(noteUrl);
        if (noteInfo && noteInfo.success && noteInfo.data) {
          (task as any).title = noteInfo.data.title || '';
          (task as any).coverUrl = (noteInfo.data.images_list && noteInfo.data.images_list[0]?.url_default) || '';
          (task as any).authorAvatar = noteInfo.data.user?.avatar || '';
          (task as any).authorName = noteInfo.data.user?.nickname || '';
        }
      }

      // 保存更新后的任务配置
      monitorStore.saveMonitorTask(task.toConfig());

      // 新增：如果任务正在运行且频率发生变化，重启定时器
      if (frequencyChanged && task.status === 'running' && typeof (task as any).start === 'function') {
        if ((task as any).intervalId) {
          clearInterval((task as any).intervalId);
          (task as any).intervalId = null;
        }
        await (task as any).start();
      }

      console.log(`任务 ${taskUpdate.id} 更新成功.`);
      return { success: true };
    } catch (error: any) {
      console.error(`更新任务失败:`, error);
      return {
        success: false,
        message: error.message || '更新任务失败'
      };
    }
  }

  getMonitorTask(taskId: string): MonitorTaskConfig | null {
    const task = this.tasks.get(taskId);
    if (task) {
      return task.toConfig();
    }
    return null;
  }

  // 新增：获取博主笔记列表方法
  async getBloggerNotes(bloggerId: string, options?: { page: number, pageSize: number, sort?: string }) {
    console.log(`获取博主笔记列表: ${bloggerId}, 页码: ${options?.page || 1}, 每页: ${options?.pageSize || 20}`);
    try {
      // 从缓存中获取博主笔记，而不是实时请求
      const sanitizedBloggerId = sanitizePath(bloggerId);
      const cachedNotes = monitorStore.getBloggerNewNotes(sanitizedBloggerId);

      // 分页处理
      if (options && options.page && options.pageSize) {
        const start = (options.page - 1) * options.pageSize;
        const end = start + options.pageSize;
        return cachedNotes.slice(start, end);
      }

      return cachedNotes;
    } catch (error) {
      console.error(`获取博主笔记失败:`, error);
      return [];
    }
  }

  // 新增：获取笔记信息方法
  async fetchNoteInfo(noteUrl: string) {
    try {
      console.log(`获取笔记信息: ${noteUrl}`);
      const noteDetail = await this.xhsSpider.getNoteByText(noteUrl);
      if (noteDetail) {
        return {
          success: true,
          data: noteDetail
        };
      } else {
        return {
          success: false,
          message: '获取笔记信息失败'
        };
      }
    } catch (error: any) {
      console.error(`获取笔记信息失败:`, error);
      return {
        success: false,
        message: error.message || '获取笔记信息失败'
      };
    }
  }
}