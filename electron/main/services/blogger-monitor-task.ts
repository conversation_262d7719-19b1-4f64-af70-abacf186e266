import { monitorStore, MonitorTaskConfig, BloggerNewNote, IMonitorTask } from '../store/monitor-store';
import { XhsSpider } from '../api/xhs-spider';

export class BloggerMonitorTask implements IMonitorTask {
  id: string;
  bloggerId: string;
  name: string;
  frequency: number;
  lastRunTime: number;
  status: 'running' | 'paused' | 'stopped';
  lastKnownNoteIds: string[];

  // 用户数据存储在data对象中
  data: {
    nickname?: string;
    avatar?: string;
    desc?: string;
    gender?: string;
    fans?: number;
    follows?: number;
    interaction?: number;
    notes?: number;
    lastUserInfoUpdate?: number;
  };

  // 兼容旧版本的属性
  private _authorAvatar: string = '';
  private _authorName: string = '';

  private xhsSpider: XhsSpider;
  private intervalId: NodeJS.Timeout | null = null;
  private errorCount: number = 0; // 错误计数器
  private maxErrorCount: number = 3; // 最大允许错误次数
  private lastErrorTime: number = 0; // 上次发生错误的时间
  private errorCooldownPeriod: number = 30 * 60 * 1000; // 错误冷却时间，30分钟

  constructor(config: MonitorTaskConfig, xhsSpider: XhsSpider) {
    this.id = config.id;
    this.bloggerId = config.bloggerId!;
    this.name = config.name;
    this.frequency = config.frequency;
    this.lastRunTime = config.lastRunTime || 0;
    this.status = config.status || 'stopped';
    this.lastKnownNoteIds = config.lastKnownNoteIds || [];

    // 初始化数据对象
    this.data = {
      ...(config.data || {}),
    };

    this.xhsSpider = xhsSpider;
  }

  // 添加访问器方法，保持向后兼容性
  get authorAvatar(): string {
    return this.data.avatar || this._authorAvatar;
  }

  get authorName(): string {
    return this.data.nickname || this._authorName;
  }

  async start(): Promise<void> {
    if (this.status === 'running' && this.intervalId) {
      console.log(`BloggerMonitorTask ${this.name} (ID: ${this.id}) is already running.`);
      return;
    }

    // 重置错误计数器
    this.errorCount = 0;

    this.status = 'running';
    monitorStore.saveMonitorTask(this.toConfig());
    console.log(`BloggerMonitorTask ${this.name} (ID: ${this.id}) started.`);

    const now = Date.now();
    const timeSinceLastRun = now - this.lastRunTime;
    const frequencyMs = this.getFrequencyInMs();

    // 如果上次出错，增加一个冷却期，避免立即重试
    let initialDelay = 0;
    if (this.lastErrorTime > 0 && (now - this.lastErrorTime) < this.errorCooldownPeriod) {
      // 使用冷却时间作为初始延迟
      initialDelay = this.errorCooldownPeriod - (now - this.lastErrorTime);
      console.log(`BloggerMonitorTask ${this.name} (ID: ${this.id}) will wait ${initialDelay / 1000} seconds before retry due to previous error.`);
    } else if (timeSinceLastRun < frequencyMs) {
      initialDelay = frequencyMs - timeSinceLastRun;
      console.log(`BloggerMonitorTask ${this.name} (ID: ${this.id}) will execute first in ${initialDelay / 1000} seconds.`);
    } else {
      // 只有在没有处于错误冷却期时才立即执行
      await this.execute();
    }

    // 确保只设置一个定时器，并在 initialDelay 后开始周期性执行
    this.intervalId = setInterval(() => this.execute(), frequencyMs);
    if (initialDelay > 0) {
      // 如果有初始延迟，则在延迟后执行一次，然后由 setInterval 接管
      setTimeout(() => {
        if (this.status === 'running') { // 确保任务在延迟期间没有被暂停或停止
          this.execute();
        }
      }, initialDelay);
    }
  }

  private getFrequencyInMs(): number {
    // 将频率转换为毫秒，最小为5分钟
    const minFrequency = 5; // 最小5分钟
    let freqInMinutes = typeof this.frequency === 'number' ? this.frequency : 60; // 默认60分钟

    // 如果是字符串频率(hourly/daily/weekly/monthly)，转换为分钟
    if (typeof this.frequency === 'string') {
      switch (this.frequency) {
        case 'hourly': freqInMinutes = 60; break;
        case 'daily': freqInMinutes = 24 * 60; break;
        case 'weekly': freqInMinutes = 7 * 24 * 60; break;
        case 'monthly': freqInMinutes = 30 * 24 * 60; break;
        default: freqInMinutes = 60; // 默认为1小时
      }
    }

    freqInMinutes = Math.max(freqInMinutes, minFrequency);
    return freqInMinutes * 60 * 1000;
  }

  pause(): void {
    if (this.status === 'paused') {
      console.log(`BloggerMonitorTask ${this.name} (ID: ${this.id}) is already paused.`);
      return;
    }
    this.status = 'paused';
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    monitorStore.saveMonitorTask(this.toConfig());
    console.log(`BloggerMonitorTask ${this.name} (ID: ${this.id}) paused.`);
  }

  async resume(): Promise<void> {
    if (this.status === 'running') {
      console.log(`BloggerMonitorTask ${this.name} (ID: ${this.id}) is already running.`);
      return;
    }

    // 重置错误计数器
    this.errorCount = 0;

    this.status = 'running';
    monitorStore.saveMonitorTask(this.toConfig());
    console.log(`BloggerMonitorTask ${this.name} (ID: ${this.id}) resumed.`);

    // 恢复时，立即执行一次，并重新设置定时器
    await this.execute();
    this.intervalId = setInterval(() => this.execute(), this.getFrequencyInMs());
  }

  stop(): void {
    if (this.status === 'stopped') {
      console.log(`BloggerMonitorTask ${this.name} (ID: ${this.id}) is already stopped.`);
      return;
    }
    this.status = 'stopped';
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    monitorStore.saveMonitorTask(this.toConfig());
    console.log(`BloggerMonitorTask ${this.name} (ID: ${this.id}) stopped.`);
  }

  async execute(): Promise<void> {
    console.log(`Executing BloggerMonitorTask ${this.name} (ID: ${this.id})...`);
    try {
      // 获取博主最新笔记列表和用户信息（一次请求获取两种数据）
      const result = await this.xhsSpider.getUserWebNotesWithUserInfo(this.bloggerId);
      const latestNotes = result.notes;
      const userInfo = result.userInfo;
      
      console.log(`获取到博主${this.bloggerId}的笔记数量: ${latestNotes.length}`);
      this.data.notes = latestNotes.length;

      // 如果获取到了用户信息，则更新用户数据
      if (userInfo) {
        console.log(`更新博主${this.bloggerId}的用户信息`);
        this.data.nickname = userInfo.nickname || userInfo.name || this.data.nickname;
        this.data.avatar = userInfo.avatar || this.data.avatar;
        this.data.desc = userInfo.desc || this.data.desc;
        this.data.gender = userInfo.gender || this.data.gender;
        this.data.fans = userInfo.fans || this.data.fans;
        this.data.follows = userInfo.follows || this.data.follows;
        this.data.interaction = userInfo.interaction || this.data.interaction;
        this.data.lastUserInfoUpdate = Date.now();
        
        // 更新任务名称为最新的昵称
        if (userInfo.nickname) {
          this.name = userInfo.nickname;
        }
        
        console.log(`博主${this.bloggerId}信息更新完成: ${this.data.nickname}`);
      } else {
        console.log(`未获取到博主${this.bloggerId}的用户信息，保持原有数据`);
      }

      const newNoteIds: string[] = [];
      const newNotesData: BloggerNewNote[] = [];
      const currentNoteIds = latestNotes.map(note => note.id);

      // 处理当前获取的笔记
      for (const note of latestNotes) {
        if (!this.lastKnownNoteIds.includes(note.id)) {
          newNoteIds.push(note.id);
          const newNote: BloggerNewNote = {
            noteId: note.id,
            type: note.noteCard.type,
            title: note.noteCard.displayTitle || '无标题',
            url: `https://www.xiaohongshu.com/explore/${note.id}?xsec_source=1&xsec_token=${note.xsecToken}`,
            cover: note.noteCard.cover.urlDefault || '',
            likeCount: note.noteCard.interactInfo.likedCount,
            sticky: note.noteCard.interactInfo.sticky,
            index: note.index,
            createTime: new Date().toISOString(),
          };
          newNotesData.push(newNote);
          monitorStore.addBloggerNewNote(this.bloggerId, newNote);
          console.log(`新增博主${this.bloggerId}的笔记:`, newNote);
        } else {
          monitorStore.updateBloggerNewNote(this.bloggerId, note.id, {
            cover: note.noteCard.cover.urlDefault || '',
            title: note.noteCard.displayTitle || '无标题',
            likeCount: note.noteCard.interactInfo.likedCount,
            sticky: note.noteCard.interactInfo.sticky,
            index: note.index,
          });
        }
      }

      // 检查已知笔记中是否有被删除的笔记（在最新获取的笔记中找不到的）
      const deletedNoteIds = this.lastKnownNoteIds.filter(noteId => !currentNoteIds.includes(noteId));
      for (const deletedNoteId of deletedNoteIds) {
        monitorStore.markBloggerNoteAsDeleted(this.bloggerId, deletedNoteId);
        console.log(`标记博主${this.bloggerId}的笔记${deletedNoteId}为已删除`);
      }

      // 更新已知的笔记ID列表：保留所有笔记ID（包括已删除的），并添加新的
      this.lastKnownNoteIds = Array.from(new Set([...this.lastKnownNoteIds, ...newNoteIds]));
      this.lastRunTime = Date.now();
      monitorStore.saveMonitorTask(this.toConfig());

      // 成功执行后，重置错误计数
      this.errorCount = 0;
      this.lastErrorTime = 0;

    } catch (error) {
      this.errorCount++;
      this.lastErrorTime = Date.now();
      console.error(`Error executing BloggerMonitorTask ${this.id}:`, error);

      // 如果连续错误次数超过阈值，暂停任务
      if (this.errorCount >= this.maxErrorCount) {
        console.error(`BloggerMonitorTask ${this.id} failed ${this.errorCount} times consecutively. Pausing task.`);
        this.pause();
        // 可以在这里发送通知给用户，告知任务因多次失败而被暂停
      }
    }
  }

  public toConfig(): MonitorTaskConfig {
    return {
      id: this.id,
      type: 'blogger',
      bloggerId: this.bloggerId,
      name: this.name,
      frequency: this.frequency,
      lastRunTime: this.lastRunTime,
      status: this.status,
      lastKnownNoteIds: this.lastKnownNoteIds,
      // 新版数据结构
      data: {
        ...this.data
      },
    };
  }
}