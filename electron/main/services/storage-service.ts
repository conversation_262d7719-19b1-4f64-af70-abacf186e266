import Store from 'electron-store'
import { app } from 'electron'
import path from 'path'

export class StorageService {
  private stores: Map<string, Store> = new Map()
  private dataPath: string
  
  constructor() {
    this.dataPath = path.join(app.getPath('userData'), 'media-data')
  }
  
  getStore(name: string): Store {
    if (!this.stores.has(name)) {
      this.stores.set(name, new Store({
        name,
        cwd: this.dataPath,
        encryptionKey: process.env.NODE_ENV === 'production' ? 'your-encryption-key' : undefined,
        fileExtension: 'json',
        serialize: (data) => JSON.stringify(data, null, 2),
        deserialize: JSON.parse,
        migrations: {
          '1.0.0': (_store) => {
            // 迁移逻辑
          }
        }
      }))
    }
    return this.stores.get(name)!
  }
  
  async get(storeName: string, key?: string): Promise<any> {
    const store = this.getStore(storeName)
    return key ? store.get(key) : store.store
  }
  
  async set(storeName: string, key: string, value: any): Promise<void> {
    const store = this.getStore(storeName)
    store.set(key, value)
  }
  
  async delete(storeName: string, key: string): Promise<void> {
    const store = this.getStore(storeName)
    store.delete(key)
  }
  
  async clear(storeName: string): Promise<void> {
    const store = this.getStore(storeName)
    store.clear()
  }
  
  // 数据版本控制
  async compareAndSet(storeName: string, key: string, expectedVersion: number, newData: any): Promise<boolean> {
    const store = this.getStore(storeName)
    const current = store.get(key) as any
    
    if (current && current.version !== expectedVersion) {
      return false
    }
    
    store.set(key, newData)
    return true
  }
}

// IPC 处理器
export function registerStorageHandlers(ipcMain: Electron.IpcMain, storageService: StorageService) {
  ipcMain.handle('storage-get', async (_, storeName: string, key?: string) => {
    try {
      const data = await storageService.get(storeName, key)
      return { success: true, data }
    } catch (error: any) {
      return { success: false, error: error.message }
    }
  })
  
  ipcMain.handle('storage-set', async (_, storeName: string, key: string, value: any) => {
    try {
      await storageService.set(storeName, key, value)
      return { success: true }
    } catch (error: any) {
      return { success: false, error: error.message }
    }
  })
  
  ipcMain.handle('storage-delete', async (_, storeName: string, key: string) => {
    try {
      await storageService.delete(storeName, key)
      return { success: true }
    } catch (error: any) {
      return { success: false, error: error.message }
    }
  })
  
  ipcMain.handle('storage-clear', async (_, storeName: string) => {
    try {
      await storageService.clear(storeName)
      return { success: true }
    } catch (error: any) {
      return { success: false, error: error.message }
    }
  })
}