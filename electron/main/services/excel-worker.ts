import xlsx from 'node-xlsx'; // Import runtime object
import fs from 'fs';
import { dialog } from 'electron'; // Keep dialog if needed for file selection in main process

export class ExcelService {

    /**
     * 解析 Excel 文件
     * @param filePath Excel 文件路径
     * @returns 解析后的数据
     */
    parseExcelFile(filePath: string): any {
        console.log(`Parsing file: ${filePath}`);
        // Note: fs.access is asynchronous, consider handling errors differently if needed
        fs.access(filePath, fs.constants.R_OK, (err) => {
            if (err) {
                console.error(`Cannot access file: ${filePath}`, err);
                // Depending on requirements, you might want to throw an error or return a specific value here
            } else {
                console.log(`File is accessible: ${filePath}`);
            }
        });
        try {
            const data = xlsx.parse(filePath);
            console.log(data);
            return data;
        } catch (error) {
            console.error(`Error parsing Excel file ${filePath}:`, error);
            throw error; // Re-throw or handle as needed
        }
    }

    /**
     * 构建 Excel 文件
     * @param data 要写入的数据
     * @param filePath 文件保存路径
     */
    buildExcelFile(data: any[], filePath: string): void { // Use imported SheetData type
        try {
            const buffer = xlsx.build(data);
            fs.writeFileSync(filePath, buffer);
            console.log(`Excel file built successfully at ${filePath}`);
        } catch (error) {
            console.error(`Error building Excel file ${filePath}:`, error);
            throw error; // Re-throw or handle as needed
        }
    }

    /**
     * 导出数据到 Excel 文件
     * @param data 要导出的对象数组
     * @param filePath 文件保存路径
     * @returns 成功状态
     */
    exportToExcel(data: any[], filePath: string): { success: boolean; error?: string } {
        try {
            if (!data || data.length === 0) {
                console.warn('No data to export.');
                return { success: true }; // Or false, depending on desired behavior for empty data
            }

            // 将对象数组转换为Excel可用的格式
            const headers = Object.keys(data[0]);
            const rows = [headers];

            data.forEach(item => {
                const row = headers.map(header => item[header]);
                rows.push(row);
            });

            const sheetData = [{
                name: '小红书笔记数据', // Sheet name
                data: rows
            }];

            this.buildExcelFile(sheetData, filePath);

            return { success: true };
        } catch (error: any) {
            console.error('导出Excel失败:', error);
            return { success: false, error: error.message };
        }
    }

    // Note: The original code had a dialog import but it wasn't used in the worker's methods.
    // If file selection dialogs are needed in the main process, they should be handled
    // in the IPC handler layer, calling methods from this service.
}