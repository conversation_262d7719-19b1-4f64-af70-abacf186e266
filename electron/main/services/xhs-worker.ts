import { XhsSpider } from "../api/xhs-spider";
import fs from "fs";
import moment from "moment";
import { Ut } from '../utils/sleep';
import { XhsImage, XhsNote } from "../api/models/note"; // Changed import path
import { downloadAndConvert, downloadFile, getImgUrlByUrl } from "../utils/img-utils";
import Store from 'electron-store';
import { getLoginWindow, getMainWindow } from "../windows";



export class XhsService {

    private spider: XhsSpider
    private store: Store
    private debouncedLogin;

    constructor() {
        this.store = new Store()
        this.spider = new XhsSpider()
        this.debouncedLogin = this.debounce(this.login, 5000);

        this.store.onDidChange('xhs-login-info', (newValue: any) => {
            // 向渲染进程发送数据变化的消息
            const loginWindow = getLoginWindow()
            if (loginWindow) {
                if (newValue?.guest) {
                    loginWindow.show();
                } else {
                    loginWindow.hide();
                }
            }
        });
    }

    async start() {
        const info: any = this.store.get('xhs-login-info');
        if (!info || info.guest) {
            await this.debouncedLogin()
        } else {
            // 更新前端用户信息显示
            const mainWindow = getMainWindow();
            if (mainWindow) {
                mainWindow.webContents.send('login-status-changed', info);
            }
        }
    }


    async login() {
        try {
            const info = await this.spider.loginInfo()
            if (info) {
                this.store.set('xhs-login-info', info)
                const mainWindow = getMainWindow();
                if (mainWindow) {
                    mainWindow.webContents.send('login-status-changed', info);
                }
            } else {
                // 如果获取信息失败，至少设置一个guest标识
                this.store.set('xhs-login-info', { guest: true })
            }
        } catch (error) {
            console.error('登录失败:', error);
            // 登录失败时设置guest模式
            this.store.set('xhs-login-info', { guest: true })
        }
    }


    debounce(func: (...args: any[]) => any, wait: number): (...args: any[]) => void { // Added type annotations
        let timeout: NodeJS.Timeout | undefined; // Added type annotation
        return function (this: any, ...args: any[]): void { // Add explicit 'this' type
            // eslint-disable-next-line @typescript-eslint/no-this-alias
            const context: any = this;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), wait);
        };
    }


    /**
     * 获取笔记对应的连接
     *
     * @param input 输入分享文字或者链接
     */
    getNoteUrl(input: string): Promise<string> {
        return new Promise(async (resolve) => {
            const url: string = input || ''
            try {
                // console.log(`文本为:${input}`)
                // if (/^http/.test(input)) {
                //     url = input
                // } else {
                //     url = /(http:\/\/xhslink.com\/\w+)，/.exec(input)[1]
                // }
                // console.log(`链接为:${url}`)
                resolve(url)
            } catch (e) {
                console.log(`解析异常`)
                resolve(url)
            }
        })
    }

    /**
     * 读取笔记图片
     *
     * @param input 输入分享文字或者链接
     */
    async readNoteImgs(input: string) {
        const item = {
            noteid: '',
            nickname: '',
            imgs: [] as any[],
            video: null as any,
            desc: '',
            type: ''
        }
        let note: XhsNote | undefined | null = undefined; // Initialize note as undefined
        try {
            note = await this.spider.getNoteByText(input)
        } catch (e) {
            console.log(e);
        }
        if (note) {
            item.noteid = note.id
            item.imgs = note.images_list ?? []; // Provide default empty array
            item.nickname = note.user.nickname ?? ''; // Provide default empty string
            item.desc = note.desc;
            item.type = note.type;
            item.video = note.video;
        }
        return item
    }


    async getNoteInfo(input: string) {
        return await this.spider.getNoteByText(input)
    }

    /**
     * 获取博主主页数据
     *
     * @param userId
     */
    async getUserInfo(userId: string) {
        return await this.spider.getUser(userId);
    }


    /**
     * 检测笔记收录情况
     *
     * @param input 笔记链接
     */
    async checkNote(noteId: string, redId: string) {
        // return await this.spider.checkNoteStatus(nodeId);
        const info: any = await this.getNoteInfo(noteId)
        // const noteId = info.id
        // const redId = info.user.red_id
        const time = info.time
        console.log(`笔记ID为:${noteId},作者小红书ID为${redId},发布日期为:${moment(time).format('YYYY-MM-DD HH:mm:ss')}}`)
        let num = 1
        let find = false
        let timeout = false
        while (num < 10 && !find && !timeout) {
            const res = await this.spider.getNotesByKeyword(redId, num, 'time_descending')
            await Ut.sleep(2000)
            if (res && res.items.length > 0) {
                console.log(`是否有下一页：${res.hasMore}`)
                for (const it of res.items) {
                    const index = res.items.indexOf(it);
                    if (it.id == noteId) {
                        console.log(`找到该笔记，该笔记已被收录`)
                        find = true
                        break
                    } else if (index == res.items.length - 1) {
                        console.log(`最后一篇笔记了`)
                        const lastInfo: any = await this.getNoteInfo(it.id)
                        if (moment(time).unix() > moment(lastInfo.time).unix()) {
                            console.log('笔记已经超时')
                            timeout = true
                        }
                        if (!res.hasMore) timeout = true
                    }
                }
            } else {
                timeout = true
            }
            //随机暂停3000~5000毫秒
            if (!timeout && !find) await Ut.sleep(Math.floor(Math.random() * 2000) + 3000)
            num++;
        }
        console.log(`返回结果：${find}`)
        return find
    }

    /**
     *  批量获取笔记
     *
     */
    async getNotesByKeyword(item: any) {
        return await this.spider.getNotesByKeyword(item.keyword, item.page, item.type)
    }

    /**
     * 检测笔记敏感词
     *
     * @param text 笔记内容
     */
    async checkNoteWord(text: string) {
        return await this.spider.checkWords(text)
    }

    async getUser(userId: string) {
        console.log(`获取${userId}博主的主页数据！`);
        return await this.getUserInfo(userId)
    }

    async checkUser() {
        // const userInfo = info.userDetail;
        // const noteInfo = info.notesDetail;
        // const redId = userInfo.redId;

        // let includeNums = 0
        // const officaRes = await this.spider.getNotesByKeyword(redId, 1, 'time_descending')
        // await Promise.all(noteInfo.map((note: any) => {
        //     const res = officaRes.list.find((item: any) => item.id == note.id)
        //     console.log(!!res);
        //     if (!!res) includeNums++
        //     note["include"] = !!res
        // }))
        // console.log(`该博主[${userInfo.redId}]最新10篇笔记的收录率为${this.getPercent(includeNums, 10)}`);
        // return {
        //     info: info,
        //     collectionRate: this.getPercent(includeNums, noteInfo.length)
        // }

    }


    getPercent(num: number, total: number) {
        num = parseFloat(num.toString());
        total = parseFloat(total.toString());
        if (isNaN(num) || isNaN(total)) {
            return "-";
        }
        return total <= 0 ? "0%" : (Math.round(num / total * 10000) / 100.00) + "%";
    }

    async getNotes() {
        // const notes = [] as any[]
        // let cursor = ''
        // let num = 1
        // let find = true
        // while (counts > notes.length && find) {
        //     const noteRes = await this.spider.getNoteByUserId(userId, cursor)
        //     if (noteRes && noteRes.notes.length > 0) {
        //         cursor = noteRes.cursor
        //         noteRes.notes.forEach((item: any) => {
        //             notes.push(item)
        //         })
        //         if (noteRes.notes.length < 10) {
        //             console.log(`该博主[${userId}]笔记获取完成`)
        //             find = false
        //         }
        //     } else {
        //         console.log(`该博主[${userId}]没有笔记`)
        //         find = false
        //     }
        //     console.log(`该博主[${userId}]当前笔记数量为：${notes.length}`)
        //     await Ut.sleep(1000)
        // }
        // return notes
    }





    // async preHeaders(url, data, isCreator, cookieDict) {
    //     let headers = {};

    //     // 同样，这里也需要实现相应的sign函数
    //     Object.assign(headers, await sign(url, data, cookieDict.a1, cookieDict.web_session));

    //     // 使用axios更新HTTP头部信息
    //     const instance = axios.create({
    //         headers: headers
    //     });

    //     return instance;
    // }

    /**
     * 初始化监听
     */
}

