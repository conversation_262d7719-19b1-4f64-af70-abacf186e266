import {
    app,
    protocol,
    ipcMain,
    dialog,
    Menu,
    BrowserWindow // Keep BrowserWindow for type checking in activate handler
} from "electron";
import installExtension, { VUEJS_DEVTOOLS } from "electron-devtools-installer";
import * as path from "path";
import Store from "electron-store"; // Keep Store if used by logout IPC directly
import { registerIpcHandlers } from "./ipcHandlers";
import {
    createMainWindow,
    createLoginWindow,
    getLoginWindow,
    handleAppActivate,
    handleToggleLoginWindow,
    handleLogout,
    saveCookiesToFile // Import saveCookiesToFile specifically
} from "./windows"; // Import window management functions
import { setAppQuitting } from "./appState"; // Import state setter

// Environment Setup (Keep as is)
process.env.ELECTRON_DISABLE_SECURITY_WARNINGS = 'true'
process.env.DIST_ELECTRON = path.join(__dirname, '..');
process.env.DIST = path.join(process.env.DIST_ELECTRON, '../dist');
const isDevelopment = !app.isPackaged;

// App Quitting Flag is now managed in appState.ts
// let isAppQuitting = false;

// Protocol Registration (Keep as is)
protocol.registerSchemesAsPrivileged([
    { scheme: "app", privileges: { secure: true, standard: true } },
]);

// --- Application Menu (Keep as is, ensure it doesn't rely on removed globals) ---
function createApplicationMenu() {
    if (process.platform === 'darwin') {
        const template = [
            {
                label: app.name,
                submenu: [
                    { role: 'about' },
                    { type: 'separator' },
                    { role: 'services' }, // Standard macOS services menu
                    { type: 'separator' },
                    { role: 'hide' },
                    { role: 'hideOthers' }, // Standard macOS hideOthers
                    { role: 'unhide' }, // Standard macOS unhide
                    { type: 'separator' },
                    // 关闭窗口
                    { role: 'close' },
                    // Use app.quit() for standard quit behavior respecting before-quit
                    { role: 'quit', label: '退出 ' + app.name, accelerator: 'Command+Q' }
                ]
            },
            {
                label: '编辑',
                submenu: [
                    { role: 'undo' },
                    { role: 'redo' },
                    { type: 'separator' },
                    { role: 'cut' },
                    { role: 'copy' },
                    { role: 'paste' },
                    { role: 'delete' },
                    { role: 'selectAll' }
                ]
            },
            { // Add View menu for standard actions
                label: '视图',
                submenu: [
                    { role: 'reload' },
                    { role: 'forceReload' },
                    { role: 'toggleDevTools' },
                    { type: 'separator' },
                    { role: 'resetZoom' },
                    { role: 'zoomIn' },
                    { role: 'zoomOut' },
                    { type: 'separator' },
                    { role: 'togglefullscreen' }
                ]
            },
            { // Add Window menu for standard actions
                label: '窗口',
                submenu: [
                    { role: 'minimize' },
                    { role: 'zoom' }, // Standard macOS zoom
                    { type: 'separator' },
                    { role: 'front' } // Standard macOS bring all to front
                ]
            }
        ];

        // Add Developer menu only in development
        if (isDevelopment) {
            const devMenu = template.find(m => m.label === '视图')?.submenu;
            if (devMenu) {
                // Already added toggleDevTools etc. in View menu
            } else {
                // Fallback if View menu structure changes
                template.push({
                    label: '开发者',
                    submenu: [
                        { role: 'reload' },
                        { role: 'forceReload' },
                        { role: 'toggleDevTools' },
                    ]
                });
            }
        }

        Menu.setApplicationMenu(Menu.buildFromTemplate(template as any));
    } else {
        // Keep default menu or null for non-macOS
        Menu.setApplicationMenu(null);
    }
}


// --- App Lifecycle Events ---

// Quit when all windows are closed (except on macOS)
app.on("window-all-closed", () => {
    console.log("All windows closed.");
    // On macOS it is common for applications and their menu bar
    // to stay active until the user quits explicitly with Cmd + Q
    if (process.platform !== 'darwin') {
        console.log("Quitting application (non-macOS).");
        app.quit();
    }
});

// Handle macOS dock icon click
app.on("activate", () => {
    console.log("Application activated.");
    // Call the handler from windows.ts
    handleAppActivate();
});

// App Ready Event
app.whenReady().then(async () => {
    console.log("App is ready.");

    // Set Dock icon on macOS
    if (process.platform === 'darwin') {
        try {
            // 使用PNG格式的图标 src/renderer/assets/logo/256.png
            const iconPath = path.join(process.cwd(), 'src/assets/logo/256.png');
            (app.dock as Electron.Dock).setIcon(iconPath);
        } catch (error) {
            console.error("Failed to set dock icon:", error);
        }
    }

    // Register IPC Handlers (Needs to be done before windows are created if preload uses them)
    // 延迟10s后注册IPC handlers
    registerIpcHandlers();

    // Create initial windows using functions from windows.ts
    try {
        await createMainWindow();
        await createLoginWindow(); // Create login window (initially hidden)
    } catch (error) {
        console.error("Failed to create initial windows:", error);
        // Handle error appropriately, maybe show a dialog and quit
        dialog.showErrorBox("Initialization Error", "Failed to create application windows. The application will now exit.");
        app.quit();
        return; // Exit early
    }


    // Create Application Menu
    createApplicationMenu();

    // Install DevTools in development
    if (isDevelopment) {
        console.log(`Development mode detected. Installing Vue Devtools...`);
        try {
            await installExtension(VUEJS_DEVTOOLS);
            console.log("Vue Devtools installed successfully.");
        } catch (e: any) {
            console.error("Vue Devtools failed to install:", e.toString());
        }
    } else {
        console.log(`Production mode detected.`);
        ipcMain.emit('check-for-updates', { isManualCheck: false });
    }
});

// Before Quit Event (Save state, etc.)
app.on('before-quit', async () => {
    console.log('Application is preparing to quit...');
    setAppQuitting(true); // Set the flag using the shared state module

    // Save login window cookies before quitting
    try {
        const lw = getLoginWindow(); // Get the login window instance
        if (lw) {
            console.log('Saving cookies from login window before quit...');
            await saveCookiesToFile(lw); // Pass the window instance
        } else {
            console.log('Login window not found, skipping cookie save.');
        }
    } catch (err) {
        console.error('Failed to save data before quit:', err);
    }
});

// Quit Event (Final cleanup)
app.on('quit', () => {
    console.log('Application quit.');
    // No need to force exit on macOS here, standard quit process should handle it
});


// --- IPC Handlers ---

// Toggle Login Window Visibility
ipcMain.on("toggle-login-window", (event, action: 'show' | 'hide', url?: string) => {
    console.log(`IPC received: toggle-login-window, action: ${action}, url: ${url}`);
    handleToggleLoginWindow(action, url); // Delegate to windows.ts handler
});

// Handle Logout Request
ipcMain.handle('logout', async (event) => { // Use handle for async operations
    console.log('IPC received: logout');
    try {
        await handleLogout(); // Delegate to windows.ts handler
        // Notify the specific renderer that initiated the logout (optional)
        // event.sender.send('logout-successful');
        // Or broadcast to all windows if needed
        BrowserWindow.getAllWindows().forEach(win => {
            win.webContents.send('login-status-changed', { guest: true });
        });
        return { success: true };
    } catch (error) {
        console.error('Logout IPC handler failed:', error);
        return { success: false, error: (error as Error).message };
    }
});


// --- Development Mode Graceful Exit ---
if (isDevelopment) {
    if (process.platform === "win32") {
        process.on("message", (data) => {
            if (data === "graceful-exit") {
                console.log("Received graceful-exit message, quitting.");
                app.quit();
            }
        });
    } else {
        process.on("SIGTERM", () => {
            console.log("Received SIGTERM, quitting.");
            app.quit();
        });
    }
}