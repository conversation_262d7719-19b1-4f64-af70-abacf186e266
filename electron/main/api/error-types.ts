/**
 * API错误处理相关的类型定义和工具类
 */

// 错误代码枚举
export enum ErrorCode {
  // 网络相关错误
  NETWORK_ERROR = 1000,
  TIMEOUT_ERROR = 1001,
  CONNECTION_ERROR = 1002,
  
  // 认证相关错误
  AUTH_FAILED = 2000,
  COOKIE_EXPIRED = 2001,
  SIGN_FAILED = 2002,
  
  // API相关错误
  API_ERROR = 3000,
  INVALID_PARAMS = 3001,
  RATE_LIMITED = 3002,
  
  // 业务相关错误
  DATA_NOT_FOUND = 4000,
  PARSE_ERROR = 4001,
  VALIDATION_ERROR = 4002,
  
  // 系统错误
  UNKNOWN_ERROR = 5000
}

// 统一的错误响应接口
export interface ApiErrorResponse {
  success: false;
  errorCode: ErrorCode;
  message: string;
  details?: any;
  timestamp: number;
  requestId?: string;
}

// 统一的成功响应接口
export interface ApiSuccessResponse<T = any> {
  success: true;
  data: T;
  timestamp: number;
  requestId?: string;
}

// 统一的API响应类型
export type ApiResponse<T = any> = ApiSuccessResponse<T> | ApiErrorResponse;

/**
 * 基础API错误类
 */
export class BaseApiError extends Error {
  public readonly errorCode: ErrorCode;
  public readonly timestamp: number;
  public readonly details?: any;
  public readonly requestId?: string;

  constructor(
    errorCode: ErrorCode,
    message: string,
    details?: any,
    requestId?: string
  ) {
    super(message);
    this.name = this.constructor.name;
    this.errorCode = errorCode;
    this.timestamp = Date.now();
    this.details = details;
    this.requestId = requestId;

    // 保持错误堆栈跟踪
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }

  /**
   * 转换为API响应格式
   */
  toApiResponse(): ApiErrorResponse {
    return {
      success: false,
      errorCode: this.errorCode,
      message: this.message,
      details: this.details,
      timestamp: this.timestamp,
      requestId: this.requestId
    };
  }
}

/**
 * 网络错误类
 */
export class NetworkError extends BaseApiError {
  constructor(message: string, details?: any, requestId?: string) {
    super(ErrorCode.NETWORK_ERROR, message, details, requestId);
  }
}

/**
 * 认证错误类
 */
export class AuthError extends BaseApiError {
  constructor(message: string, details?: any, requestId?: string) {
    super(ErrorCode.AUTH_FAILED, message, details, requestId);
  }
}

/**
 * 签名错误类
 */
export class SignatureError extends BaseApiError {
  constructor(message: string, details?: any, requestId?: string) {
    super(ErrorCode.SIGN_FAILED, message, details, requestId);
  }
}

/**
 * 数据解析错误类
 */
export class ParseError extends BaseApiError {
  constructor(message: string, details?: any, requestId?: string) {
    super(ErrorCode.PARSE_ERROR, message, details, requestId);
  }
}

/**
 * 错误处理工具类
 */
export class ErrorHandler {
  /**
   * 包装异步函数，统一处理错误
   */
  static async handleAsync<T>(
    operation: () => Promise<T>,
    defaultErrorMessage: string = '操作失败'
  ): Promise<ApiResponse<T>> {
    try {
      const data = await operation();
      return {
        success: true,
        data,
        timestamp: Date.now()
      };
    } catch (error) {
      if (error instanceof BaseApiError) {
        return error.toApiResponse();
      }

      // 处理未知错误
      console.error('未捕获的错误:', error);
      return {
        success: false,
        errorCode: ErrorCode.UNKNOWN_ERROR,
        message: error instanceof Error ? error.message : defaultErrorMessage,
        details: error,
        timestamp: Date.now()
      };
    }
  }

  /**
   * 创建成功响应
   */
  static createSuccess<T>(data: T, requestId?: string): ApiSuccessResponse<T> {
    return {
      success: true,
      data,
      timestamp: Date.now(),
      requestId
    };
  }

  /**
   * 创建错误响应
   */
  static createError(
    errorCode: ErrorCode,
    message: string,
    details?: any,
    requestId?: string
  ): ApiErrorResponse {
    return {
      success: false,
      errorCode,
      message,
      details,
      timestamp: Date.now(),
      requestId
    };
  }
}