/**
 * API配置管理模块
 * 统一管理所有API相关的配置
 */

export interface ApiConfig {
  // 基础配置
  apiHost: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
  
  // 用户代理配置
  userAgent: string;
  
  // 缓存配置
  cacheEnabled: boolean;
  cacheMaxSize: number;
  cacheTTL: number;
  
  // 安全配置
  httpsOnly: boolean;
  trustedHosts: string[];
  
  // 性能配置
  maxConcurrentRequests: number;
  scrollOptimization: {
    enabled: boolean;
    scrollDistance: number;
    scrollInterval: number;
    maxScrollTime: number;
    maxUnchangedCount: number;
  };
  
  // 调试配置
  debugMode: boolean;
  logLevel: 'error' | 'warn' | 'info' | 'debug';
}

export interface XhsApiEndpoints {
  // 用户相关
  userMe: string;
  userInfo: string;
  userProfile: string;
  
  // 笔记相关
  noteSearch: string;
  noteDetail: string;
  
  // 其他
  wordCheck: string;
}

/**
 * 默认配置
 */
const defaultConfig: ApiConfig = {
  // 基础配置
  apiHost: 'https://edith.xiaohongshu.com',
  timeout: 30000,
  retryAttempts: 3,
  retryDelay: 1000,
  
  // 用户代理配置
  userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  
  // 缓存配置
  cacheEnabled: true,
  cacheMaxSize: 500,
  cacheTTL: 5 * 60 * 1000, // 5分钟
  
  // 安全配置
  httpsOnly: true,
  trustedHosts: ['edith.xiaohongshu.com', 'www.xiaohongshu.com', 'foryet.net', 'localhost', '127.0.0.1'],
  
  // 性能配置
  maxConcurrentRequests: 5,
  scrollOptimization: {
    enabled: true,
    scrollDistance: 800,
    scrollInterval: 300,
    maxScrollTime: 10000,
    maxUnchangedCount: 3
  },
  
  // 调试配置
  debugMode: false,
  logLevel: 'info'
};

/**
 * API端点配置
 */
const defaultEndpoints: XhsApiEndpoints = {
  // 用户相关
  userMe: '/api/sns/web/v2/user/me',
  userInfo: '/api/sns/web/v1/user/otherinfo',
  userProfile: '/user/profile/{userId}',
  
  // 笔记相关
  noteSearch: '/api/sns/web/v1/search/notes',
  noteDetail: '/api/sns/web/v1/feed',
  
  // 其他
  wordCheck: '/api/actions.aspx?action=check_words_zhuanyong'
};

/**
 * 环境特定配置
 */
const environmentConfigs: Record<string, Partial<ApiConfig>> = {
  development: {
    debugMode: true,
    logLevel: 'debug',
    timeout: 60000,
    cacheEnabled: false
  },
  
  production: {
    debugMode: false,
    logLevel: 'warn',
    timeout: 30000,
    cacheEnabled: true
  },
  
  test: {
    debugMode: true,
    logLevel: 'debug',
    timeout: 10000,
    cacheEnabled: false,
    retryAttempts: 1
  }
};

/**
 * 配置管理器类
 */
export class ConfigManager {
  private config: ApiConfig;
  private endpoints: XhsApiEndpoints;
  private environment: string;

  constructor(environment: string = 'production') {
    this.environment = environment;
    this.config = this.mergeConfigs(defaultConfig, environmentConfigs[environment] || {});
    this.endpoints = { ...defaultEndpoints };
    
    // 验证配置
    this.validateConfig();
  }

  /**
   * 合并配置对象
   */
  private mergeConfigs(base: ApiConfig, override: Partial<ApiConfig>): ApiConfig {
    return {
      ...base,
      ...override,
      scrollOptimization: {
        ...base.scrollOptimization,
        ...(override.scrollOptimization || {})
      }
    };
  }

  /**
   * 验证配置有效性
   */
  private validateConfig(): void {
    const errors: string[] = [];

    if (!this.config.apiHost) {
      errors.push('API主机地址不能为空');
    }

    if (this.config.timeout <= 0) {
      errors.push('超时时间必须大于0');
    }

    if (this.config.retryAttempts < 0) {
      errors.push('重试次数不能小于0');
    }

    if (this.config.cacheMaxSize <= 0) {
      errors.push('缓存最大大小必须大于0');
    }

    if (this.config.httpsOnly && !this.config.apiHost.startsWith('https://')) {
      errors.push('启用HTTPS模式时，API主机必须使用HTTPS协议');
    }

    if (errors.length > 0) {
      throw new Error(`配置验证失败: ${errors.join(', ')}`);
    }
  }

  /**
   * 获取完整配置
   */
  getConfig(): ApiConfig {
    return { ...this.config };
  }

  /**
   * 获取特定配置项
   */
  get<K extends keyof ApiConfig>(key: K): ApiConfig[K] {
    return this.config[key];
  }

  /**
   * 设置配置项
   */
  set<K extends keyof ApiConfig>(key: K, value: ApiConfig[K]): void {
    this.config[key] = value;
    this.validateConfig();
  }

  /**
   * 批量更新配置
   */
  updateConfig(updates: Partial<ApiConfig>): void {
    this.config = this.mergeConfigs(this.config, updates);
    this.validateConfig();
  }

  /**
   * 获取API端点
   */
  getEndpoint(name: keyof XhsApiEndpoints, params?: Record<string, string>): string {
    let endpoint = this.endpoints[name];
    
    // 替换路径参数
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        endpoint = endpoint.replace(`{${key}}`, value);
      });
    }
    
    return endpoint;
  }

  /**
   * 获取完整URL
   */
  getFullUrl(endpointName: keyof XhsApiEndpoints, params?: Record<string, string>): string {
    const endpoint = this.getEndpoint(endpointName, params);
    return `${this.config.apiHost}${endpoint}`;
  }

  /**
   * 检查主机是否可信
   */
  isTrustedHost(host: string): boolean {
    return this.config.trustedHosts.includes(host);
  }

  /**
   * 获取当前环境
   */
  getEnvironment(): string {
    return this.environment;
  }

  /**
   * 是否为开发环境
   */
  isDevelopment(): boolean {
    return this.environment === 'development';
  }

  /**
   * 是否为生产环境
   */
  isProduction(): boolean {
    return this.environment === 'production';
  }

  /**
   * 获取日志配置
   */
  shouldLog(level: 'error' | 'warn' | 'info' | 'debug'): boolean {
    const levels = ['error', 'warn', 'info', 'debug'];
    const currentLevelIndex = levels.indexOf(this.config.logLevel);
    const requestedLevelIndex = levels.indexOf(level);
    
    return requestedLevelIndex <= currentLevelIndex;
  }

  /**
   * 重置为默认配置
   */
  reset(): void {
    this.config = this.mergeConfigs(defaultConfig, environmentConfigs[this.environment] || {});
    this.validateConfig();
  }

  /**
   * 导出配置为JSON
   */
  exportConfig(): string {
    return JSON.stringify({
      config: this.config,
      endpoints: this.endpoints,
      environment: this.environment
    }, null, 2);
  }

  /**
   * 从JSON导入配置
   */
  importConfig(jsonString: string): void {
    try {
      const imported = JSON.parse(jsonString);
      
      if (imported.config) {
        this.config = imported.config;
      }
      
      if (imported.endpoints) {
        this.endpoints = imported.endpoints;
      }
      
      if (imported.environment) {
        this.environment = imported.environment;
      }
      
      this.validateConfig();
    } catch (error) {
      throw new Error(`导入配置失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }
}

// 创建全局配置管理器实例
export const globalConfigManager = new ConfigManager(
  process.env.NODE_ENV || 'production'
);

// 导出常用的配置访问器
export const getConfig = () => globalConfigManager.getConfig();
export const getEndpoint = (name: keyof XhsApiEndpoints, params?: Record<string, string>) => 
  globalConfigManager.getEndpoint(name, params);
export const getFullUrl = (name: keyof XhsApiEndpoints, params?: Record<string, string>) => 
  globalConfigManager.getFullUrl(name, params);