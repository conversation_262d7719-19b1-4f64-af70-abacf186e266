/**
 * 类型映射和转换工具
 * 处理不同API格式之间的数据转换
 */

import { 
  UserInfo, 
  NoteInfo, 
  InteractionInfo, 
  ImageInfo,
  NoteUrlInfo 
} from './unified-types';
import { XhsUser, XhsNote, XhsImage } from './note';
import { XhsWebNote } from './web-types';
import { XhsUrlInfo } from './api-types';

/**
 * 将XhsUser转换为统一的UserInfo格式
 */
export function mapXhsUserToUserInfo(xhsUser: XhsUser): UserInfo {
  return {
    id: xhsUser.id || xhsUser.user_id,
    user_id: xhsUser.user_id,
    nickname: xhsUser.nickname || xhsUser.name,
    avatar: xhsUser.avatar,
    red_id: xhsUser.red_id,
    ip_location: xhsUser.ip_location,
    desc: xhsUser.desc,
    gender: xhsUser.gender,
    follows: xhsUser.follows,
    fans: xhsUser.fans,
    interaction: xhsUser.interaction,
    tags: xhsUser.tags
  };
}

/**
 * 将XhsNote转换为统一的NoteInfo格式
 */
export function mapXhsNoteToNoteInfo(xhsNote: XhsNote): NoteInfo {
  return {
    id: xhsNote.id,
    type: xhsNote.type,
    title: xhsNote.title,
    desc: xhsNote.desc,
    time: xhsNote.time,
    last_update_time: xhsNote.last_update_time,
    xsec_token: xhsNote.xsecToken,
    user: mapXhsUserToUserInfo(xhsNote.user),
    interact_info: {
      liked_count: xhsNote.liked_count,
      collected: xhsNote.collected,
      collected_count: xhsNote.collected_count,
      comment_count: xhsNote.comments_count,
      shared_count: xhsNote.shared_count
    },
    images_list: xhsNote.images_list?.map(mapXhsImageToImageInfo),
    video: xhsNote.video ? {
      id: xhsNote.video.id,
      duration: xhsNote.video.duration,
      url: xhsNote.video.url,
      thumbnail: xhsNote.video.thumbnail,
      stream: xhsNote.video.url_info_list ? {
        h264: xhsNote.video.url_info_list.map(info => ({
          master_url: info.url || '',
          backup_urls: []
        }))
      } : undefined
    } : undefined
  };
}

/**
 * 将XhsImage转换为统一的ImageInfo格式
 */
export function mapXhsImageToImageInfo(xhsImage: XhsImage): ImageInfo {
  return {
    file_id: xhsImage.file_id,
    height: xhsImage.height,
    width: xhsImage.width,
    url: xhsImage.url,
    url_default: xhsImage.url_default,
    url_pre: xhsImage.url_pre,
    trace_id: xhsImage.trace_id,
    live_photo: xhsImage.live_photo
  };
}

/**
 * 将XhsWebNote转换为统一的NoteInfo格式
 */
export function mapXhsWebNoteToNoteInfo(webNote: XhsWebNote): NoteInfo {
  return {
    id: webNote.id,
    note_id: webNote.noteCard.noteId,
    type: webNote.noteCard.type,
    title: webNote.noteCard.displayTitle,
    desc: '', // XhsWebNote中没有desc字段
    time: Date.now(), // XhsWebNote中没有time字段，使用当前时间
    xsec_token: webNote.xsecToken,
    user: {
      id: webNote.noteCard.user.userId,
      user_id: webNote.noteCard.user.userId,
      nickname: webNote.noteCard.user.nickname,
      avatar: webNote.noteCard.user.avatar
    },
    interact_info: {
      liked: webNote.noteCard.interactInfo.liked,
      liked_count: webNote.noteCard.interactInfo.likedCount,
      sticky: webNote.noteCard.interactInfo.sticky,
      collected_count: 0, // XhsWebNote中没有这些字段
      comment_count: 0,
      shared_count: 0
    },
    images_list: [{
      file_id: webNote.noteCard.cover.fileId,
      height: webNote.noteCard.cover.height,
      width: webNote.noteCard.cover.width,
      url: webNote.noteCard.cover.url,
      url_default: webNote.noteCard.cover.urlDefault,
      url_pre: webNote.noteCard.cover.urlPre,
      trace_id: webNote.noteCard.cover.traceId
    }]
  };
}

/**
 * 将api-types中的XhsUrlInfo转换为统一的NoteUrlInfo格式
 */
export function mapApiXhsUrlInfoToNoteUrlInfo(apiUrlInfo: XhsUrlInfo): NoteUrlInfo {
  return {
    noteId: apiUrlInfo.noteId,
    xsecSource: apiUrlInfo.xsecSource,
    xsecToken: apiUrlInfo.xsecToken
  };
}

/**
 * 标准化交互信息格式
 */
export function normalizeInteractionInfo(info: any): InteractionInfo {
  return {
    liked: typeof info.liked === 'boolean' ? info.liked : false,
    liked_count: typeof info.liked_count === 'string' ? 
      parseInt(info.liked_count) || 0 : info.liked_count || 0,
    collected: typeof info.collected === 'boolean' ? info.collected : false,
    collected_count: typeof info.collected_count === 'string' ? 
      parseInt(info.collected_count) || 0 : info.collected_count || 0,
    comment_count: typeof info.comment_count === 'string' ? 
      parseInt(info.comment_count) || 0 : info.comment_count || 0,
    shared_count: typeof info.shared_count === 'string' ? 
      parseInt(info.shared_count) || 0 : info.shared_count || 0,
    sticky: typeof info.sticky === 'boolean' ? info.sticky : false
  };
}

/**
 * 数据验证工具
 */
export class TypeValidator {
  static isValidNoteInfo(obj: any): obj is NoteInfo {
    return obj && 
           typeof obj.id === 'string' && 
           typeof obj.type === 'string' && 
           typeof obj.title === 'string' && 
           typeof obj.desc === 'string' &&
           typeof obj.time === 'number' &&
           obj.user && 
           obj.interact_info;
  }

  static isValidUserInfo(obj: any): obj is UserInfo {
    return obj && 
           (typeof obj.id === 'string' || typeof obj.user_id === 'string') &&
           typeof obj.nickname === 'string';
  }

  static isValidImageInfo(obj: any): obj is ImageInfo {
    return obj && 
           typeof obj.file_id === 'string' &&
           typeof obj.height === 'number' &&
           typeof obj.width === 'number';
  }
}