export interface XhsWebNote {
    id: string;
    noteCard: {
        interactInfo: {
            liked: boolean;
            likedCount: string;
            sticky: boolean;
        };
        cover: {
            urlPre: string;
            urlDefault: string;
            fileId: string;
            height: number;
            width: number;
            url: string;
            traceId: string;
            infoList: {
                imageScene: string;
                url: string;
            }[];
        };
        noteId: string;
        xsecToken: string;
        type: string;
        displayTitle: string;
        user: {
            avatar: string;
            userId: string;
            nickname: string;
            nickName: string;
        };
    };
    index: number;
    exposed: boolean;
    ssrRendered: boolean;
    xsecToken: string;
}