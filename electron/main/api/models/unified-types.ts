/**
 * 统一的类型定义文件
 * 整合所有API相关的类型定义，避免重复和冲突
 */

// 重新导出所有核心类型
export * from './note';
export * from './api-types';
export * from './web-types';

// 统一的响应类型
export interface BaseApiResponse {
  success: boolean;
  data?: any;
  error?: string;
  errorCode?: number;
  timestamp?: number;
}

// 统一的分页参数
export interface BasePaginationParams {
  page?: number;
  page_size?: number;
  cursor?: string;
  has_more?: boolean;
}

// 统一的搜索参数
export interface BaseSearchParams extends BasePaginationParams {
  keyword: string;
  sort?: string;
  filters?: Record<string, any>;
}

// URL解析结果（从api-types.ts导入的XhsUrlInfo重命名）
export interface NoteUrlInfo {
  noteId: string;
  xsecSource: string | null;
  xsecToken: string | null;
}

// 视频URL信息（从note.ts导入的XhsVideoUrlInfo）
export interface VideoUrlInfo {
  avg_bitrate?: number;
  desc?: string;
  height?: number;
  width?: number;
  url?: string;
  vmaf?: number;
}

// 图片信息的统一类型
export interface ImageInfo {
  file_id: string;
  height: number;
  width: number;
  url?: string;
  url_default?: string;
  url_pre?: string;
  trace_id?: string;
  live_photo?: boolean;
}

// 用户交互信息的统一类型
export interface InteractionInfo {
  liked?: boolean;
  liked_count: string | number;
  collected?: boolean;
  collected_count: string | number;
  comment_count: string | number;
  shared_count?: string | number;
  sticky?: boolean;
}

// 媒体流格式的统一类型
export interface MediaStreamFormat {
  master_url: string;
  backup_urls: string[];
}

export interface MediaStream {
  h264?: MediaStreamFormat[];
  h265?: MediaStreamFormat[];
  h266?: MediaStreamFormat[];
  av1?: MediaStreamFormat[];
}

// 用户基本信息的统一类型
export interface UserBasicInfo {
  id?: string;
  user_id?: string;
  nickname?: string;
  avatar?: string;
  red_id?: string;
  ip_location?: string;
  desc?: string;
  gender?: string;
}

// 用户统计信息的统一类型
export interface UserStats {
  follows?: number;
  fans?: number;
  interaction?: number;
  notes_count?: number;
}

// 用户标签的统一类型
export interface UserTag {
  name: string;
  tagType: string;
  icon?: string;
}

// 完整的用户信息类型
export interface UserInfo extends UserBasicInfo, UserStats {
  tags?: UserTag[];
}

// 笔记基本信息的统一类型
export interface NoteBasicInfo {
  id: string;
  note_id?: string;
  type: string;
  title: string;
  desc: string;
  time: number;
  last_update_time?: number;
  xsec_token?: string;
}

// 笔记媒体信息的统一类型
export interface NoteMediaInfo {
  images_list?: ImageInfo[];
  video?: {
    id?: string;
    duration?: number;
    url?: string;
    thumbnail?: string;
    stream?: MediaStream;
  };
}

// 完整的笔记信息类型
export interface NoteInfo extends NoteBasicInfo, NoteMediaInfo {
  user: UserInfo;
  interact_info: InteractionInfo;
}

// API方法的通用返回类型
export type ApiMethodResult<T> = Promise<T | null>;
export type ApiMethodResultWithError<T> = Promise<{ data: T | null; error?: string }>;

// 常用的枚举类型
export enum NoteType {
  NORMAL = 'normal',
  VIDEO = 'video'
}

export enum SortType {
  TIME_DESC = 'time_descending',
  TIME_ASC = 'time_ascending', 
  POPULARITY = 'popularity',
  RELEVANCE = 'relevance'
}

export enum ImageFormat {
  JPG = 'jpg',
  WEBP = 'webp',
  AVIF = 'avif'
}

// 工具类型
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type PartialExcept<T, K extends keyof T> = Partial<T> & Pick<T, K>;