export interface XhsNote {
    id: string;
    collected: boolean;
    collected_count: number; //收藏数
    comments_count: number;
    desc: string;
    images_list?: XhsImage[];
    last_update_time: number
    liked_count: number
    shared_count: number
    time: number
    title: string;
    type: string;
    user: XhsUser;
    video?: XhsVideo;
    xsecToken?: string;
}

export interface XhsImage {
    live_photo?: boolean
    file_id: string
    height: number
    width: number
    original?: string
    trace_id?: string
    url?: string
    info_list?: XhsImageInfo[]
    url_default?: string
    url_pre?: string
    stream?: XhsImageStream
}

// 新增视频流格式类型
export interface XhsImageStream {
    h264: VideoStreamFormat[]
    h265: VideoStreamFormat[]
    h266: VideoStreamFormat[]
    av1: VideoStreamFormat[]
}

// 新增视频流具体格式
export interface VideoStreamFormat {
    master_url: string
    backup_urls: string[]
}

export interface XhsImageInfo {
    image_scene: string,
    url: string,
}


export interface XhsUser {
    id?: string
    group_id?: string
    avatar?: string
    name?: string
    nickname?: string
    red_id?: string
    user_id?: string
    ip_location?: string
    desc?: string
    gender?: string
    follows?: number
    fans?: number
    interaction?: number
    tags?: XhsUserTag[]
}

export interface XhsUserTag {
    name: string
    tagType: string
    icon?: string

}

export interface XhsVideo {
    id?: string
    duration?: number
    first_frame?: string
    height?: number
    width?: number
    preload_size?: number
    thumbnail?: string
    thumbnail_dim?: string
    url?: string
    origin_video_key?: string
    url_info_list?: XhsVideoUrlInfo[]
}

export interface XhsVideoUrlInfo {
    avg_bitrate?: number
    desc?: string
    height?: number
    width?: number
    url?: string
    vmaf?: number
}


export interface XhsNoteItem {
    id: string;
    model_type: string;
    note_card: {
        type: string;
        display_title: string;
        user: {
            avatar: string;
            user_id: string;
            nickname: string;
            nick_name: string;
        };
        interact_info: {
            liked: boolean;
            liked_count: string;
            collected: boolean;
            collected_count: string;
            comment_count: string;
            shared_count: string;
        };
        cover: {
            height: number;
            width: number;
            url_default: string;
            url_pre: string;
        };
        image_list: {
            height: number;
            width: number;
            info_list: {
                image_scene: string;
                url: string;
            }[];
        }[];
    };
}