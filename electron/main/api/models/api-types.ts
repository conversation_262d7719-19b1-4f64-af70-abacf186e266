/**
 * API响应和请求参数相关的类型定义
 */

export interface XhsApiResponse<T> {
    success: boolean;
    data?: T;
    error?: string;
    errorCode?: number;
}

export interface XhsNoteApiResponse {
    items: Array<{
        note_card: {
            note_id: string;
            type: string;
            title: string;
            desc: string;
            time: number;
            last_update_time: number;
            image_list: Array<XhsApiImageData>;
            interact_info: {
                collected: boolean;
                collected_count: string;
                comment_count: string;
                liked_count: string;
                share_count: string;
            };
            user: {
                user_id: string;
                nickname: string;
                avatar: string;
            };
            video?: XhsApiVideoData;
        }
    }>;
    has_more: boolean;
    cursor?: string;
}

export interface XhsApiImageData {
    file_id: string;
    height: number;
    width: number;
    url_default: string;
    url_pre: string;
    trace_id?: string;
    live_photo?: boolean;
    info_list: Array<{
        image_scene: string;
        url: string;
    }>;
    stream?: {
        h264?: Array<{ master_url: string; backup_urls: string[] }>;
        h265?: Array<{ master_url: string; backup_urls: string[] }>;
        av1?: Array<{ master_url: string; backup_urls: string[] }>;
    };
}

export interface XhsApiVideoData {
    media: {
        video_id: string;
        stream?: {
            h264?: Array<{ master_url: string; backup_urls: string[] }>;
            h265?: Array<{ master_url: string; backup_urls: string[] }>;
            av1?: Array<{ master_url: string; backup_urls: string[] }>;
        };
    };
    capa: {
        duration: number;
    };
    consumer: {
        origin_video_key: string;
    };
}

export interface XhsUserApiResponse {
    basic_info?: {
        ip_location?: string;
        desc?: string;
        images?: string;
        nickname?: string;
        red_id?: string;
        gender?: string;
    };
    interactions?: Array<{
        type: string;
        count: string;
    }>;
    tags?: Array<{
        name: string;
        tagType: string;
        icon?: string;
    }>;
}

export interface XhsNotesListApiResponse {
    items: Array<any>;
    has_more: boolean;
    cursor?: string;
}

// 重新导出统一类型，避免重复定义
export type { NoteUrlInfo as XhsUrlInfo } from './unified-types'; 