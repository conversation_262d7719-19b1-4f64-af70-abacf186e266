/**
 * 小红书爬虫类，提供小红书数据的获取和处理功能
 */
import { XhsAPI } from "./xhs-api";
import { XhsWordAPI } from "./xhs-word-api";
import {
  XhsNote,
  XhsUser,
} from "./models/note"; // Changed path alias to relative path
import { QueryNotePageParams, WebXhsAPI } from "./web-xhs-api";
import {
  getNoteIdAndTokensFromText
} from "../utils/url-utils";
import {
  transformDataToXhsNote,
  transformDataToXhsUser,
} from "../utils/transform-utils";
import { XhsWebNote } from "./models/web-types";
// ErrorHandler和ParseError暂时未使用，但保留以备后续错误处理改进

/**
 * 分页模型，用于处理分页数据
 */
class PageModel {
  constructor(pageNo: number, items?: Array<any>, hasMore?: boolean) {
    this.pageNo = pageNo;
    this.items = items ?? [];
    this.hasMore = hasMore ?? false;
  }

  pageNo: number = 1;
  pageSize: number = 20;
  hasMore: boolean = false;
  items: Array<any> = [];
}

/**
 * 主要信息接口，包含用户详情和笔记详情
 */
export interface MainInfo {
  userDetail: any;
  notesDetail: any;
  albumDetail: any;
}

/**
 * 笔记信息接口，包含笔记列表和游标
 */
export interface NotesInfo {
  notes: Array<any>;
  cursor: string;
}

/**
 * 小红书爬虫类，用于获取小红书的各种数据
 */
export class XhsSpider {
  // API实例
  private api: XhsAPI;
  private webAPI: WebXhsAPI;
  private wordAPI: XhsWordAPI;

  /**
   * 构造函数，初始化API实例
   */
  constructor() {
    this.api = new XhsAPI();
    this.webAPI = new WebXhsAPI();
    this.wordAPI = new XhsWordAPI();
  }

  /**
   * 获取登录信息
   * @returns 当前登录用户的信息
   */
  async loginInfo() {
    try {
      const data = await this.webAPI.getMe();
      return data;
    } catch (error) {
      console.error("获取登录信息失败:", error);
      return null;
    }
  }

  // ===== 笔记相关方法 =====

  /**
   * 通过文本获取笔记（支持包含URL的文本）
   * @param text 包含小红书笔记URL的文本
   * @returns 解析后的笔记对象
   */
  async getNoteByText(text: string): Promise<XhsNote | null> {
    try {
      const { noteId, xsecSource, xsecToken } = await getNoteIdAndTokensFromText(text);

      if (!noteId) {
        console.error("无法从文本中提取笔记ID");
        return null;
      }

      console.log(`从链接【${text}】中提取到的noteId为【${noteId}】, xsecSource为【${xsecSource}】，xsecToken为【${xsecToken}】`);
      const resModel = await this.webAPI.getNodeById(noteId, xsecSource ?? undefined, xsecToken ?? undefined);

      if (!resModel || !resModel.items || resModel.items.length === 0) {
        console.error("获取笔记数据失败或数据为空");
        return null;
      }

      return transformDataToXhsNote(resModel);
    } catch (error) {
      console.error("获取笔记失败:", error);
      return null;
    }
  }

  /**
   * 根据笔记ID获取笔记详情
   * @param noteId 笔记ID
   * @returns 笔记详情或undefined
   */
  async getNoteDetail(noteId: string): Promise<XhsNote | undefined> {
    try {
      const resModel = await this.webAPI.getNodeById(noteId);
      if (resModel && resModel.items && resModel.items.length > 0) {
        return transformDataToXhsNote(resModel);
      }
      return undefined;
    } catch (error) {
      console.error(`获取笔记详情失败 (ID: ${noteId}):`, error);
      return undefined;
    }
  }

  /**
   * 根据ID获取笔记（旧版API）
   * @param id 笔记ID
   * @returns 笔记数据
   */
  async getNoteById(id: string) {
    try {
      const res = await this.api.callAPI(`/discovery/item/${id}`);
      return this.getNote(res);
    } catch (error) {
      console.error(`获取笔记失败 (ID: ${id}):`, error);
      return undefined;
    }
  }

  /**
   * 从HTML响应中解析笔记数据
   * @param res API响应HTML
   * @returns 解析后的笔记数据
   */
  private getNote(res: string) {
    try {
      let note = undefined;
      const s: any = /"NoteView":(.*)\}<\/script>/.exec(res);
      if (s && s[1]) {
        note = JSON.parse(s[1]);
      }
      return note;
    } catch (error) {
      console.error("解析笔记数据失败:", error);
      return undefined;
    }
  }

  /**
   * 检测笔记收录情况
   * @param _nodeId 笔记ID（暂未使用）
   * @param _redId 用户ID（暂未使用）
   * @returns 收录状态
   */
  async checkNoteStatus(_nodeId: string, _redId: string) {
    // TODO: 实现笔记收录检测逻辑
    // 当前只返回false，未实现具体逻辑
    return false;
  }

  /**
   * 根据关键词搜索笔记
   * @param kw 关键词
   * @param pageNo 页码
   * @param sort 排序方式
   * @param postType 帖子类型
   * @param hotWord 热词
   * @returns 分页笔记数据
   */
  async getNotesByKeyword(
    kw: string,
    pageNo: number = 1,
    sort?: string,
    _postType?: string,
    _hotWord?: string
  ): Promise<PageModel> {
    try {
      const ret = await this.webAPI.getNoteByKw(
        new QueryNotePageParams({ keyword: kw, page: pageNo, sort: sort })
      );

      if (ret) {
        return new PageModel(pageNo, ret.items, ret.has_more);
      } else {
        return new PageModel(pageNo);
      }
    } catch (error) {
      console.error(`搜索笔记失败 (关键词: ${kw}):`, error);
      return new PageModel(pageNo);
    }
  }

  // ===== 用户相关方法 =====

  /**
   * 获取用户详情
   * @param id 用户ID
   * @returns 用户详情数据
   */
  async getUserDetail(id: string) {
    try {
      const info: MainInfo = await this.getUserResInfo(id);
      let userDetail = undefined;
      if (info) userDetail = info.userDetail;
      return userDetail;
    } catch (error) {
      console.error(`获取用户详情失败 (ID: ${id}):`, error);
      return undefined;
    }
  }

  /**
   * 获取用户最新笔记
   * @param id 用户ID
   * @returns 用户笔记数据
   */
  async getUserNewNotes(id: string) {
    try {
      const info: MainInfo = await this.getUserResInfo(id);
      let notes = undefined;
      if (info) notes = info.notesDetail;
      return notes;
    } catch (error) {
      console.error(`获取用户笔记失败 (ID: ${id}):`, error);
      return undefined;
    }
  }

  /**
   * 获取用户资源信息
   * @param id 用户ID
   * @returns 用户主要信息
   */
  async getUserResInfo(id: string): Promise<MainInfo> {
    let res: any = undefined;
    try {
      const ret = await this.api.callAPI(`/user/profile/${id}`);
      const arr = /window.__INITIAL_SSR_STATE__=\{"RedAppLayout":undefined,"Main":(\{.*\})\}<\/script>/.exec(ret);

      if (arr && arr.length > 0) {
        res = arr[1];
        res = JSON.parse(res);
      }
    } catch (e) {
      console.error(`获取博主详情异常！`, e);
    }
    return res;
  }

  /**
   * 获取用户信息
   * @param id 用户ID
   * @returns 转换后的用户对象
   */
  async getUser(id: string): Promise<XhsUser | null> {
    try {
      const data = await this.webAPI.getUser2(id);
      return transformDataToXhsUser(id, data);
    } catch (error) {
      console.error(`获取用户信息失败 (ID: ${id}):`, error);
      return null;
    }
  }

  /**
   * 获取用户笔记列表
   * @param userId 用户ID
   * @param cursor 游标，用于分页
   * @returns 用户笔记列表
   */
  async getUserWebNotes(userId: string, cursor = ""): Promise<XhsWebNote[]> {
    try {
      console.log(`获取博主${userId}的笔记列表`);
      return await this.webAPI.getUserNotes(userId, cursor);;
    } catch (error) {
      console.error(`获取博主${userId}的笔记列表失败:`, error);
      return [];
    }
  }

  /**
   * 获取用户笔记列表和用户信息（优化版本，一次请求获取两种数据）
   * @param userId 用户ID
   * @param cursor 游标，用于分页
   * @returns 包含笔记列表和用户信息的对象
   */
  async getUserWebNotesWithUserInfo(userId: string, cursor = ""): Promise<{ notes: XhsWebNote[], userInfo: XhsUser | null }> {
    try {
      console.log(`获取博主${userId}的笔记列表和用户信息`);
      const result = await this.webAPI.getUserNotesWithUserInfo(userId, cursor);

      // 转换用户信息格式
      let transformedUserInfo: XhsUser | null = null;
      if (result.userInfo) {
        transformedUserInfo = transformDataToXhsUser(userId, result.userInfo);
      }

      return {
        notes: result.notes,
        userInfo: transformedUserInfo
      };
    } catch (error) {
      console.error(`获取博主${userId}的笔记列表和用户信息失败:`, error);
      return { notes: [], userInfo: null };
    }
  }

  // ===== 其他方法 =====

  /**
   * 检查文本敏感词
   * @param text 待检查的文本
   * @returns 检查结果
   */
  async checkWords(text: string) {
    try {
      return await this.wordAPI.checkWords(text);
    } catch (error) {
      console.error("检查敏感词失败:", error);
      return null;
    }
  }

  // 废弃方法已移除，请使用 transform-utils.ts 中的 getFirstAvailableVideoUrl 方法
}
