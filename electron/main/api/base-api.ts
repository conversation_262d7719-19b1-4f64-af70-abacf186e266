/**
 * 统一的API基类
 * 提供通用的API调用功能和错误处理
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { globalConfigManager, ApiConfig } from './config-manager';
import { globalCache, CacheNamespaces } from './cache-manager';
import { 
  BaseApiError, 
  NetworkError, 
  ErrorHandler, 
  ApiResponse,
  ErrorCode 
} from './error-types';

export interface RequestOptions {
  // 基础选项
  timeout?: number;
  retries?: number;
  
  // 缓存选项
  useCache?: boolean;
  cacheNamespace?: string;
  cacheKey?: string;
  cacheTTL?: number;
  
  // 其他选项
  skipErrorHandler?: boolean;
  customHeaders?: Record<string, string>;
}

/**
 * API基类
 */
export abstract class BaseAPI {
  protected config: ApiConfig;
  protected axios: AxiosInstance;
  private requestCounter: number = 0;

  constructor() {
    this.config = globalConfigManager.getConfig();
    this.axios = this.createAxiosInstance();
  }

  /**
   * 创建Axios实例
   */
  private createAxiosInstance(): AxiosInstance {
    const instance = axios.create({
      baseURL: this.config.apiHost,
      timeout: this.config.timeout,
      headers: {
        'User-Agent': this.config.userAgent,
        'Content-Type': 'application/json;charset=UTF-8'
      }
    });

    // 请求拦截器
    instance.interceptors.request.use(
      (config) => {
        const requestId = ++this.requestCounter;
        config.metadata = { requestId, startTime: Date.now() };
        
        if (this.config.debugMode) {
          console.log(`[API Request ${requestId}] ${config.method?.toUpperCase()} ${config.url}`);
        }
        
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    instance.interceptors.response.use(
      (response) => {
        const { requestId, startTime } = response.config.metadata || {};
        const duration = Date.now() - (startTime || 0);
        
        if (this.config.debugMode) {
          console.log(`[API Response ${requestId}] ${response.status} (${duration}ms)`);
        }
        
        return response;
      },
      (error) => {
        const { requestId, startTime } = error.config?.metadata || {};
        const duration = Date.now() - (startTime || 0);
        
        if (this.config.debugMode) {
          console.error(`[API Error ${requestId}] ${error.message} (${duration}ms)`);
        }
        
        return Promise.reject(error);
      }
    );

    return instance;
  }

  /**
   * 发送HTTP请求的通用方法
   */
  protected async request<T = any>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    url: string,
    data?: any,
    options: RequestOptions = {}
  ): Promise<ApiResponse<T>> {
    const {
      timeout = this.config.timeout,
      retries = this.config.retryAttempts,
      useCache = this.config.cacheEnabled,
      cacheNamespace = 'default',
      cacheKey,
      cacheTTL = this.config.cacheTTL,
      skipErrorHandler = false,
      customHeaders = {}
    } = options;

    // 生成缓存键
    const finalCacheKey = cacheKey || this.generateCacheKey(method, url, data);

    // 检查缓存（仅GET请求）
    if (method === 'GET' && useCache) {
      const cached = globalCache.get<T>(cacheNamespace, finalCacheKey);
      if (cached) {
        if (this.config.debugMode) {
          console.log(`[Cache Hit] ${url}`);
        }
        return ErrorHandler.createSuccess(cached);
      }
    }

    // 执行请求
    return await this.executeRequestWithRetry<T>(
      method,
      url,
      data,
      {
        timeout,
        retries,
        useCache: method === 'GET' && useCache,
        cacheNamespace,
        cacheKey: finalCacheKey,
        cacheTTL,
        skipErrorHandler,
        customHeaders
      }
    );
  }

  /**
   * 带重试的请求执行
   */
  private async executeRequestWithRetry<T>(
    method: string,
    url: string,
    data: any,
    options: RequestOptions & { cacheKey: string }
  ): Promise<ApiResponse<T>> {
    const { retries = 0, skipErrorHandler = false } = options;
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const result = await this.executeRequest<T>(method, url, data, options);
        
        // 缓存成功的结果
        if (options.useCache && result.success) {
          globalCache.set(
            options.cacheNamespace || 'default',
            options.cacheKey,
            result.data,
            options.cacheTTL
          );
        }
        
        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        if (attempt < retries) {
          const delay = this.config.retryDelay * Math.pow(2, attempt); // 指数退避
          await this.sleep(delay);
          
          if (this.config.debugMode) {
            console.log(`[Retry ${attempt + 1}/${retries}] ${url} after ${delay}ms`);
          }
        }
      }
    }

    // 所有重试都失败了
    if (skipErrorHandler) {
      throw lastError;
    }

    return ErrorHandler.createError(
      ErrorCode.NETWORK_ERROR,
      lastError?.message || '请求失败',
      { url, method, attempts: retries + 1 }
    );
  }

  /**
   * 执行单次请求
   */
  private async executeRequest<T>(
    method: string,
    url: string,
    data: any,
    options: RequestOptions
  ): Promise<ApiResponse<T>> {
    const config: AxiosRequestConfig = {
      method: method as any,
      url,
      timeout: options.timeout,
      headers: options.customHeaders
    };

    if (method === 'GET' && data) {
      config.params = data;
    } else if (method !== 'GET') {
      config.data = data;
    }

    try {
      const response: AxiosResponse<T> = await this.axios(config);
      return ErrorHandler.createSuccess(response.data);
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.response) {
          // HTTP错误响应
          throw new NetworkError(
            `HTTP ${error.response.status}: ${error.response.statusText}`,
            {
              status: error.response.status,
              statusText: error.response.statusText,
              data: error.response.data
            }
          );
        } else if (error.request) {
          // 网络错误
          throw new NetworkError('网络连接失败，请检查网络设置');
        } else {
          // 请求配置错误
          throw new NetworkError(`请求配置错误: ${error.message}`);
        }
      }
      
      throw error;
    }
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(method: string, url: string, data?: any): string {
    const dataStr = data ? JSON.stringify(data) : '';
    return `${method}_${url}_${dataStr}`;
  }

  /**
   * 休眠函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * GET请求简化方法
   */
  protected async get<T = any>(
    url: string,
    params?: any,
    options?: RequestOptions
  ): Promise<ApiResponse<T>> {
    return this.request<T>('GET', url, params, options);
  }

  /**
   * POST请求简化方法
   */
  protected async post<T = any>(
    url: string,
    data?: any,
    options?: RequestOptions
  ): Promise<ApiResponse<T>> {
    return this.request<T>('POST', url, data, options);
  }

  /**
   * PUT请求简化方法
   */
  protected async put<T = any>(
    url: string,
    data?: any,
    options?: RequestOptions
  ): Promise<ApiResponse<T>> {
    return this.request<T>('PUT', url, data, options);
  }

  /**
   * DELETE请求简化方法
   */
  protected async delete<T = any>(
    url: string,
    options?: RequestOptions
  ): Promise<ApiResponse<T>> {
    return this.request<T>('DELETE', url, undefined, options);
  }

  /**
   * 清理特定命名空间的缓存
   */
  protected clearCache(namespace?: string): void {
    if (namespace) {
      globalCache.clearNamespace(namespace);
    } else {
      globalCache.clear();
    }
  }

  /**
   * 更新配置
   */
  protected updateConfig(updates: Partial<ApiConfig>): void {
    globalConfigManager.updateConfig(updates);
    this.config = globalConfigManager.getConfig();
    this.axios = this.createAxiosInstance();
  }

  /**
   * 日志记录方法
   */
  protected log(level: 'error' | 'warn' | 'info' | 'debug', message: string, ...args: any[]): void {
    if (globalConfigManager.shouldLog(level)) {
      console[level](`[${this.constructor.name}] ${message}`, ...args);
    }
  }
}

// 扩展Axios配置以支持元数据
declare module 'axios' {
  interface AxiosRequestConfig {
    metadata?: {
      requestId: number;
      startTime: number;
    };
  }
}