import axios, { AxiosInstance, AxiosRequestConfig } from 'axios'; // Import axios
import { CookieJar } from 'tough-cookie'; // Import CookieJar
import { WdjProxy } from "../../main/utils/wdj-proxy";
import { getUa } from "../../main/utils/header-utils";
const { wrapper } = require('axios-cookiejar-support');

// 内联一个简单的加密函数，作为临时替代
function generateCanvasId(text: string): string {
    // 简单的替代函数，生成一个基于时间戳和随机数的ID
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 1000000).toString();
    return timestamp + random;
}

export class XhsAPI {

    api_host: string;

    proxy: WdjProxy | undefined

    ua: string | undefined = undefined

    private cookieJar: CookieJar; // Use CookieJar type
    private axiosInstance: AxiosInstance; // Axios instance

    constructor(host: string = 'www.xiaohongshu.com', proxy?: WdjProxy) {
        this.api_host = host;
        this.proxy = proxy;

        this.cookieJar = new CookieJar(); // Initialize CookieJar
        this.axiosInstance = wrapper(axios.create({ // Create axios instance with wrapper
            baseURL: `https://${this.api_host}`, // Set base URL
            headers: {
                'User-Agent': this.ua, // Initial UA, will be updated in initHeader
            },
            // proxy configuration will be added in callAPI if needed
        }));
    }

    async initHeader() {
        this.ua = getUa();
        // Update UA in axios instance headers
        this.axiosInstance.defaults.headers.common['User-Agent'] = this.ua;
        await this.initCookie();
    }

    public clearCookie() {
        // Clear cookies from the jar
        this.cookieJar = new CookieJar();
        this.axiosInstance = wrapper(axios.create({ // Recreate axios instance with new jar
            baseURL: `https://${this.api_host}`,
            headers: {
                'User-Agent': this.ua,
            },
            // proxy configuration will be added in callAPI if needed
        }));
    }

    public async initCookie(): Promise<void> {
        try {
            // Use axios instance to get initial cookies
            await this.axiosInstance.get('/');

            // Use axios instance to register canvas
            const canvasId = generateCanvasId(this.ua || ''); // 使用新的替代函数
            await this.axiosInstance.post('/fe_api/burdock/v2/shield/registerCanvas?p=cc', {
                id: canvasId,
                sign: this.ua,
            });

        } catch (error: any) {
            console.error('Error initializing cookies:', error);
            throw error; // Re-throw the error
        }
    }


    public async callAPI(path: string, method: string = 'GET', data?: any): Promise<any> { // Added data parameter for POST/PUT
        if (!this.ua) { // Check if headers are initialized
            await this.initHeader();
        }

        const url = path.includes('http') ? path : `https://${this.api_host}${path}`;

        const config: AxiosRequestConfig = {
            method: method,
            url: url,
            headers: {
                // User-Agent and Cookie are handled by the axios instance with cookiejar support
            },
            data: data, // Include data for POST/PUT
        };

        if (this.proxy) { // Add proxy configuration if proxy exists
            const proxyIp = await this.proxy.getIp();
            const [host, port] = proxyIp.split(':');
            config.proxy = {
                host: host,
                port: parseInt(port),
            };
        }

        try {
            const response = await this.axiosInstance(config);
            // Assuming the API returns data in response.data
            if (response.data && response.data.error) {
                throw new Error(response.data.error.message || 'API Error');
            }
            return response.data;
        } catch (error: any) {
            console.error(`Error calling API ${url}:`, error);
            throw error; // Re-throw the error
        }
    }


}
