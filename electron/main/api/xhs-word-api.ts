import axios from "axios";
import { getUa } from "../utils/header-utils";


export class XhsWordAPI {

    api_host: string;
    private readonly TRUSTED_HOSTS = ['foryet.net', 'localhost', '127.0.0.1']; // 可信任的API主机列表

    ua: string | undefined = undefined

    constructor(host: string = 'foryet.net') {
        // 安全检查：验证API主机是否在可信任列表中
        if (!this.TRUSTED_HOSTS.includes(host)) {
            console.warn(`警告：API主机 ${host} 不在可信任列表中`);
        }
        this.api_host = host
    }

    async initHeader() {
        this.ua = getUa()
    }


    public async checkWords(text: string) {
        // 安全检查：确保使用HTTPS协议
        const protocol = this.api_host.startsWith('localhost') || this.api_host.startsWith('127.0.0.1') ? 'http' : 'https';
        
        return new Promise((resolve, reject) => {
            axios.post(`${protocol}://${this.api_host}/api/actions.aspx?action=check_words_zhuanyong`, {
                'words_type[]': [0, 1, 2, 3, 4],
                'check_text': text
            }).then(res => {
                resolve(res.data)
            }).catch(err => {
                console.error('敏感词检测失败:', err.message);
                reject(new Error(`敏感词检测服务不可用: ${err.message}`));
            })
        })
    }
}
