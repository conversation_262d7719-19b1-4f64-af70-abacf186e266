// 在文件最顶部添加（确保在所有其他导入之前）
import { getSearchId } from "../utils/img-utils";
import { signCommon } from "../utils/code-utils";
import axios from "axios";
import { getLoginWindow } from "../windows";
import { XhsWebNote } from "./models/web-types";
import { SignatureError, NetworkError, ErrorHandler, ApiResponse } from "./error-types";
import { globalCache, CacheNamespaces } from "./cache-manager";

// 定义错误枚举
const ErrorEnum = {
  IP_BLOCK: { code: 300012, msg: "网络连接异常，请检查网络设置或重启试试" },
  SIGN_FAULT: {
    code: 300015,
    msg: "浏览器异常，请尝试关闭/卸载风险插件或重启试试！",
  },
  // ...其他错误
};

// 自定义错误类
class DataFetchError extends Error {
  constructor(message: any) {
    super(message);
    this.name = "DataFetchError";
  }
}

class IPBlockError extends Error {
  constructor(message: any) {
    super(message);
    this.name = "IPBlockError";
  }
}

class SignError extends Error {
  constructor(message: any) {
    super(message);
    this.name = "SignError";
  }
}

export class QueryNotePageParams {
  keyword: string = '';
  image_formats?: string[] = ["jpg", "webp", "avif"];
  page?: number = 1;
  page_size?: number = 20;
  search_id?: string = "";
  sort?: string = "time_descending";
  note_type?: number = 0;

  constructor(params: Partial<QueryNotePageParams> = {}) {
    Object.assign(this, params);
  }
}

export class WebXhsAPI {
  private apiHost: string = "https://edith.xiaohongshu.com";

  // 构造函数
  constructor() { }

  // {"s0":3,"s1":"","x0":"1","x1":"3.6.8","x2":"Mac OS","x3":"xhs-pc-web","x4":"4.0.5","x5":"18ce857cd639ne2hpe1ufs0p64x4y0cklwbpkkhhl30000126520","x6":1705835178527,"x7":"XYW_eyJzaWduU3ZuIjoiNTEiLCJzaWduVHlwZSI6IngxIiwiYXBwSWQiOiJ4aHMtcGMtd2ViIiwic2lnblZlcnNpb24iOiIxIiwicGF5bG9hZCI6IjUxZDU3YTljNTdkOGQ1NTVlYTQ0ZGUyMjY4OGNlMGZiYzk5NTVkZTc3MDk4ZTFjYzQ0Zjk1MWNkNGRiYTQ4OTJjOGE3YTc2OWYwZDhmMTk5NWIzN2YzZTNiMzljOWE3YWM5ZTNiZmRhMWZhYTFlYjkwZDc0YWEzMWI1NGM3MmNkMGQ3NGFhMzFiNTRjNzJjZGFjNDg5YjlkYThjZTVlNDhmNGFmYjlhY2ZjM2VhMjZmZTBiMjY2YTZiNGNjM2NiNTY1NTVlMzYyNjQ5YmNlODk2NGI3ODEwNWQ1YWY1ZDk1ZTRjYmQ3MmQ4YWI5NjYxYWUwZDdmOWJlOGI5MTFiMzBjNGJhYmZmYTUxYjBiNjRhYmU1ZjcyNjZhYTUzMzI1OWQwNWVlYWEwMWZjZDBlYWFmZTRmOTc5M2MxMTVlNzkxZGYzNzJjZWZmY2M1YzY4NjE3MTA1NGQwNWI5YzVhZGEyYmM5NWZhMDIzZTI5ZjczN2YxOTQ5NGU1ZmE2NmRlMCJ9","x8":"I38rHdgsjopgIvesdVwgIC+oIELmBZ5e3VwXLgFTIxS3bqwErFeexd0ekncAzMFYnqthIhJeSfMDKutRI3KsYorWHPtGrbV0P9WfIi/eWc6eYqtyQApPI37ekmR1QL+5Ii6sdnoeSfqYHqwl2qt5BfqJIvFbNLQ+ZPw7Ixdsxuwr4qtkIkrwIi/skZc3ICLdI3Oe0utl2ADZsL5eDSJsSPwXIEvsiVtJOPw8BuwfPpdeTDWOIx4VIiu6ZPwbJqt0IxHyoMAeVutWIvvs1utBIkTKIimrzf7sY9GdZ0vsYutmor/e0lD9oooeYlqZI3geSuweIEesjVtIolT0OoVGIvgeiqtR/Ygexdp8IhTpIEzqHocdI3kK4z8gIiifpVwAICrVJo3eDbvekVtlIx3s0U6sfgu4IENs6DZ3yutiIkos1ovskY/skVw2enNsSPwVIC+ssA7eSuwkggKsDqwsI3E/I3k1oVwNauwyrUV2qbesjaoskVt+IkeeDj5s6bWWIkgsjpes3nVMIkoeTVtDIkGFOVwPQVtFI3eeiVtdIkKsVuwiIEvsfVtUtPw8sqw5IvrhI3HgGBhD4Ige3PtlIhI4IiOedqwzZZktBUvsjAQDIk8+NqwLcVtRIvosYuw+tIos1SvekrGNIEJeiI==","x9":1037368187,"x10":24}

  async sign2(uri: string, params: any = undefined) {
    // 检查全局loginWindow是否存在
    const loginWindow = getLoginWindow();
    if (!loginWindow) {
      console.warn('登录窗口未初始化，签名无法完成');
      // 返回一个空对象作为签名
      return {};
    }

    const hiddenWindow = loginWindow;
    return new Promise(async (resolve) => {
      try {
        // 确保窗口的webContents存在
        if (!hiddenWindow.webContents) {
          console.warn('登录窗口webContents未初始化，签名无法完成');
          resolve({});
          return;
        }

        const cookies = await hiddenWindow.webContents.session.cookies.get({
          url: "https://www.xiaohongshu.com",
        });
        // 从cookies中获取a1的值
        const a1Cookie = cookies.find((cookie) => cookie.name === "a1");
        if (!a1Cookie) {
          console.warn('未找到a1 cookie，可能未登录');
          resolve({});
          return;
        }

        const a1 = a1Cookie.value;
        const cookie_str = cookies
          .map((cookie) => `${cookie.name}=${cookie.value}`)
          .join(";");
        // 安全改进：不输出敏感参数到日志
        console.log(`正在生成请求签名参数...`);

        const encryptParams = await hiddenWindow.webContents.executeJavaScript(
          `window._webmsxyw("${uri}", ${params ? JSON.stringify(params) : undefined
          });`
        );

        // 安全改进：不输出完整的加密参数到日志
        console.log(`签名参数生成${encryptParams ? '成功' : '失败'}`);
        // hiddenWindow.close(); // 不关闭窗口，保持登录状态

        // 检查encryptParams是否有效
        if (!encryptParams || !encryptParams["X-s"] || !encryptParams["X-t"]) {
          console.warn('获取X-s和X-t失败');
          resolve({});
          return;
        }

        const x_s = encryptParams["X-s"];
        const x_t = encryptParams["X-t"];

        // 安全地获取b1和b1b1
        let b1 = "";
        let b1b1 = "1";
        try {
          b1 = await hiddenWindow.webContents.executeJavaScript(
            `localStorage.getItem("b1")`
          ) || "";
          b1b1 = await hiddenWindow.webContents.executeJavaScript(
            `localStorage.getItem("b1b1")`
          ) || "1";
        } catch (error) {
          console.warn('获取b1和b1b1失败:', error);
        }

        const x_s_common = signCommon(a1, x_s, x_t, b1, b1b1);

        let signObj = {};
        if (uri.includes("/feed")) {
          signObj = {
            "x-s": encryptParams["X-s"],
            "x-t": encryptParams["X-t"],
            "X-S-Common": x_s_common,
            cookie: cookie_str,
          };
        } else {
          signObj = {
            "x-s": encryptParams["X-s"],
            "x-t": encryptParams["X-t"],
            cookie: cookie_str,
          };
        }
        resolve(signObj);
      } catch (error) {
        console.error("签名生成失败:", error);
        // 返回包含错误信息的对象，而不是undefined
        resolve({ 
          error: true, 
          message: error instanceof Error ? error.message : '未知签名错误',
          timestamp: Date.now()
        });
      }
    });
  }

  async getNoteByKw(params: QueryNotePageParams): Promise<any> {
    const uri = "/api/sns/web/v1/search/notes";
    params.search_id = getSearchId();
    console.log(`getNoteByKw - params:${JSON.stringify(params)}`);

    return await this.callAPI("POST", uri, params);
  }

  async getNodeById(id: string, xsec_source = '', xsec_token = ''): Promise<any> {
    // const uri = "/api/sns/web/v1/search/notes"
    // const params = {
    //     "keyword": '724660549',
    //     "image_formats": ["jpg", "webp", "avif"],
    //     "page": 1,
    //     "page_size": 20,
    //     "search_id": '2COM0I6TBHEZPPDF5Z6CQ',
    //     "sort": 'time_descending',
    //     "note_type": 0,
    // }

    const params = {
      source_note_id: id,
      // "image_scenes": ["CRD_WM_WEBP"]
      image_formats: ["jpg", "webp", "avif"],
      extra: { need_body_topic: "1" },
      xsec_source: xsec_source,
      xsec_token: xsec_token
    };
    const uri = "/api/sns/web/v1/feed";
    return await this.callAPI("POST", uri, params);
  }

  async getMe(): Promise<any> {
    const uri = "/api/sns/web/v2/user/me";
    return await this.callAPI("GET", uri, undefined);
  }

  async getUser(id: string): Promise<any> {
    // 检查缓存
    const cached = globalCache.get(CacheNamespaces.USER_INFO, id);
    if (cached) {
      console.log(`从缓存获取用户信息: ${id}`);
      return cached;
    }

    const uri = "/api/sns/web/v1/user/otherinfo";
    const params = { target_user_id: id };
    const result = await this.callAPI("GET", uri, params);
    
    // 缓存结果
    if (result) {
      globalCache.set(CacheNamespaces.USER_INFO, id, result, 10 * 60 * 1000); // 10分钟缓存
    }
    
    return result;
  }

  // get user2 by id
  async getUser2(id: string): Promise<any> {
    const userUrl = `https://www.xiaohongshu.com/user/profile/${id}`;
    console.log(`开始加载用户主页：${userUrl}`);

    // 通过hiddenWindow加载用户主页
    const hiddenWindow = getLoginWindow();
    if (!hiddenWindow) {
      console.warn('登录窗口未初始化，获取用户信息失败');
      return {};
    }
    return new Promise((resolve, reject) => {
      // 设置超时处理
      const timeout = setTimeout(() => {
        hiddenWindow.webContents.removeAllListeners("did-finish-load");
        hiddenWindow.webContents.removeAllListeners("did-fail-load");
        reject(new Error('加载用户页面超时'));
      }, 30000); // 30秒超时

      // 加载完成事件处理
      const handleLoadFinish = async () => {
        try {
          clearTimeout(timeout);
          hiddenWindow.webContents.removeAllListeners("did-finish-load");
          hiddenWindow.webContents.removeAllListeners("did-fail-load");
          
          console.log(`did-finish-load, 用户主页加载完成`);
          const user = await hiddenWindow.webContents.executeJavaScript(
            `JSON.stringify(window.__INITIAL_STATE__.user.userPageData._rawValue)`
          );
          console.log(`user: ${user}`);
          resolve(JSON.parse(user));
        } catch (error) {
          reject(error);
        }
      };

      // 加载失败事件处理
      const handleLoadFail = (_event: any, errorCode: number, errorDescription: string) => {
        clearTimeout(timeout);
        hiddenWindow.webContents.removeAllListeners("did-finish-load");
        hiddenWindow.webContents.removeAllListeners("did-fail-load");
        reject(new Error(`加载失败: ${errorDescription} (${errorCode})`));
      };

      // 注册事件监听器
      hiddenWindow.webContents.once("did-finish-load", handleLoadFinish);
      hiddenWindow.webContents.once("did-fail-load", handleLoadFail);
      
      // 开始加载页面
      hiddenWindow.loadURL(userUrl);
    });
  }

  /**
   * 获取用户笔记列表
   *
   * @param userId 用户ID
   * @param cursor 下标
   */
  async getUserNotes(userId: string, cursor = ""): Promise<XhsWebNote[]> {
    const result = await this.getUserNotesWithUserInfo(userId, cursor);
    return result.notes;
  }

  async getUserNotesWithUserInfo(userId: string, _cursor = ""): Promise<{ notes: XhsWebNote[], userInfo: any }> {
    const userUrl = `https://www.xiaohongshu.com/user/profile/${userId}`;
    console.log(`开始加载用户主页获取笔记和用户信息：${userUrl}`);

    // 通过hiddenWindow加载用户主页
    const hiddenWindow = getLoginWindow();
    if (!hiddenWindow) {
      console.warn('登录窗口未初始化，获取用户笔记失败');
      return { notes: [], userInfo: null };
    }

    try {
      hiddenWindow.show();
      await hiddenWindow.loadURL(userUrl);
      console.log(`用户主页加载完成，开始滚动加载全部笔记`);

      // 优化的自动滚动加载策略
      await hiddenWindow.webContents.executeJavaScript(`
        new Promise((resolve) => {
          let lastScrollHeight = 0;
          let unchangedCount = 0;
          const maxUnchangedCount = 3; // 连续3次高度不变就停止
          const scrollDistance = 800; // 减少滚动距离，更平滑
          const scrollInterval = 300; // 减少滚动间隔
          const maxScrollTime = 10000; // 最大滚动时间10秒
          
          const startTime = Date.now();
          
          const scrollTimer = setInterval(() => {
            const currentScrollHeight = document.body.scrollHeight;
            const currentTime = Date.now();
            
            // 检查是否已到达最大时间
            if (currentTime - startTime > maxScrollTime) {
              clearInterval(scrollTimer);
              resolve();
              return;
            }
            
            // 检查页面高度是否变化
            if (currentScrollHeight === lastScrollHeight) {
              unchangedCount++;
              if (unchangedCount >= maxUnchangedCount) {
                clearInterval(scrollTimer);
                setTimeout(resolve, 500); // 短暂等待确保数据加载完成
                return;
              }
            } else {
              unchangedCount = 0;
              lastScrollHeight = currentScrollHeight;
            }
            
            // 平滑滚动
            window.scrollBy(0, scrollDistance);
          }, scrollInterval);
        });
      `);

      console.log(`滚动加载完成，开始提取笔记数据`);

      // 提取笔记数据和用户信息
      const result = await hiddenWindow.webContents.executeJavaScript(`
        (function() {
          try {
            const state = window.__INITIAL_STATE__;
            if (!state) {
              console.log("找不到__INITIAL_STATE__");
              return { notes: [], userInfo: null };
            }

            // 提取用户信息
            let userInfo = null;
            try {
              const userPageData = state.user?.userPageData?._rawValue;
              if (userPageData) {
                userInfo = userPageData;
                console.log("成功提取用户信息");
              }
            } catch (userErr) {
              console.warn("提取用户信息时出错:", userErr);
            }

            // 提取笔记数据
            const notesRaw = state.user?.notes?._rawValue || [];
            console.log("原始笔记数据:", JSON.stringify(notesRaw.slice(0, 2)));  // 只记录前两条笔记以避免日志过大
            
            let notes = [];
            if (Array.isArray(notesRaw) && notesRaw.length > 0) {
              notes = notesRaw[0].map(note => {
                if (!note || !note.noteCard) {
                  console.log("发现无效笔记项:", note);
                  return null;
                }
                return note;
              }).filter(note => note !== null);
            } else {
              console.log("未找到笔记数据或数据不是数组");
            }

            return { notes, userInfo };
          } catch (err) {
            console.error("提取数据时出错:", err);
            return { notes: [], userInfo: null };
          }
        })();
      `);

      console.log(`数据提取完成，共获取${result.notes.length}条笔记，用户信息:`, result.userInfo ? '已获取' : '未获取');
      hiddenWindow.hide();

      return {
        notes: result.notes,
        userInfo: result.userInfo
      };
    } catch (error) {
      console.error(`获取博主${userId}笔记时发生错误:`, error);
      return { notes: [], userInfo: null };
    }
  }

  // 该方法已移除，功能由 getNoteByKw 方法提供

  private async callAPI(
    method: string,
    path: string,
    params: any,
    timeout = 30
  ) {
    if (method.toUpperCase() === "GET") {
      if (params) {
        const queryParams = new URLSearchParams(params).toString();
        path += "?" + queryParams;
        params = undefined;
      }
    }
    const url = `${this.apiHost}${path}`;
    const encryptParams = await this.sign2(path, params);
    
    // 改进错误处理：检查签名是否包含错误信息
    if (!encryptParams || (encryptParams as any).error) {
      const errorMsg = (encryptParams as any)?.message || '签名生成失败';
      throw new SignatureError(errorMsg, { url, method });
    }
    const headers = Object.assign(
      {
        "user-agent":
          "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Content-Type": "application/json;charset=UTF-8",
      },
      encryptParams
    );

    const options: any = {
      method: method,
      url: url,
      headers: headers,
    };

    if (method.toUpperCase() === "POST") {
      options.data = params;
    }

    return axios(options)
      .then((response) => {
        const data = response.data;

        if (data.success) {
          return data.data || data.success;
        } else if (data.code) {
          switch (data.code) {
            case ErrorEnum.IP_BLOCK.code:
              throw new IPBlockError(ErrorEnum.IP_BLOCK.msg);
            case ErrorEnum.SIGN_FAULT.code:
              throw new SignError(ErrorEnum.SIGN_FAULT.msg);
            default:
              throw new DataFetchError(data);
          }
        } else {
          return data;
        }
      })
      .catch((error) => {
        // 改进错误处理：提供更详细的错误信息
        if (error.response) {
          // 处理HTTP错误响应
          const status = error.response.status;
          const statusText = error.response.statusText;
          throw new NetworkError(
            `HTTP请求失败: ${status} ${statusText}`,
            { 
              status, 
              statusText, 
              url,
              data: error.response.data 
            }
          );
        } else if (error.request) {
          // 处理网络连接错误
          throw new NetworkError(
            '网络连接失败，请检查网络设置',
            { url, timeout }
          );
        } else {
          // 处理其他错误
          throw new NetworkError(
            `请求配置错误: ${error.message}`,
            { url, originalError: error }
          );
        }
      });
  }
}
