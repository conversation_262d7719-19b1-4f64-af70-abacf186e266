/**
 * 缓存管理器
 * 提供请求缓存和内存管理功能
 */

interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number; // 生存时间（毫秒）
  accessCount: number;
  lastAccess: number;
}

interface CacheStats {
  totalItems: number;
  hitRate: number;
  memoryUsage: number;
  oldestItem: number;
  newestItem: number;
}

export class CacheManager {
  private cache = new Map<string, CacheItem<any>>();
  private maxSize: number;
  private defaultTTL: number;
  private hitCount: number = 0;
  private missCount: number = 0;

  constructor(maxSize: number = 1000, defaultTTL: number = 5 * 60 * 1000) { // 默认5分钟TTL
    this.maxSize = maxSize;
    this.defaultTTL = defaultTTL;
    
    // 定期清理过期缓存
    setInterval(() => {
      this.cleanup();
    }, 60000); // 每分钟清理一次
  }

  /**
   * 生成缓存键
   */
  private generateKey(namespace: string, identifier: string, params?: any): string {
    const paramsStr = params ? JSON.stringify(params) : '';
    return `${namespace}:${identifier}:${paramsStr}`;
  }

  /**
   * 设置缓存
   */
  set<T>(namespace: string, identifier: string, data: T, ttl?: number, params?: any): void {
    const key = this.generateKey(namespace, identifier, params);
    const now = Date.now();
    
    // 如果缓存已满，移除最少使用的项
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      this.evictLeastUsed();
    }

    this.cache.set(key, {
      data,
      timestamp: now,
      ttl: ttl || this.defaultTTL,
      accessCount: 0,
      lastAccess: now
    });
  }

  /**
   * 获取缓存
   */
  get<T>(namespace: string, identifier: string, params?: any): T | null {
    const key = this.generateKey(namespace, identifier, params);
    const item = this.cache.get(key);

    if (!item) {
      this.missCount++;
      return null;
    }

    const now = Date.now();
    
    // 检查是否过期
    if (now - item.timestamp > item.ttl) {
      this.cache.delete(key);
      this.missCount++;
      return null;
    }

    // 更新访问统计
    item.accessCount++;
    item.lastAccess = now;
    this.hitCount++;

    return item.data;
  }

  /**
   * 检查缓存是否存在且未过期
   */
  has(namespace: string, identifier: string, params?: any): boolean {
    const key = this.generateKey(namespace, identifier, params);
    const item = this.cache.get(key);
    
    if (!item) return false;
    
    const now = Date.now();
    if (now - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }

  /**
   * 删除特定缓存
   */
  delete(namespace: string, identifier: string, params?: any): boolean {
    const key = this.generateKey(namespace, identifier, params);
    return this.cache.delete(key);
  }

  /**
   * 清除命名空间下的所有缓存
   */
  clearNamespace(namespace: string): number {
    const keysToDelete: string[] = [];
    
    for (const key of this.cache.keys()) {
      if (key.startsWith(`${namespace}:`)) {
        keysToDelete.push(key);
      }
    }
    
    keysToDelete.forEach(key => this.cache.delete(key));
    return keysToDelete.length;
  }

  /**
   * 清理过期缓存
   */
  private cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));
    
    if (keysToDelete.length > 0) {
      console.log(`缓存清理：移除了 ${keysToDelete.length} 个过期项`);
    }
  }

  /**
   * 移除最少使用的缓存项
   */
  private evictLeastUsed(): void {
    let leastUsedKey: string | null = null;
    let leastAccessCount = Infinity;
    let oldestTime = Infinity;

    for (const [key, item] of this.cache.entries()) {
      // 首先按访问次数，再按时间
      if (item.accessCount < leastAccessCount || 
          (item.accessCount === leastAccessCount && item.lastAccess < oldestTime)) {
        leastUsedKey = key;
        leastAccessCount = item.accessCount;
        oldestTime = item.lastAccess;
      }
    }

    if (leastUsedKey) {
      this.cache.delete(leastUsedKey);
      console.log(`缓存已满，移除最少使用的项: ${leastUsedKey}`);
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): CacheStats {
    const totalRequests = this.hitCount + this.missCount;
    const hitRate = totalRequests > 0 ? this.hitCount / totalRequests : 0;
    
    let oldestItem = Infinity;
    let newestItem = 0;
    let memoryUsage = 0;

    for (const item of this.cache.values()) {
      oldestItem = Math.min(oldestItem, item.timestamp);
      newestItem = Math.max(newestItem, item.timestamp);
      // 粗略估计内存使用（实际会更复杂）
      memoryUsage += JSON.stringify(item.data).length * 2; // 每个字符大约2字节
    }

    return {
      totalItems: this.cache.size,
      hitRate: Math.round(hitRate * 100) / 100,
      memoryUsage: memoryUsage,
      oldestItem: oldestItem === Infinity ? 0 : oldestItem,
      newestItem: newestItem
    };
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    this.cache.clear();
    this.hitCount = 0;
    this.missCount = 0;
  }

  /**
   * 获取缓存大小
   */
  size(): number {
    return this.cache.size;
  }
}

// 创建全局缓存实例
export const globalCache = new CacheManager(500, 3 * 60 * 1000); // 500项，3分钟TTL

// 缓存命名空间常量
export const CacheNamespaces = {
  USER_INFO: 'user_info',
  NOTE_DETAIL: 'note_detail',
  SEARCH_RESULTS: 'search_results',
  USER_NOTES: 'user_notes'
} as const;