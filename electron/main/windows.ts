import { app, BrowserWindow, protocol, ipcMain, session } from 'electron';
import * as path from 'path';
import * as fs from 'fs';
import Store from 'electron-store';
import { isAppQuitting } from './appState'; // Import the state checker

// Environment and Constants
// Correctly determine isDevelopment based on app.isPackaged
const isDevelopment = !app.isPackaged;
const WINDOW_WIDTH = 1200;
const WINDOW_HEIGHT = 1024;
process.env.DIST_ELECTRON = path.join(__dirname, '..');
process.env.DIST = path.join(process.env.DIST_ELECTRON, '../dist');
const VITE_DEV_SERVER_URL = process.env.VITE_DEV_SERVER_URL;

// Window References
let mainWindow: BrowserWindow | null = null;
let loginWindow: BrowserWindow | null = null;

// Store Initialization
const store = new Store();

// --- Helper Functions ---

function loadCookiesFromFile(): string {
    let cookie_str = '';
    const filePath = path.join(app.getPath("userData"), "cookies.json");
    console.log(`Cookie file path: ${filePath}`);

    if (!fs.existsSync(filePath)) {
        console.log('Cookie file not found, creating empty file.');
        fs.writeFileSync(filePath, "[]");
    } else {
        try {
            const data = fs.readFileSync(filePath, "utf-8");
            const cookies = JSON.parse(data);
            cookie_str = cookies
                .map((cookie: any) => `${cookie.name}=${cookie.value}`)
                .join("; "); // Use "; " for better separation
            console.log('Cookies loaded from file.');
        } catch (error) {
            console.error('Error reading or parsing cookie file:', error);
            // If parsing fails, create a new empty file
            fs.writeFileSync(filePath, "[]");
        }
    }
    return cookie_str;
}

async function saveCookiesToFile(targetWindow: BrowserWindow | null): Promise<void> {
    if (!targetWindow) {
        console.log('Cannot save cookies, login window is null.');
        return;
    }
    try {
        const cookies = await targetWindow.webContents.session.cookies.get({
            url: "https://www.xiaohongshu.com",
        });
        const filePath = path.join(app.getPath("userData"), "cookies.json");
        fs.writeFileSync(filePath, JSON.stringify(cookies, null, 2)); // Pretty print JSON
        console.log('Cookies saved successfully.');
    } catch (error) {
        console.error('Failed to save cookies:', error);
    }
}

// Correct path calculation for stealth script
function getStealthScriptPath(): string {
    return app.isPackaged
        ? path.join(process.resourcesPath, 'app.asar.unpacked', 'stealth.min.js') // Adjust if needed based on build output
        : path.join(app.getAppPath(), 'stealth.min.js'); // Assumes stealth.min.js is in project root
}


// --- Window Creation and Management ---

export async function createMainWindow(): Promise<BrowserWindow> {
    console.log('Creating main window...');
    mainWindow = new BrowserWindow({
        width: WINDOW_WIDTH,
        height: WINDOW_HEIGHT,
        webPreferences: {
            preload: path.join(__dirname, '../preload/index.js'), // Correct path relative to compiled windows.js
            nodeIntegration: false,
            contextIsolation: true,
        },
        show: false, // Don't show until ready
    });

    mainWindow.once('ready-to-show', () => {
        mainWindow?.show();
        console.log('Main window is ready to show.');
    });

    mainWindow.on('closed', () => {
        console.log('Main window closed.');
        mainWindow = null; // Dereference the window object
    });

    if (VITE_DEV_SERVER_URL) {
        console.log(`Loading URL for main window: ${VITE_DEV_SERVER_URL}`);
        await mainWindow.loadURL(VITE_DEV_SERVER_URL);
        mainWindow.webContents.openDevTools();
    } else {
        const distPath = process.env.DIST;
        if (!distPath) {
            console.error("DIST environment variable is not set. Cannot load main window HTML.");
            // Optionally, close the window or show an error message
            mainWindow?.close();
            throw new Error("DIST environment variable is not set.");
        }
        const htmlPath = path.join(distPath, 'index.html');
        console.log(`Loading file for main window: ${htmlPath}`);
        await mainWindow.loadFile(htmlPath);
    }

    return mainWindow;
}

export async function createLoginWindow(): Promise<BrowserWindow> {
    console.log('Creating login window...');
    loginWindow = new BrowserWindow({
        // 设置User-Agent
        width: WINDOW_WIDTH,
        height: WINDOW_HEIGHT,
        show: false, // Initially hidden
        webPreferences: {
            preload: path.join(__dirname, '../preload/index.js'), // Correct path relative to compiled windows.js
            nodeIntegration: false,
            contextIsolation: true,
        },
    });
    loginWindow.webContents.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

    // --- Login Window Event Handlers ---

    loginWindow.webContents.on("did-finish-load", async () => {
        console.log(`Login window finished loading URL: ${loginWindow?.webContents.getURL()}`);
        // Inject stealth script
        const stealthScriptPath = getStealthScriptPath();
        console.log(`Attempting to inject stealth script from: ${stealthScriptPath}`);
        try {
            const data = fs.readFileSync(stealthScriptPath, "utf8");
            await loginWindow!.webContents.executeJavaScript(data);
            console.log("Stealth script injected successfully.");
        } catch (err) {
            console.error("Failed to load or execute stealth script:", err);
        }

        // TODO: Implement actual login status check after load if needed
        console.log("Checking login status after did-finish-load (placeholder)...");
    });

    loginWindow.webContents.session.webRequest.onCompleted({ urls: ['*://*.xiaohongshu.com/*'] }, (details) => {
        // Simplified login detection logic
        if (details.url.includes('/user/me')) { // Assuming this is the correct endpoint
            console.log(`Detected /user/me completion (Status: ${details.statusCode}). Triggering login status update.`);
            // Notify main process (via IPC or direct call if structure allows) to check and update login status
            // Example: ipcMain.emit('check-login-status'); // Needs proper implementation
            // TODO: Implement robust login status check and notification
            // Potentially hide window if login is confirmed successful
            // handleToggleLoginWindow('hide'); // Example, needs proper logic

            const contentLength = details.responseHeaders?.['content-length']?.[0] || '0';
            // 如果contentLength > 100
            if (Number(contentLength) > 100) {
                console.log('请求完成:', details.url);
                console.log('响应状态:', details.statusCode);
                console.log('响应头:', details.responseHeaders);
                ipcMain.emit('login');
            }
        }
    });

    loginWindow.on("close", (e) => {
        // Check if the application is actually quitting
        if (isAppQuitting()) {
            // If the app is quitting, allow the window to be closed naturally.
            // The 'closed' event will handle cleanup.
            console.log("Login window 'close' event: App is quitting, allowing close.");
            return; // Do not prevent default
        } else {
            // If the app is not quitting (user clicked 'x'), prevent closing and just hide.
            console.log("Login window 'close' event: App not quitting, preventing default and hiding.");
            e.preventDefault();
            loginWindow?.hide();
        }
    });

    loginWindow.on('closed', () => {
        console.log('Login window instance closed.');
        loginWindow = null; // Dereference the window object
    });

    // --- Load URL and Cookies ---
    const login_cookie_str = loadCookiesFromFile();
    console.log(`Applying cookies to login window session...`);

    if (login_cookie_str) {
        const cookieArr = login_cookie_str.split(";");
        const cookiePromises = cookieArr
            .map(cookie => cookie.trim()) // Trim whitespace
            .filter(cookie => cookie.length > 0) // Ensure cookie string is not empty
            .map((cookie) => {
                const parts = cookie.split("=");
                const key = parts[0]?.trim();
                const value = parts.slice(1).join('=').trim(); // Handle values containing '='

                if (!key) return Promise.resolve(); // Skip if key is empty

                // Skip potentially problematic cookies (adjust as needed)
                const skipKeys = ['unread', 'cache_feeds', 'webId', 'web_session', 'xsecappid'];
                if (skipKeys.includes(key)) {
                    console.log(`Skipping cookie: ${key}`);
                    return Promise.resolve();
                }

                return loginWindow!.webContents.session.cookies
                    .set({
                        url: "https://www.xiaohongshu.com",
                        domain: ".xiaohongshu.com", // Ensure domain is correct
                        name: key,
                        value: value,
                        path: '/', // Set path for broader applicability
                        secure: true, // Assume secure cookies
                        httpOnly: false // We can only set non-HttpOnly cookies here
                    })
                    .catch((e) => {
                        console.error(`Failed to set cookie: ${key}=${value}, Error: ${e.message}`);
                    });
            });

        await Promise.all(cookiePromises);
        console.log('Finished applying cookies from file.');
    } else {
        console.log('No cookies found in file.');
    }

    console.log('Loading URL for login window: https://www.xiaohongshu.com');
    await loginWindow.loadURL("https://www.xiaohongshu.com");

    return loginWindow;
}

// --- Public API for Window Management ---

export function getMainWindow(): BrowserWindow | null {
    return mainWindow;
}

export function getLoginWindow(): BrowserWindow | null {
    return loginWindow;
}

export function showLoginWindow(url?: string): void {
    if (loginWindow) {
        console.log('Showing login window.');
        if (url) {
            console.log(`Loading URL in login window: ${url}`);
            loginWindow.loadURL(url); // Load URL if provided
        }
        loginWindow.show();
    } else {
        console.warn('Attempted to show login window, but it is null.');
        // Optionally recreate it: createLoginWindow().then(win => win.show());
    }
}

export function hideLoginWindow(): void {
    if (loginWindow) {
        console.log('Hiding login window.');
        loginWindow.hide();
    }
}

export async function handleToggleLoginWindow(action: 'show' | 'hide', url?: string): Promise<void> {
    if (action === "show") {
        showLoginWindow(url);
    } else if (action === "hide") {
        hideLoginWindow();
    }
}

export async function handleLogout(): Promise<void> {
    console.log('Handling logout...');
    try {
        // Set guest status in store
        store.set('xhs-login-info', { guest: true });
        console.log('Set login status to guest.');

        // Clear local cookie file
        const filePath = path.join(app.getPath("userData"), "cookies.json");
        fs.writeFileSync(filePath, "[]");
        console.log('Cleared local cookie file.');

        // Clear session storage and reload login window
        if (loginWindow) {
            console.log('Clearing session storage for login window...');
            await loginWindow.webContents.session.clearStorageData({
                storages: ['cookies', 'localstorage'], // Clear more storage types if needed
            });
            console.log('Reloading login window.');
            loginWindow.reload();
            // TODO: Notify renderer processes about login status change via IPC
            // mainWindow?.webContents.send('login-status-changed', { guest: true });
        }
        console.log('Logout process completed.');
    } catch (error) {
        console.error('Logout failed:', error);
    }
}

// --- App Lifecycle Handlers ---

export function handleAppActivate(): void {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    const main = BrowserWindow.getAllWindows().find((window) => window.title === "xhs-pc-app")
    if (!main) {
        console.log('App activated with no windows open, creating main window.');
        createMainWindow();
    } else {
        console.log('App activated, showing main window.');
        mainWindow?.show(); // Ensure main window is visible if it exists
    }
}

// Export saveCookiesToFile to be used in index.ts's before-quit handler
export { saveCookiesToFile };