# Electron IPC 改造计划

## 问题描述

当前 Electron 应用中的 IPC (Inter-Process Communication) 处理集中在 `electron/main/ipcHandlers.ts` 文件中，预加载脚本 `electron/preload/index.ts` 将所有功能通过一个扁平的 `electronAPI` 对象暴露给渲染进程，并在 `src/vite-env.d.ts` 中定义了相应的类型。随着功能的增加，这种方式导致代码结构混乱，难以管理和维护。

## 改造目标

通过模块化和结构化 IPC 处理函数和 API 暴露方式，提高代码的可读性、可维护性和可扩展性。

## 改造方案计划

1.  **模块化主进程 IPC 处理函数：**
    *   根据功能将 `electron/main/ipcHandlers.ts` 中的 IPC 处理函数分组（例如：小红书相关、Excel 相关、文件系统相关、更新相关、应用/窗口相关等）。
    *   在 `electron/main/` 目录下创建一个新的 `ipc` 文件夹。
    *   为每个功能组在 `electron/main/ipc/` 文件夹下创建单独的 TypeScript 文件（例如：`xhs.ts`、`excel.ts`、`fileSystem.ts`、`updater.ts`、`app.ts`）。
    *   将 `ipcHandlers.ts` 中对应分组的处理函数移动到各自的文件中。
    *   在每个新的文件中，导出一个函数用于注册该模块的 IPC 处理函数。
    *   修改 `electron/main/ipcHandlers.ts`，使其只负责导入这些模块的注册函数并统一调用。

2.  **优化预加载脚本 `electronAPI` 结构：**
    *   修改 `electron/preload/index.ts`，将暴露的 `electronAPI` 对象结构化，使用命名空间或嵌套对象来组织不同模块的功能（例如：`electronAPI.xhs.getNoteDetail()`，`electronAPI.excel.parseFile()`）。
    *   更新 `electronAPI` 中的函数，使其调用 `ipcRenderer.invoke` 或 `ipcRenderer.send` 时使用与主进程模块对应的 IPC 通道名称。

3.  **完善类型定义：**
    *   修改 `src/vite-env.d.ts`，使其类型定义与新的结构化 `electronAPI` 对象相匹配。
    *   为 IPC 通道传递的数据定义清晰的 TypeScript 接口或类型，替换当前的 `any` 类型，提高代码的可读性和类型安全性。

4.  **代码实现与测试：**
    *   按照上述计划修改代码文件。
    *   进行全面的测试，确保所有 IPC 通信正常工作。

## 改造后的结构示意图

```mermaid
graph TD
    A[渲染进程] --> B(预加载脚本);
    B --> C{electronAPI};
    C --> C1[xhs];
    C --> C2[excel];
    C --> C3[fileSystem];
    C --> C4[updater];
    C --> C5[app];
    C1 --> D1(IPC 通道: xhs-note-detail);
    C1 --> D2(IPC 通道: xhs-notes-fetch);
    C2 --> D3(IPC 通道: excel-file-parse);
    C3 --> D4(IPC 通道: fs-existsSync);
    C4 --> D5(IPC 通道: check-for-updates);
    C5 --> D6(IPC 通道: open-dialog);
    D1 --> E[主进程];
    D2 --> E;
    D3 --> E;
    D4 --> E;
    D5 --> E;
    D6 --> E;
    E --> F[IPC 处理函数注册];
    F --> F1[electron/main/ipc/xhs.ts];
    F --> F2[electron/main/ipc/excel.ts];
    F --> F3[electron/main/ipc/fileSystem.ts];
    F --> F4[electron/main/ipc/updater.ts];
    F --> F5[electron/main/ipc/app.ts];
    F1 --> G1(XhsService);
    F2 --> G2(ExcelService);
    F3 --> G3(fs, path);
    F4 --> G4(更新逻辑);
    F5 --> G5(dialog, shell, windows);