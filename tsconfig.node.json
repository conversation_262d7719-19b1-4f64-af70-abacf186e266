{
  "compilerOptions": {
    "composite": true, // Added for project references
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.node.tsbuildinfo",
    "noImplicitAny": false,
    "target": "ES2022",
    "lib": [
      "ES2023"
    ],
    "module": "ESNext",
    "skipLibCheck": true,
    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true, // Added to allow importing JSON
    "esModuleInterop": true, // Added for CJS/ESM compatibility
    "allowSyntheticDefaultImports": true, // Added for compatibility
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,
    "types": [
      "node",
      "vite/client"
    ], // Added explicit types
    /* Linting */
    "strict": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": true
  },
  // Include vite config and electron main/preload files later
  "include": [
    "vite.config.ts",
    "electron/main/**/*.ts", // Added electron main files
    "electron/main/**/*.d.ts" // Include declaration files
  ]
}