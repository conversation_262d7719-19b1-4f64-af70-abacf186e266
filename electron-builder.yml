# electron-builder.yml

asar: true

# 产品名称
productName: KOL小红书工具箱

# 应用 ID
appId: com.kolcm.xhs

# 发布配置
publish:
  provider: generic
  url: https://wy.frp.kolcm.com/updates/

# 目录配置
directories:
  output: release/${version} # electron-builder 的最终输出目录

# 需要包含/排除的文件
# 当 asar: false 时，这些文件会直接复制到应用资源目录
files:
  - dist/**/* # 包含你的 Vite 构建输出 (渲染进程文件)
  - dist-electron/main/**/* # 包含 vite-plugin-electron 构建输出的主进程文件
  - dist-electron/preload/**/* # 包含 vite-plugin-electron 构建输出的预加载文件
  - package.json # electron-builder 需要读取 package.json
  - "!**/*.map" # 排除 sourcemap 文件
  - "!**/node_modules/**/{test,docs,examples}/**" # 排除 node_modules 中的不必要文件

# 额外资源文件
extraResources:
  - from: "./stealth.min.js" # 确保路径相对于 electron-builder.yml 所在的项目根目录
    to: stealth.min.js

# Windows 特定配置
win:
  icon: src/assets/logo/icon.ico # 图标路径
  artifactName: "${productName}_Setup_${version}.${ext}"
  target:
    - target: nsis
      arch:
        - x64

# NSIS 安装程序配置
nsis:
  oneClick: false
  allowElevation: true
  allowToChangeInstallationDirectory: true
  installerIcon: src/assets/logo/icon.ico
  uninstallerIcon: src/assets/logo/icon.ico
  installerHeaderIcon: src/assets/logo/icon.ico
  createDesktopShortcut: true
  createStartMenuShortcut: true

# macOS 特定配置
mac:
  icon: src/assets/logo/icon.icns # 图标路径
  category: public.app-category.utilities
  hardenedRuntime: true
  gatekeeperAssess: false
  target:
    - target: dmg
      arch:
        - x64 # 通用构建
        - arm64

# DMG 配置
dmg:
  contents:
    - x: 410
      y: 150
      type: link
      path: /Applications
    - x: 130
      y: 150
      type: file
