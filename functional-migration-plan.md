# 小红书工具箱功能迁移计划 (xhs-pc-app)

基于对 `xhs-pc-app` 项目中 TODO 标记的分析以及原重构计划 (`refactoring-plan.md`, `refactoring-plan-next-steps.md`)，并结合对 `windows.ts` 现状的补充，制定以下功能迁移计划。

**目标:** 优先完成与核心功能相关的 TODO 迁移，确保应用基本功能在新架构下正常运行，并将 `windows.ts` 的优化重构作为后续任务。

## 1. 处理渲染进程中的 IPC TODO (优先级: 高)

*   **任务:** 将 `xhs-pc-app/src/views/` 和 `xhs-pc-app/src/components/` 目录下所有包含 TODO 的 Vue 文件中，原使用 `ipcRenderer` 或 Node.js 内置模块的地方，替换为通过 `window.electronAPI` 调用相应的主进程功能。
*   **涉及功能:** 文件选择、目录选择、文件路径操作、Excel 文件解析、目录创建、图片/视频保存、在文件夹中显示文件、获取用户信息、检查笔记、打开外部链接、复制到剪贴板、显示登录窗口、获取登录状态、获取 Cookie、检查账号余额等。
*   **参考:** 原 `kol-xhs-app` 项目中对应的实现，以及 `xhs-pc-app/electron/preload/index.ts` 中暴露的 API。
*   **特别注意:** `UserInfo.vue` 中的登录/退出逻辑需要与主进程的登录窗口管理和 Cookie 处理逻辑紧密配合。

## 2. 处理主进程中的功能相关 TODO (优先级: 高)

*   **任务:** 在 `xhs-pc-app/electron/main/` 目录下，根据 TODO 提示，实现或完善与渲染进程 IPC 调用对应的处理函数。
*   **涉及文件:** 主要包括 `ipcHandlers.ts` 和 `updater.ts`。
*   **具体内容:**
    *   在 `ipcHandlers.ts` 中，实现账号余额查询、用户检查等处理函数，调用服务层 (`electron/main/services/`) 或 API 层 (`electron/main/api/`) 的方法。
    *   在 `updater.ts` 中，完善自动更新相关的配置（如 `updateUrl`）。

## 3. 完善预加载脚本 (优先级: 高)

*   **任务:** 检查 `xhs-pc-app/electron/preload/index.ts`，确保所有步骤 1 中渲染进程需要调用的主进程功能都已通过 `contextBridge.exposeInMainWorld` 安全地暴露。
*   **具体内容:** 如果发现有渲染进程的 TODO 需要调用但预加载脚本中未暴露的方法，需要在此处添加相应的暴露方法。

## 4. 集成和测试 (功能部分) (优先级: 高)

*   **任务:** 在完成上述功能相关的代码修改后，进行全面的集成测试。
*   **测试重点:** 确保前后端 IPC 通信正常，各项功能（特别是文件操作、网络请求、登录、更新）在新架构下能够正确执行。
*   **工具:** 利用 Electron DevTools 和 Vue Devtools 进行调试。

## 5. `windows.ts` 优化重构 (优先级: 低)

*   **任务:** 将主进程 `index.ts` 中所有关于窗口管理的逻辑逐步迁移到 `windows.ts` 中，并优化 `windows.ts` 的代码结构。
*   **具体内容:** 包括主窗口和登录窗口的创建、管理、事件处理等逻辑的迁移和重构。
*   **后续步骤:** 更新 `index.ts` 以引用 `windows.ts` 中的功能，并进行相关测试。

## 流程图

```mermaid
graph TD
    subgraph Renderer Process (Vue 3)
        R_Views_Components[Views & Components] -- Calls window.electronAPI --> P_Preload[Preload Script]
        R_App[App.vue] -- Handles Login/Update UI --> P_Preload
        R_Store[Pinia Stores] -- Manages State --> R_Views_Components
    end

    subgraph Electron Preload Script
        P_Preload -- Exposes API via contextBridge --> M_IPC[Main Process IPC Handlers]
        P_Preload -- IPC Send/Invoke --> M_IPC
    end

    subgraph Electron Main Process
        M_IPC -- Calls Services --> M_Services[Main Process Services]
        M_Services -- Includes --> S_XhsService[XhsService]
        M_Services -- Includes --> S_ExcelService[ExcelService]
        M_Services -- Includes --> S_AuthService[AuthService (Login/Cookie)]
        M_IPC -- Interacts with --> M_Updater[Updater]
        M_Updater -- Uses --> ElectronUpdater[electron-updater]
        S_AuthService -- Handles --> Cookie_File[Cookie File]
        M_Index[index.ts] -- (Later: Migrates Window Logic To) --> M_Windows[Window Management]
        M_Windows -- Manages --> Win_Main[Main Window]
        M_Windows -- Manages --> Win_Login[Login Window]
    end

    R_App -- Checks Login Status --> R_Store
    Win_Login -- Captures Cookies --> S_AuthService
    M_Updater -- Notifies via IPC --> M_IPC
    S_AuthService -- Notifies via IPC --> M_IPC
    M_IPC -- Sends Status/Data via IPC --> P_Preload

    %% Highlight areas with TODOs (excluding windows.ts for now)
    style R_Views_Components fill:#f9f,stroke:#333,stroke-width:2px
    style R_App fill:#f9f,stroke:#333,stroke-width:2px
    style M_IPC fill:#f9f,stroke:#333,stroke-width:2px
    style M_Updater fill:#f9f,stroke:#333,stroke-width:2px
    style P_Preload fill:#ff9,stroke:#333,stroke-width:2px,stroke-dasharray: 5 5
    style M_Windows fill:#ccf,stroke:#333,stroke-width:2px %% windows.ts is lower priority