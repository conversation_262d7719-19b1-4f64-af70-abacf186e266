# 视频处理组件后端转换方法完善方案

## 项目概述

本文档详细说明了如何完善KOL小红书工具箱应用中视频处理组件的后端转换方法，将当前的模拟实现升级为真实的FFmpeg集成。

## 当前状况分析

### 已完成部分

1. **前端组件完整** 
   - `VideoConversionPanel.vue` 已实现完整的UI和交互逻辑
   - 支持文件上传、格式选择、进度显示等功能
   - 包含批量处理和任务管理界面

2. **架构完善**
   - 建立了完整的媒体处理架构：`MediaService`、`FFmpegService`、IPC处理器
   - 模块化的服务层设计，支持扩展
   - 完整的批量任务管理系统

3. **依赖已安装**
   - `@ffmpeg-installer/ffmpeg": "^1.1.0"`
   - `fluent-ffmpeg": "^2.1.3"`
   - 相关的IPC通信和状态管理已建立

### 关键问题

**FFmpegService 当前为模拟实现**，文件位置：`electron/main/services/ffmpeg-service.ts`

```typescript
// 当前状态：所有FFmpeg相关代码都被注释
// import ffmpeg from 'fluent-ffmpeg';
// import ffmpegInstaller from '@ffmpeg-installer/ffmpeg';

// 所有方法都是模拟实现，使用setTimeout和文件复制
```

## 完善方案详述

### 1. 启用真实的FFmpeg服务

**文件路径：** `electron/main/services/ffmpeg-service.ts`

#### 1.1 基础设置改动

```typescript
// 需要取消注释并启用
import ffmpeg from 'fluent-ffmpeg';
import ffmpegInstaller from '@ffmpeg-installer/ffmpeg';

export class FFmpegService {
  constructor() {
    // 设置FFmpeg路径
    ffmpeg.setFfmpegPath(ffmpegInstaller.path);
    console.log(`[FFmpegService] FFmpeg路径设置: ${ffmpegInstaller.path}`);
  }
}
```

#### 1.2 核心方法实现

**1.2.1 视频转换方法 (`convertVideo`)**

```typescript
async convertVideo(inputPath: string, outputPath: string, options: ConvertOptions): Promise<string> {
  return new Promise((resolve, reject) => {
    let command = ffmpeg(inputPath);
    
    // 设置输出格式
    command = command.format(options.outputFormat);
    
    // 设置质量参数
    if (options.quality) {
      command = command.videoBitrate(options.quality);
    }
    
    // 设置尺寸调整
    if (options.resizeEnabled && options.maxWidth) {
      command = command.size(`${options.maxWidth}x?`);
    }
    
    // 进度监听
    command.on('progress', (progress) => {
      // 通过事件发送进度更新
      this.emitProgress(progress);
    });
    
    // 错误处理
    command.on('error', (err) => {
      reject(new Error(`视频转换失败: ${err.message}`));
    });
    
    // 完成处理
    command.on('end', () => {
      resolve(outputPath);
    });
    
    // 开始转换
    command.save(outputPath);
  });
}
```

**1.2.2 音频提取方法 (`extractAudio`)**

```typescript
async extractAudio(videoPath: string, audioPath: string, options?: { quality?: string }): Promise<string> {
  return new Promise((resolve, reject) => {
    let command = ffmpeg(videoPath);
    
    // 只提取音频
    command = command.noVideo();
    
    // 设置音频质量
    if (options?.quality) {
      command = command.audioBitrate(options.quality);
    }
    
    // 设置输出格式
    const ext = path.extname(audioPath).slice(1);
    command = command.format(ext);
    
    // 进度和错误处理
    command.on('progress', (progress) => this.emitProgress(progress));
    command.on('error', (err) => reject(new Error(`音频提取失败: ${err.message}`)));
    command.on('end', () => resolve(audioPath));
    
    command.save(audioPath);
  });
}
```

**1.2.3 媒体信息获取 (`getMediaInfo`)**

```typescript
async getMediaInfo(filePath: string): Promise<MediaInfo> {
  return new Promise((resolve, reject) => {
    ffmpeg.ffprobe(filePath, (err, metadata) => {
      if (err) {
        reject(new Error(`获取媒体信息失败: ${err.message}`));
        return;
      }
      
      const videoStream = metadata.streams.find(s => s.codec_type === 'video');
      const audioStream = metadata.streams.find(s => s.codec_type === 'audio');
      
      const mediaInfo: MediaInfo = {
        duration: metadata.format.duration,
        bitrate: metadata.format.bit_rate
      };
      
      if (videoStream) {
        mediaInfo.dimensions = {
          width: videoStream.width || 0,
          height: videoStream.height || 0
        };
        mediaInfo.codec = videoStream.codec_name;
      } else if (audioStream) {
        mediaInfo.codec = audioStream.codec_name;
      }
      
      resolve(mediaInfo);
    });
  });
}
```

#### 1.3 进度追踪机制

```typescript
private progressCallbacks: Map<string, (progress: any) => void> = new Map();

// 注册进度回调
registerProgressCallback(taskId: string, callback: (progress: any) => void): void {
  this.progressCallbacks.set(taskId, callback);
}

// 发送进度更新
private emitProgress(progress: any): void {
  this.progressCallbacks.forEach(callback => callback(progress));
}

// 任务取消机制
private activeCommands: Map<string, ffmpeg.FfmpegCommand> = new Map();

cancelTask(taskId: string): void {
  const command = this.activeCommands.get(taskId);
  if (command) {
    command.kill('SIGKILL');
    this.activeCommands.delete(taskId);
  }
}
```

### 2. 增强MediaService

**文件路径：** `electron/main/services/media-worker.ts`

#### 2.1 添加进度追踪

```typescript
export class MediaService {
  private taskProgressCallbacks: Map<string, (progress: number) => void> = new Map();
  
  // 注册任务进度回调
  onTaskProgress(taskId: string, callback: (progress: number) => void): void {
    this.taskProgressCallbacks.set(taskId, callback);
  }
  
  // 更新任务进度
  private updateTaskProgress(taskId: string, progress: number): void {
    const callback = this.taskProgressCallbacks.get(taskId);
    if (callback) {
      callback(progress);
    }
  }
}
```

#### 2.2 增强任务控制

```typescript
// 暂停任务
async pauseTask(taskId: string): Promise<void> {
  const task = this.findTaskById(taskId);
  if (task && task.status === 'processing') {
    // 暂停FFmpeg命令
    this.ffmpegService.pauseTask(taskId);
    task.status = 'paused';
  }
}

// 恢复任务
async resumeTask(taskId: string): Promise<void> {
  const task = this.findTaskById(taskId);
  if (task && task.status === 'paused') {
    // 恢复FFmpeg命令
    this.ffmpegService.resumeTask(taskId);
    task.status = 'processing';
  }
}

// 取消任务
async cancelTask(taskId: string): Promise<void> {
  const task = this.findTaskById(taskId);
  if (task) {
    this.ffmpegService.cancelTask(taskId);
    task.status = 'cancelled';
  }
}
```

#### 2.3 FFmpeg可用性检查

```typescript
async checkFFmpegStatus(): Promise<{
  available: boolean;
  version?: string;
  path?: string;
  error?: string;
}> {
  try {
    const available = await this.ffmpegService.checkFFmpegAvailable();
    if (available) {
      return {
        available: true,
        path: ffmpegInstaller.path,
        version: await this.ffmpegService.getVersion()
      };
    } else {
      return {
        available: false,
        error: 'FFmpeg not available'
      };
    }
  } catch (error) {
    return {
      available: false,
      error: error instanceof Error ? error.message : '检查失败'
    };
  }
}
```

### 3. 扩展IPC通信

**文件路径：** `electron/main/ipc/media.ts`

#### 3.1 添加进度事件推送

```typescript
import { ipcMain, BrowserWindow } from 'electron';

// 进度事件推送
export function setupProgressEvents(mediaService: MediaService): void {
  mediaService.onTaskProgress = (taskId: string, progress: number) => {
    // 向所有渲染进程推送进度更新
    BrowserWindow.getAllWindows().forEach(window => {
      window.webContents.send('media-task-progress', { taskId, progress });
    });
  };
}

// 任务控制IPC处理器
ipcMain.handle('media-pause-task', async (event, taskId: string): Promise<ApiResponse<boolean>> => {
  try {
    await mediaService.pauseTask(taskId);
    return { success: true, data: true };
  } catch (error: unknown) {
    return { success: false, error: error instanceof Error ? error.message : '暂停失败' };
  }
});

ipcMain.handle('media-resume-task', async (event, taskId: string): Promise<ApiResponse<boolean>> => {
  try {
    await mediaService.resumeTask(taskId);
    return { success: true, data: true };
  } catch (error: unknown) {
    return { success: false, error: error instanceof Error ? error.message : '恢复失败' };
  }
});

ipcMain.handle('media-cancel-task', async (event, taskId: string): Promise<ApiResponse<boolean>> => {
  try {
    await mediaService.cancelTask(taskId);
    return { success: true, data: true };
  } catch (error: unknown) {
    return { success: false, error: error instanceof Error ? error.message : '取消失败' };
  }
});

// FFmpeg状态检查
ipcMain.handle('media-check-ffmpeg-status', async (): Promise<ApiResponse<any>> => {
  try {
    const status = await mediaService.checkFFmpegStatus();
    return { success: true, data: status };
  } catch (error: unknown) {
    return { success: false, error: error instanceof Error ? error.message : '检查失败' };
  }
});
```

### 4. 更新前端API定义

**文件路径：** `electron/preload/index.ts`

#### 4.1 扩展媒体API

```typescript
media: {
  // 现有API...
  
  // 任务控制
  pauseTask: (taskId: string) => ipcRenderer.invoke('media-pause-task', taskId),
  resumeTask: (taskId: string) => ipcRenderer.invoke('media-resume-task', taskId),
  cancelTask: (taskId: string) => ipcRenderer.invoke('media-cancel-task', taskId),
  
  // FFmpeg状态检查
  checkFFmpegStatus: () => ipcRenderer.invoke('media-check-ffmpeg-status'),
  
  // 进度监听
  onTaskProgress: (callback: (data: { taskId: string; progress: number }) => void) => {
    ipcRenderer.on('media-task-progress', (event, data) => callback(data));
  },
  
  // 移除进度监听
  removeTaskProgressListener: () => {
    ipcRenderer.removeAllListeners('media-task-progress');
  }
}
```

### 5. 前端组件优化

**文件路径：** `src/components/media/VideoConversionPanel.vue`

#### 5.1 添加进度监听

```typescript
// 在组件mounted时注册进度监听
onMounted(() => {
  window.electronAPI.media.onTaskProgress((data) => {
    const task = conversionTasks.value.find(t => t.id === data.taskId);
    if (task) {
      task.progress = data.progress;
    }
  });
});

// 在组件卸载时移除监听
onUnmounted(() => {
  window.electronAPI.media.removeTaskProgressListener();
});
```

#### 5.2 实现真实的暂停功能

```typescript
const pauseConversion = async () => {
  if (!isConverting.value) return;
  
  try {
    // 暂停所有处理中的任务
    const processingTasks = conversionTasks.value.filter(t => t.status === 'processing');
    
    for (const task of processingTasks) {
      await window.electronAPI.media.pauseTask(task.id);
    }
    
    isConverting.value = false;
    ElMessage.info('转换已暂停');
  } catch (error: any) {
    ElMessage.error(`暂停失败: ${error.message}`);
  }
}
```

## 实施步骤

### 第一阶段：启用FFmpeg服务
1. 修改 `ffmpeg-service.ts`，取消注释并实现真实的FFmpeg调用
2. 添加基础的进度追踪和错误处理
3. 测试基本的视频转换和音频提取功能

### 第二阶段：实现进度追踪
1. 在 `MediaService` 中添加进度管理机制
2. 扩展IPC通信支持进度事件推送
3. 更新前端组件支持实时进度显示

### 第三阶段：任务控制功能
1. 实现任务暂停、恢复、取消功能
2. 添加相应的IPC处理器
3. 更新前端UI支持任务控制操作

### 第四阶段：健壮性优化
1. 添加FFmpeg可用性检查
2. 优化错误处理和重试机制
3. 实现内存管理和临时文件清理

### 第五阶段：测试和优化
1. 全面测试各种视频格式和转换场景
2. 性能优化和内存使用优化
3. 用户体验优化和错误提示完善

## 技术要点

### 进度追踪机制
- 利用FFmpeg的 `progress` 事件获取实时进度
- 通过IPC事件推送进度更新到前端
- 支持百分比、时间戳、比特率等详细信息

### 任务控制
- 使用 `child_process` 管理FFmpeg进程
- 支持 SIGTERM 和 SIGKILL 信号控制
- 实现任务状态持久化

### 错误处理
- 捕获FFmpeg的stderr输出
- 分类处理不同类型的错误
- 提供用户友好的错误信息

### 性能优化
- 控制并发任务数量避免系统过载
- 实现内存使用监控
- 大文件分片处理机制

### 格式支持
- 动态检测FFmpeg支持的编解码器
- 输入输出格式兼容性验证
- 自动格式推荐和转换建议

## 风险评估

### 高风险项
1. **FFmpeg依赖**：确保FFmpeg在所有目标平台正确安装和运行
2. **大文件处理**：避免内存溢出和长时间阻塞
3. **进程管理**：正确处理FFmpeg子进程的生命周期

### 中风险项
1. **格式兼容性**：某些特殊格式可能不被支持
2. **性能优化**：并发任务可能影响系统性能
3. **错误恢复**：转换失败后的状态恢复

### 低风险项
1. **UI响应性**：进度更新频率控制
2. **用户体验**：操作提示和确认机制

## 预期成果

完成本方案后，视频处理组件将具备：

1. **真实的媒体转换能力**：支持主流视频/音频格式互转
2. **实时进度追踪**：用户可以看到详细的转换进度
3. **完整的任务控制**：支持暂停、恢复、取消操作
4. **健壮的错误处理**：友好的错误提示和恢复机制
5. **高性能批量处理**：支持大量文件的并发处理
6. **跨平台兼容性**：在Windows和macOS上稳定运行

这将使KOL小红书工具箱成为一个真正实用的媒体处理工具，满足用户的实际需求。