{
    "version": "0.2.0",
    "compounds": [
        {
            "name": "启动 Electron 全部进程 (Vite)",
            "configurations": [
                "启动主进程 (Vite)",
                "附加到渲染进程 (Vite)"
            ],
            "stopAll": true
        }
    ],
    "configurations": [
        {
            "name": "启动主进程 (Vite)",
            "type": "node",
            "request": "launch",
            // **最终修改：** 运行自定义的调试启动脚本
            "program": "${workspaceFolder}/.vscode/.debug.script.mjs", // 指向你创建的脚本
            // 移除 runtimeExecutable 和 args 字段，因为脚本是直接运行的目标
            // "runtimeExecutable": "npm", // 删除此行
            // "args": ["run", "dev"], // 删除此行
            "cwd": "${workspaceFolder}",
            "outputCapture": "std",
            "env": {
                // 虽然脚本内部会设置，但在这里设置可以确保脚本本身也能获取
                "VSCODE_DEBUG": "true",
                "NODE_ENV": "development"
            },
            "sourceMaps": true,
            // outFiles 可以包含脚本路径，以及最终 Electron 主进程的输出路径
            "outFiles": [
                "${workspaceFolder}/.vscode/**/*.js", // 包含调试脚本本身
                "${workspaceFolder}/dist-electron/main/**/*.js" // 仍然包含 Electron 主进程的输出
            ],
            "resolveSourceMapLocations": [
                "${workspaceFolder}/**",
                "!**/node_modules/**"
            ],
            "skipFiles": [
                "<node_internals>/**",
                "**/node_modules/**"
            ],
            "console": "integratedTerminal",
            "autoAttachChildProcesses": true, // **关键：** 自动附加到由脚本启动的 Electron 主进程
            "protocol": "inspector"
        },
        {
            "name": "附加到渲染进程 (Vite)",
            "type": "chrome",
            "request": "attach",
            "port": 9222, // 标准 Electron 调试端口
            "webRoot": "${workspaceFolder}/src",
            "timeout": 30000,
            "sourceMaps": true,
            "urlFilter": "http://localhost:*"
        }
    ]
}