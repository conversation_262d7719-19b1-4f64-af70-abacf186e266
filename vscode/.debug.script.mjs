// .vscode/.debug.script.mjs - 增加捕获 Vite URL 的逻辑
import { spawn } from 'node:child_process'
import path from 'node:path'
import { fileURLToPath } from 'node:url'
import readline from 'node:readline' // 用于逐行读取子进程输出

// 获取当前文件所在的目录 (.vscode)
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
// 计算出项目根目录 (.vscode 的父目录)，已修正
const projectRoot = path.join(__dirname, '..');

const env = { ...process.env, VSCODE_DEBUG: 'true', NODE_ENV: 'development' }

let viteDevServerUrl = null; // 用于存储捕获到的 Vite URL
let viteProcess = null; // 用于存储 npm run dev 子进程对象
let electronProcess = null; // 用于存储 Electron 子进程对象

// --- 步骤 1: 启动 Vite 开发进程 (npm run dev) ---
console.log('[debug.script] Starting Vite dev server (npm run dev)...')
// 使用 'pipe' stdio 以便我们能捕获子进程的输出
viteProcess = spawn('npm', ['run', 'dev'], {
    cwd: projectRoot, // 在正确的项目根目录执行命令
    env: env, // 传递环境变量
    stdio: ['ignore', 'pipe', 'pipe'] // 忽略 stdin, 捕获 stdout 和 stderr
});

// 创建 readline 接口来逐行读取输出
const stdoutLines = readline.createInterface({ input: viteProcess.stdout });
const stderrLines = readline.createInterface({ input: viteProcess.stderr });

// 监听标准输出
stdoutLines.on('line', (line) => {
    console.log(`[vite stdout] ${line}`); // 在 VS Code 终端中打印 Vite 输出
    // 尝试从输出中匹配 Vite 开发服务器的本地 URL
    // Vite 的输出通常包含类似 "Local: http://localhost:xxxx/" 的行
    if (line.includes('Local:') && line.includes('http://')) {
        // **修正正则表达式：** 使用 .* (贪婪匹配) 来捕获完整的 URL
        // 保留捕获组外的 \s* 来匹配 URL 后的空格
        const match = line.match(/Local:\s*(http:\/\/.*)\s*/); // <-- 修改这一行
        if (match && match[1]) {
            // 捕获到的 match[1] 应该是完整的 URL 字符串
            viteDevServerUrl = match[1].trim(); // 使用 trim() 清除捕获到的 URL 字符串可能包含的首尾空格
            console.log(`[debug.script] Detected Vite Dev Server URL: ${viteDevServerUrl}`);
            // 一旦检测到 URL，就可以启动 Electron 了
            launchElectron();
        }
    }
    // 你也可以在这里监听 vite.config.ts 中 onstart 打印的 "[startup] Electron App"
    // 作为主进程和预加载脚本构建完成的信号，但这不足以获得 Vite URL。
    // if (line.includes('[startup] Electron App')) {
    //     console.log('[debug.script] Vite main/preload build ready signal received.');
    //     // 如果 URL 已经找到了，就启动 Electron；否则继续等待 URL
    //     if (viteDevServerUrl) {
    //         launchElectron();
    //     }
    // }
});

// 监听标准错误输出
stderrLines.on('line', (line) => {
    console.error(`[vite stderr] ${line}`); // 打印 Vite 的错误输出
    // 有时 Vite 的 URL 也可能出现在 stderr 中，这里也可以添加捕获逻辑，但通常在 stdout
});


viteProcess.on('error', (err) => {
    console.error('[debug.script] Failed to start npm run dev:', err);
    process.exit(1);
});

viteProcess.on('exit', (code) => {
    console.log(`[debug.script] Vite process exited with code ${code}`);
    // 如果 Vite 进程意外退出，而 Electron 还在运行，强制关闭 Electron
    if (electronProcess && !electronProcess.killed) {
        console.warn('[debug.script] Vite exited while Electron is still running. Forcing Electron exit.');
        electronProcess.kill('SIGTERM');
    }
    // 只有当 Electron 也结束时，才让调试脚本退出
    if (!electronProcess) {
        process.exit(code || 0);
    }
});

// --- 步骤 2: 启动 Electron 的函数 ---
function launchElectron() {
    // 如果 Electron 已经启动了，或者 Vite URL 还没找到，就不做任何事
    if (electronProcess || !viteDevServerUrl) {
        return;
    }

    console.log('[debug.script] Launching Electron with VITE_DEV_SERVER_URL:', viteDevServerUrl);

    // 查找正确项目根目录下的 node_modules/.bin 目录中的 electron 可执行文件
    const electronBinPath = path.join(projectRoot, 'node_modules', '.bin', process.platform === 'win32' ? 'electron.cmd' : 'electron');

    // 启动 Electron 进程，**注入 VITE_DEV_SERVER_URL 环境变量**
    electronProcess = spawn(electronBinPath, ['--remote-debugging-port=9222', projectRoot], { // 传递项目根目录作为 Electron 的启动参数
        cwd: projectRoot, // 在项目根目录启动 Electron
        env: { // 显式设置子进程的环境变量
            ...env, // 继承已有的环境变量 (如 VSCODE_DEBUG, NODE_ENV)
            VITE_DEV_SERVER_URL: viteDevServerUrl // **注入捕获到的 URL**
        },
        stdio: 'inherit' // 将 Electron 的标准输出和错误直接连接到父进程的输出
    });

    electronProcess.on('error', (err) => {
        console.error('[debug.script] Failed to launch Electron:', err);
        if (!viteProcess.killed) viteProcess.kill('SIGTERM'); // 尝试关闭 Vite 进程
        process.exit(1);
    });

    electronProcess.on('exit', (code) => {
        console.log(`[debug.script] Electron process exited with code ${code}`);
        // 当 Electron 退出时，也关闭 Vite 开发服务器进程
        if (!viteProcess.killed) viteProcess.kill('SIGTERM');
        process.exit(code || 0);
    });
}


// 确保在调试脚本接收到退出信号时，能正确关闭子进程
process.on('exit', () => {
    if (viteProcess && !viteProcess.killed) viteProcess.kill('SIGTERM');
    if (electronProcess && !electronProcess.killed) electronProcess.kill('SIGTERM');
});
process.on('SIGINT', () => process.exit()); // 处理 Ctrl+C
process.on('SIGTERM', () => process.exit()); // 处理终止信号