{
  "extends": "@vue/tsconfig/tsconfig.dom.json",
  "compilerOptions": {
    "noImplicitAny": false,
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "paths": {
      "@/*": [
        "./src/*"
      ]
    },
    /* Linting */
    "strict": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": true
  },
  "include": [
    "src/**/*.ts",
    "src/vite-env.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue"
  ]
}