# Media模块重构设计方案

## 项目背景

KOL小红书工具箱应用的Media模块当前存在以下问题：
- 三个子模块（音视频转换、语音识别、图片处理）功能重叠严重
- 代码重复率高达50%以上，维护成本增加
- 状态管理分散，部分模块使用本地状态，部分使用全局Store
- 职责边界模糊，单文件处理功能职责不清

## 重构目标

1. **减少代码重复**：将重复代码降低至20%以下
2. **统一状态管理**：所有任务状态集中到MediaStore管理
3. **清晰职责分工**：单文件处理功能明确职责边界
4. **提升用户体验**：统一操作流程，优化交互体验
5. **提高可维护性**：模块化设计，便于功能扩展

## 重构架构设计

### 1. 新架构层次结构

```
Media.vue (主容器)
├── 单文件处理层
│   ├── VideoConverter.vue - 纯单文件视频转换
│   ├── ASRProcessor.vue - 纯单文件语音识别
│   └── ImageProcessor.vue - 纯单文件图片处理
└── 共享组件层
    ├── FileUploader.vue - 文件上传组件
    ├── OutputDirectorySelector.vue - 输出目录选择
    ├── TaskProgress.vue - 任务进度显示
    ├── TaskControls.vue - 任务控制按钮
    ├── ProcessingOptions.vue - 处理选项配置
    └── TaskResultPanel.vue - 结果展示面板
```

### 2. 状态管理重构

#### MediaStore增强设计

```typescript
// stores/media-store.ts
interface MediaStore {
  // 单文件任务管理
  singleTasks: Map<string, SingleTask>
  
  // 全局统计
  globalStats: {
    totalProcessed: number
    totalSize: number
    totalTime: number
    activeTasksCount: number
  }
  
  // 配置管理
  settings: {
    defaultOutputDir: string
    maxConcurrentTasks: number
  }
}
```

#### 任务类型定义

```typescript
// types/media-tasks.ts
interface BaseTask {
  id: string
  type: 'video-convert' | 'audio-extract' | 'asr' | 'image-process'
  fileName: string
  filePath: string
  outputPath: string
  status: 'pending' | 'processing' | 'completed' | 'error' | 'paused'
  progress: number
  error?: string
  createdAt: number
  startTime?: number
  completedAt?: number
}

interface SingleTask extends BaseTask {
  mode: 'single'
  options: ProcessingOptions
}
```

## 详细实现方案

### 阶段一：公共组件提取（预计1-2天）

#### 1.1 创建共享组件

**FileUploader.vue**
```vue
<template>
  <el-upload
    ref="uploadRef"
    class="unified-uploader"
    :drag="drag"
    :multiple="multiple"
    :accept="acceptedFormats"
    :before-upload="handleBeforeUpload"
    :on-change="handleFileChange"
    :auto-upload="false"
  >
    <div class="upload-content">
      <Icon :icon="uploadIcon" class="upload-icon" />
      <div class="upload-text">{{ uploadText }}</div>
      <div class="upload-hint">{{ uploadHint }}</div>
    </div>
  </el-upload>
</template>

<script setup lang="ts">
interface Props {
  taskType: 'video-convert' | 'audio-extract' | 'asr' | 'image-process'
  multiple?: boolean
  drag?: boolean
}

interface Emits {
  fileSelected: [files: FileInfo[]]
}
</script>
```

**TaskProgress.vue**
```vue
<template>
  <div class="task-progress-container">
    <div class="progress-header">
      <span class="task-name">{{ task.fileName }}</span>
      <span class="progress-percentage">{{ task.progress }}%</span>
    </div>
    
    <el-progress 
      :percentage="task.progress"
      :status="getProgressStatus(task.status)"
      :stroke-width="strokeWidth"
    />
    
    <div class="progress-details" v-if="showDetails">
      <span class="task-status">{{ getStatusLabel(task.status) }}</span>
      <span class="elapsed-time" v-if="task.startTime">
        {{ formatElapsedTime(task.startTime) }}
      </span>
    </div>
    
    <div class="error-message" v-if="task.error">
      <Icon icon="mdi:alert-circle" />
      {{ task.error }}
    </div>
  </div>
</template>
```

**ProcessingOptions.vue**
```vue
<template>
  <div class="processing-options">
    <component 
      :is="getOptionsComponent()" 
      v-model="options"
      :compact="compact"
      @update:modelValue="handleOptionsChange"
    />
  </div>
</template>

<script setup lang="ts">
// 根据任务类型动态加载对应的选项组件
const getOptionsComponent = () => {
  const components = {
    'video-convert': 'VideoConvertOptions',
    'audio-extract': 'AudioExtractOptions', 
    'asr': 'ASROptions',
    'image-process': 'ImageProcessOptions'
  }
  return components[props.taskType]
}
</script>
```

#### 1.2 选项子组件设计

**VideoConvertOptions.vue**
```vue
<template>
  <div class="video-options">
    <el-form-item label="输出格式">
      <el-select v-model="options.format">
        <el-option label="MP4" value="mp4" />
        <el-option label="AVI" value="avi" />
        <el-option label="MOV" value="mov" />
      </el-select>
    </el-form-item>
    
    <el-form-item label="视频质量">
      <el-select v-model="options.quality">
        <el-option label="高质量" value="high" />
        <el-option label="标准质量" value="medium" />
        <el-option label="压缩质量" value="low" />
      </el-select>
    </el-form-item>
    
    <el-form-item label="分辨率">
      <el-select v-model="options.resolution">
        <el-option label="保持原分辨率" value="original" />
        <el-option label="1920x1080" value="1080p" />
        <el-option label="1280x720" value="720p" />
      </el-select>
    </el-form-item>
  </div>
</template>
```

### 阶段二：单文件处理模块重构（预计2-3天）

#### 2.1 VideoConverter.vue 重构

```vue
<template>
  <div class="video-converter">
    <div class="converter-header">
      <h3>
        <Icon icon="mdi:video-outline" />
        视频转换
      </h3>
      <div class="quick-actions">
        <el-button size="small" @click="showBatchCreator = true">
          <Icon icon="mdi:playlist-plus" />
          创建批量任务
        </el-button>
      </div>
    </div>

    <!-- 文件上传区域 -->
    <FileUploader 
      task-type="video-convert"
      :multiple="false"
      @file-selected="handleFileSelected"
    />

    <!-- 处理选项 -->
    <ProcessingOptions
      task-type="video-convert"
      v-model="processingOptions"
      :compact="false"
    />

    <!-- 输出目录选择 -->
    <OutputDirectorySelector v-model="outputDirectory" />

    <!-- 开始处理按钮 -->
    <div class="action-buttons">
      <el-button 
        type="primary" 
        :disabled="!canStartProcessing"
        @click="startProcessing"
      >
        <Icon icon="mdi:play" />
        开始转换
      </el-button>
    </div>

    <!-- 当前任务进度 -->
    <TaskProgress 
      v-if="currentTask"
      :task="currentTask"
      :show-details="true"
      @cancel="cancelTask"
      @retry="retryTask"
    />

    <!-- 结果展示 -->
    <TaskResultPanel
      v-if="lastResult"
      :result="lastResult"
      @open-file="openResultFile"
      @create-new="resetConverter"
    />

    <!-- 批量任务创建对话框 -->
    <BatchTaskCreator
      v-model:visible="showBatchCreator"
      task-type="video-convert"
      :default-options="processingOptions"
      @created="handleBatchCreated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useMediaStore } from '@/stores/media-store'
import FileUploader from '@/components/media/shared/FileUploader.vue'
import ProcessingOptions from '@/components/media/shared/ProcessingOptions.vue'
import OutputDirectorySelector from '@/components/media/shared/OutputDirectorySelector.vue'
import TaskProgress from '@/components/media/shared/TaskProgress.vue'
import TaskResultPanel from '@/components/media/shared/TaskResultPanel.vue'
import BatchTaskCreator from '@/components/media/shared/BatchTaskCreator.vue'

const mediaStore = useMediaStore()

// 组件状态
const selectedFile = ref<FileInfo | null>(null)
const processingOptions = ref({})
const outputDirectory = ref('')
const showBatchCreator = ref(false)

// 计算属性
const currentTask = computed(() => {
  return mediaStore.getSingleTaskByType('video-convert')
})

const lastResult = computed(() => {
  return mediaStore.getLastCompletedTask('video-convert')
})

const canStartProcessing = computed(() => {
  return selectedFile.value && outputDirectory.value && !currentTask.value
})

// 事件处理
const handleFileSelected = (files: FileInfo[]) => {
  selectedFile.value = files[0]
}

const startProcessing = async () => {
  if (!selectedFile.value) return

  const taskConfig = {
    type: 'video-convert' as const,
    fileName: selectedFile.value.name,
    filePath: selectedFile.value.path,
    outputDirectory: outputDirectory.value,
    options: processingOptions.value
  }

  await mediaStore.createSingleTask(taskConfig)
}

const cancelTask = () => {
  if (currentTask.value) {
    mediaStore.cancelTask(currentTask.value.id)
  }
}

const retryTask = () => {
  if (currentTask.value) {
    mediaStore.retryTask(currentTask.value.id)
  }
}

const resetConverter = () => {
  selectedFile.value = null
  // 保持选项和输出目录不变，提升用户体验
}

const handleBatchCreated = (batchInfo: any) => {
  showBatchCreator.value = false
  // 可以选择是否跳转到批量处理页面
  ElMessage.success('批量任务已创建，可在批量处理页面查看')
}

const openResultFile = (filePath: string) => {
  window.electronAPI.app.showItemInFolder(filePath)
}
</script>
```

#### 2.2 ASRProcessor.vue 重构

```vue
<template>
  <div class="asr-processor">
    <div class="processor-header">
      <h3>
        <Icon icon="mdi:microphone" />
        语音识别
      </h3>
      <div class="quick-actions">
        <el-button size="small" @click="showBatchCreator = true">
          <Icon icon="mdi:playlist-plus" />
          创建批量任务
        </el-button>
      </div>
    </div>

    <!-- 文件上传区域 -->
    <FileUploader 
      task-type="asr"
      :multiple="false"
      @file-selected="handleFileSelected"
    />

    <!-- 处理选项 -->
    <ProcessingOptions
      task-type="asr"
      v-model="processingOptions"
      :compact="false"
    />

    <!-- 输出目录选择 -->
    <OutputDirectorySelector v-model="outputDirectory" />

    <!-- 开始处理按钮 -->
    <div class="action-buttons">
      <el-button 
        type="primary" 
        :disabled="!canStartProcessing"
        @click="startProcessing"
      >
        <Icon icon="mdi:play" />
        开始识别
      </el-button>
    </div>

    <!-- 当前任务进度 -->
    <TaskProgress 
      v-if="currentTask"
      :task="currentTask"
      :show-details="true"
      @cancel="cancelTask"
      @retry="retryTask"
    />

    <!-- ASR特有的结果展示 -->
    <ASRResultPanel
      v-if="lastResult"
      :result="lastResult"
      @export="exportResult"
      @create-new="resetProcessor"
    />
  </div>
</template>
```

#### 2.3 ImageProcessor.vue 重构

```vue
<template>
  <div class="image-processor">
    <div class="processor-header">
      <h3>
        <Icon icon="mdi:image-outline" />
        图片处理
      </h3>
      <div class="quick-actions">
        <el-button size="small" @click="showBatchCreator = true">
          <Icon icon="mdi:playlist-plus" />
          创建批量任务
        </el-button>
      </div>
    </div>

    <!-- 文件上传区域 - 支持多选 -->
    <FileUploader 
      task-type="image-process"
      :multiple="true"
      @file-selected="handleFilesSelected"
    />

    <!-- 图片预览网格 -->
    <ImagePreviewGrid
      v-if="selectedFiles.length > 0"
      :files="selectedFiles"
      @remove="removeFile"
    />

    <!-- 处理选项 -->
    <ProcessingOptions
      task-type="image-process"
      v-model="processingOptions"
      :compact="false"
    />

    <!-- 输出目录选择 -->
    <OutputDirectorySelector v-model="outputDirectory" />

    <!-- 开始处理按钮 -->
    <div class="action-buttons">
      <el-button 
        type="primary" 
        :disabled="!canStartProcessing"
        @click="startProcessing"
      >
        <Icon icon="mdi:play" />
        开始处理 ({{ selectedFiles.length }}张图片)
      </el-button>
    </div>

    <!-- 当前任务进度 -->
    <div v-if="currentTasks.length > 0">
      <h4>处理进度</h4>
      <TaskProgress 
        v-for="task in currentTasks"
        :key="task.id"
        :task="task"
        :show-details="false"
        :stroke-width="4"
        @cancel="cancelTask(task.id)"
      />
    </div>

    <!-- 处理结果 -->
    <ImageResultGrid
      v-if="completedResults.length > 0"
      :results="completedResults"
      @open-folder="openResultFolder"
      @create-new="resetProcessor"
    />
  </div>
</template>
```

### 阶段三：批量处理模块增强（预计2-3天）

#### 3.1 BatchProcessor.vue 重构

```vue
<template>
  <div class="batch-processor">
    <div class="batch-header">
      <h3>
        <Icon icon="mdi:playlist-check" />
        批量处理中心
      </h3>
      <div class="header-stats">
        <el-statistic title="队列任务" :value="queueStats.total" />
        <el-statistic title="进行中" :value="queueStats.processing" />
        <el-statistic title="已完成" :value="queueStats.completed" />
      </div>
    </div>

    <!-- 批量任务创建 -->
    <div class="batch-creator">
      <BatchTaskCreator
        :visible="true"
        :allow-mixed-types="true"
        @created="handleBatchCreated"
      />
    </div>

    <!-- 任务队列管理 -->
    <div class="queue-management">
      <div class="queue-controls">
        <el-button-group>
          <el-button @click="startAllPending">
            <Icon icon="mdi:play-circle" />
            启动全部
          </el-button>
          <el-button @click="pauseAllRunning">
            <Icon icon="mdi:pause-circle" />
            暂停全部
          </el-button>
          <el-button type="danger" @click="clearCompleted">
            <Icon icon="mdi:trash-can" />
            清理已完成
          </el-button>
        </el-button-group>

        <div class="queue-settings">
          <el-tooltip content="最大并发任务数">
            <el-input-number
              v-model="maxConcurrentTasks"
              :min="1"
              :max="10"
              size="small"
              @change="updateConcurrentLimit"
            />
          </el-tooltip>
        </div>
      </div>

      <!-- 任务过滤器 -->
      <BatchTaskFilters
        v-model:status-filter="statusFilter"
        v-model:type-filter="typeFilter"
        :task-counts="taskCounts"
      />
    </div>

    <!-- 批量任务列表 -->
    <div class="batch-list">
      <BatchTaskGroup
        v-for="batch in filteredBatches"
        :key="batch.id"
        :batch="batch"
        @start="startBatch"
        @pause="pauseBatch"
        @cancel="cancelBatch"
        @remove="removeBatch"
        @task-action="handleTaskAction"
      />
    </div>

    <!-- 空状态 -->
    <div class="empty-state" v-if="filteredBatches.length === 0">
      <Icon icon="mdi:playlist-remove" class="empty-icon" />
      <div class="empty-text">暂无批量任务</div>
      <div class="empty-hint">创建批量任务来高效处理多个文件</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useMediaStore } from '@/stores/media-store'
import BatchTaskCreator from '@/components/media/batch/BatchTaskCreator.vue'
import BatchTaskFilters from '@/components/media/batch/BatchTaskFilters.vue'
import BatchTaskGroup from '@/components/media/batch/BatchTaskGroup.vue'

const mediaStore = useMediaStore()

// 组件状态
const statusFilter = ref('all')
const typeFilter = ref('all')
const maxConcurrentTasks = ref(3)

// 计算属性
const allBatches = computed(() => mediaStore.batchTasks)

const filteredBatches = computed(() => {
  return allBatches.value.filter(batch => {
    const statusMatch = statusFilter.value === 'all' || 
                       batch.status === statusFilter.value
    const typeMatch = typeFilter.value === 'all' || 
                     batch.type === typeFilter.value
    return statusMatch && typeMatch
  })
})

const queueStats = computed(() => ({
  total: allBatches.value.length,
  processing: allBatches.value.filter(b => b.status === 'processing').length,
  completed: allBatches.value.filter(b => b.status === 'completed').length
}))

const taskCounts = computed(() => {
  const counts = {
    all: 0,
    pending: 0,
    processing: 0,
    completed: 0,
    error: 0
  }
  
  allBatches.value.forEach(batch => {
    batch.tasks.forEach(task => {
      counts.all++
      counts[task.status]++
    })
  })
  
  return counts
})

// 事件处理
const handleBatchCreated = (batchInfo: any) => {
  ElMessage.success(`批量任务"${batchInfo.name}"已创建`)
}

const startAllPending = () => {
  const pendingBatches = allBatches.value.filter(b => b.status === 'pending')
  pendingBatches.forEach(batch => {
    mediaStore.startBatch(batch.id)
  })
}

const pauseAllRunning = () => {
  const runningBatches = allBatches.value.filter(b => b.status === 'processing')
  runningBatches.forEach(batch => {
    mediaStore.pauseBatch(batch.id)
  })
}

const clearCompleted = async () => {
  await ElMessageBox.confirm('确定要清理所有已完成的任务吗？', '确认清理')
  const completedBatches = allBatches.value.filter(b => b.status === 'completed')
  completedBatches.forEach(batch => {
    mediaStore.removeBatch(batch.id)
  })
}

const updateConcurrentLimit = (value: number) => {
  mediaStore.updateSettings({ maxConcurrentTasks: value })
}

const startBatch = (batchId: string) => {
  mediaStore.startBatch(batchId)
}

const pauseBatch = (batchId: string) => {
  mediaStore.pauseBatch(batchId)
}

const cancelBatch = (batchId: string) => {
  mediaStore.cancelBatch(batchId)
}

const removeBatch = (batchId: string) => {
  mediaStore.removeBatch(batchId)
}

const handleTaskAction = (action: string, taskId: string) => {
  switch (action) {
    case 'retry':
      mediaStore.retryTask(taskId)
      break
    case 'cancel':
      mediaStore.cancelTask(taskId)
      break
    case 'remove':
      mediaStore.removeTask(taskId)
      break
  }
}
</script>
```

#### 3.2 BatchTaskCreator.vue（增强版）

```vue
<template>
  <div class="batch-task-creator">
    <el-card class="creator-card">
      <template #header>
        <div class="creator-header">
          <span>创建批量任务</span>
          <el-switch
            v-if="allowMixedTypes"
            v-model="mixedTypeMode"
            active-text="混合任务"
            inactive-text="单一类型"
          />
        </div>
      </template>

      <el-form :model="batchForm" label-width="100px">
        <!-- 基本信息 -->
        <el-form-item label="任务名称" required>
          <el-input 
            v-model="batchForm.name" 
            placeholder="输入批量任务名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <!-- 任务类型选择 -->
        <el-form-item label="任务类型" required v-if="!mixedTypeMode">
          <el-radio-group v-model="batchForm.type">
            <el-radio-button value="video-convert">视频转换</el-radio-button>
            <el-radio-button value="audio-extract">音频提取</el-radio-button>
            <el-radio-button value="asr">语音识别</el-radio-button>
            <el-radio-button value="image-process">图片处理</el-radio-button>
          </el-radio-group>
        </el-form-item>

        <!-- 文件上传区域 -->
        <el-form-item label="选择文件" required>
          <div class="file-upload-section">
            <!-- 单一类型文件上传 -->
            <FileUploader
              v-if="!mixedTypeMode && batchForm.type"
              :task-type="batchForm.type"
              :multiple="true"
              :drag="true"
              @file-selected="handleSingleTypeFiles"
            />

            <!-- 混合类型文件上传 -->
            <MixedFileUploader
              v-else-if="mixedTypeMode"
              @files-selected="handleMixedTypeFiles"
            />
          </div>

          <!-- 文件列表预览 -->
          <BatchFileList
            v-if="batchForm.files.length > 0"
            :files="batchForm.files"
            :show-type="mixedTypeMode"
            @remove="removeFile"
            @change-type="changeFileType"
          />
        </el-form-item>

        <!-- 输出设置 -->
        <el-form-item label="输出目录" required>
          <OutputDirectorySelector v-model="batchForm.outputDirectory" />
        </el-form-item>

        <!-- 处理选项 -->
        <el-form-item label="处理选项" v-if="!mixedTypeMode && batchForm.type">
          <ProcessingOptions
            :task-type="batchForm.type"
            v-model="batchForm.options"
            :compact="true"
          />
        </el-form-item>

        <!-- 混合类型的处理选项 -->
        <el-form-item label="处理选项" v-else-if="mixedTypeMode">
          <MixedProcessingOptions
            :file-types="uniqueFileTypes"
            v-model="batchForm.mixedOptions"
          />
        </el-form-item>

        <!-- 高级设置 -->
        <el-form-item label="高级设置">
          <el-collapse>
            <el-collapse-item title="任务调度设置" name="schedule">
              <div class="advanced-options">
                <el-form-item label="并发数量">
                  <el-input-number
                    v-model="batchForm.concurrency"
                    :min="1"
                    :max="10"
                    size="small"
                  />
                </el-form-item>

                <el-form-item label="优先级">
                  <el-select v-model="batchForm.priority">
                    <el-option label="高" value="high" />
                    <el-option label="普通" value="normal" />
                    <el-option label="低" value="low" />
                  </el-select>
                </el-form-item>

                <el-form-item label="创建后立即启动">
                  <el-switch v-model="batchForm.autoStart" />
                </el-form-item>
              </div>
            </el-collapse-item>
          </el-collapse>
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item>
          <div class="creator-actions">
            <el-button @click="resetForm">重置</el-button>
            <el-button 
              type="primary" 
              :disabled="!canCreateBatch"
              @click="createBatch"
            >
              <Icon icon="mdi:plus-circle" />
              创建批量任务 ({{ batchForm.files.length }}个文件)
            </el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>
```

### 阶段四：状态管理统一（预计1-2天）

#### 4.1 MediaStore完整实现

```typescript
// stores/media-store.ts
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useMediaStore = defineStore('media', () => {
  // 状态
  const singleTasks = ref<Map<string, SingleTask>>(new Map())
  const batchTasks = ref<Map<string, BatchTask>>(new Map())
  const processingQueue = ref<string[]>([])
  const settings = ref({
    defaultOutputDir: '',
    maxConcurrentTasks: 3,
    autoStartBatch: false,
    preserveOriginalFiles: true
  })

  // 统计计算属性
  const globalStats = computed(() => {
    let totalProcessed = 0
    let totalSize = 0
    let totalTime = 0
    let activeTasksCount = 0

    // 统计单文件任务
    singleTasks.value.forEach(task => {
      if (task.status === 'completed') {
        totalProcessed++
        totalSize += task.fileSize || 0
        totalTime += (task.completedAt || 0) - (task.startTime || 0)
      }
      if (task.status === 'processing') {
        activeTasksCount++
      }
    })

    // 统计批量任务
    batchTasks.value.forEach(batch => {
      batch.tasks.forEach(task => {
        if (task.status === 'completed') {
          totalProcessed++
          totalSize += task.fileSize || 0
          totalTime += (task.completedAt || 0) - (task.startTime || 0)
        }
        if (task.status === 'processing') {
          activeTasksCount++
        }
      })
    })

    return {
      totalProcessed,
      totalSize,
      totalTime,
      activeTasksCount
    }
  })

  // 任务管理方法
  const createSingleTask = async (config: TaskConfig) => {
    const task: SingleTask = {
      id: generateTaskId(),
      mode: 'single',
      type: config.type,
      fileName: config.fileName,
      filePath: config.filePath,
      outputPath: `${config.outputDirectory}/${config.fileName}`,
      status: 'pending',
      progress: 0,
      options: config.options,
      createdAt: Date.now()
    }

    singleTasks.value.set(task.id, task)
    
    // 立即开始处理单文件任务
    await startTask(task.id)
    
    return task.id
  }

  const createBatchTask = async (config: BatchConfig) => {
    const batchId = generateBatchId()
    
    const tasks = config.files.map(file => ({
      id: generateTaskId(),
      mode: 'batch' as const,
      batchId,
      batchName: config.name,
      type: file.taskType || config.type,
      fileName: file.name,
      filePath: file.path,
      outputPath: `${config.outputDirectory}/${file.name}`,
      status: 'pending' as const,
      progress: 0,
      options: file.options || config.options,
      createdAt: Date.now(),
      totalTasks: config.files.length,
      completedTasks: 0
    }))

    const batch: BatchTask = {
      id: batchId,
      name: config.name,
      type: config.type,
      tasks,
      status: 'pending',
      progress: 0,
      priority: config.priority || 'normal',
      concurrency: config.concurrency || settings.value.maxConcurrentTasks,
      createdAt: Date.now()
    }

    batchTasks.value.set(batchId, batch)

    if (config.autoStart) {
      await startBatch(batchId)
    }

    return batchId
  }

  const startTask = async (taskId: string) => {
    const task = findTask(taskId)
    if (!task || task.status !== 'pending') return

    task.status = 'processing'
    task.startTime = Date.now()
    task.progress = 0

    try {
      // 根据任务类型调用对应的处理方法
      await processTask(task)
      
      task.status = 'completed'
      task.progress = 100
      task.completedAt = Date.now()
    } catch (error) {
      task.status = 'error'
      task.error = error instanceof Error ? error.message : '处理失败'
    }
  }

  const startBatch = async (batchId: string) => {
    const batch = batchTasks.value.get(batchId)
    if (!batch || batch.status !== 'pending') return

    batch.status = 'processing'
    
    // 根据并发设置启动任务
    const pendingTasks = batch.tasks.filter(t => t.status === 'pending')
    const concurrentLimit = batch.concurrency
    
    for (let i = 0; i < Math.min(concurrentLimit, pendingTasks.length); i++) {
      startTask(pendingTasks[i].id)
    }
  }

  const processTask = async (task: BaseTask) => {
    // 根据任务类型调用对应的API
    switch (task.type) {
      case 'video-convert':
        return await window.electronAPI.media.convertVideo({
          inputPath: task.filePath,
          outputPath: task.outputPath,
          options: task.options,
          onProgress: (progress) => {
            task.progress = progress
          }
        })
      
      case 'asr':
        return await window.electronAPI.media.extractText({
          inputPath: task.filePath,
          outputPath: task.outputPath,
          options: task.options,
          onProgress: (progress) => {
            task.progress = progress
          }
        })
      
      case 'image-process':
        return await window.electronAPI.media.processImages({
          inputPaths: [task.filePath],
          outputDirectory: path.dirname(task.outputPath),
          options: task.options,
          onProgress: (progress) => {
            task.progress = progress
          }
        })
      
      default:
        throw new Error(`不支持的任务类型: ${task.type}`)
    }
  }

  // 任务控制方法
  const pauseTask = (taskId: string) => {
    const task = findTask(taskId)
    if (task && task.status === 'processing') {
      task.status = 'paused'
      // 调用对应的暂停API
    }
  }

  const cancelTask = (taskId: string) => {
    const task = findTask(taskId)
    if (task && ['processing', 'pending', 'paused'].includes(task.status)) {
      task.status = 'cancelled'
      // 调用对应的取消API
    }
  }

  const retryTask = async (taskId: string) => {
    const task = findTask(taskId)
    if (task && ['error', 'cancelled'].includes(task.status)) {
      task.status = 'pending'
      task.progress = 0
      task.error = undefined
      await startTask(taskId)
    }
  }

  const removeTask = (taskId: string) => {
    // 从单文件任务中移除
    if (singleTasks.value.has(taskId)) {
      singleTasks.value.delete(taskId)
      return
    }

    // 从批量任务中移除
    batchTasks.value.forEach(batch => {
      const taskIndex = batch.tasks.findIndex(t => t.id === taskId)
      if (taskIndex > -1) {
        batch.tasks.splice(taskIndex, 1)
      }
    })
  }

  // 批量任务管理
  const pauseBatch = (batchId: string) => {
    const batch = batchTasks.value.get(batchId)
    if (batch && batch.status === 'processing') {
      batch.status = 'paused'
      batch.tasks.forEach(task => {
        if (task.status === 'processing') {
          pauseTask(task.id)
        }
      })
    }
  }

  const cancelBatch = (batchId: string) => {
    const batch = batchTasks.value.get(batchId)
    if (batch) {
      batch.status = 'cancelled'
      batch.tasks.forEach(task => {
        if (['pending', 'processing', 'paused'].includes(task.status)) {
          cancelTask(task.id)
        }
      })
    }
  }

  const removeBatch = (batchId: string) => {
    const batch = batchTasks.value.get(batchId)
    if (batch) {
      // 取消所有未完成的任务
      batch.tasks.forEach(task => {
        if (task.status === 'processing') {
          cancelTask(task.id)
        }
      })
      batchTasks.value.delete(batchId)
    }
  }

  // 工具方法
  const findTask = (taskId: string): BaseTask | undefined => {
    // 先在单文件任务中查找
    const singleTask = singleTasks.value.get(taskId)
    if (singleTask) return singleTask

    // 再在批量任务中查找
    for (const batch of batchTasks.value.values()) {
      const batchTask = batch.tasks.find(t => t.id === taskId)
      if (batchTask) return batchTask
    }

    return undefined
  }

  const generateTaskId = () => {
    return `task_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  const generateBatchId = () => {
    return `batch_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  const getSingleTaskByType = (type: string) => {
    for (const task of singleTasks.value.values()) {
      if (task.type === type && task.status === 'processing') {
        return task
      }
    }
    return null
  }

  const getLastCompletedTask = (type: string) => {
    let lastTask = null
    let lastTime = 0

    for (const task of singleTasks.value.values()) {
      if (task.type === type && task.status === 'completed' && 
          task.completedAt && task.completedAt > lastTime) {
        lastTask = task
        lastTime = task.completedAt
      }
    }

    return lastTask
  }

  // 持久化方法
  const saveTasks = () => {
    const data = {
      singleTasks: Array.from(singleTasks.value.entries()),
      batchTasks: Array.from(batchTasks.value.entries()),
      settings: settings.value
    }
    localStorage.setItem('media-store-data', JSON.stringify(data))
  }

  const loadTasks = () => {
    try {
      const data = localStorage.getItem('media-store-data')
      if (data) {
        const parsed = JSON.parse(data)
        singleTasks.value = new Map(parsed.singleTasks || [])
        batchTasks.value = new Map(parsed.batchTasks || [])
        settings.value = { ...settings.value, ...(parsed.settings || {}) }
      }
    } catch (error) {
      console.error('加载任务数据失败:', error)
    }
  }

  const initialize = async () => {
    loadTasks()
    
    // 清理已完成或错误的任务（可选）
    const cleanupCompleted = () => {
      const now = Date.now()
      const oneDay = 24 * 60 * 60 * 1000

      // 清理一天前完成的单文件任务
      for (const [id, task] of singleTasks.value.entries()) {
        if (task.status === 'completed' && 
            task.completedAt && 
            now - task.completedAt > oneDay) {
          singleTasks.value.delete(id)
        }
      }

      // 清理一天前完成的批量任务
      for (const [id, batch] of batchTasks.value.entries()) {
        if (batch.status === 'completed' && 
            batch.completedAt && 
            now - batch.completedAt > oneDay) {
          batchTasks.value.delete(id)
        }
      }
    }

    // 可选的清理操作
    // cleanupCompleted()
  }

  // 监听状态变化，自动保存
  watch([singleTasks, batchTasks, settings], saveTasks, { deep: true })

  return {
    // 状态
    singleTasks: computed(() => Array.from(singleTasks.value.values())),
    batchTasks: computed(() => Array.from(batchTasks.value.values())),
    globalStats,
    settings: readonly(settings),

    // 方法
    createSingleTask,
    createBatchTask,
    startTask,
    startBatch,
    pauseTask,
    pauseBatch,
    cancelTask,
    cancelBatch,
    retryTask,
    removeTask,
    removeBatch,
    getSingleTaskByType,
    getLastCompletedTask,
    initialize
  }
})
```

### 阶段五：UI优化和用户体验提升（预计1-2天）

#### 5.1 Media.vue主页面重构

```vue
<template>
  <div class="media-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-title">
        <h2>
          <Icon icon="mdi:multimedia" />
          多媒体处理中心
        </h2>
        <div class="header-stats">
          <el-tag size="small">
            进行中: {{ globalStats.activeTasksCount }}
          </el-tag>
          <el-tag size="small" type="success">
            已完成: {{ globalStats.totalProcessed }}
          </el-tag>
        </div>
      </div>

      <div class="header-actions">
        <el-button @click="showGlobalSettings = true">
          <Icon icon="mdi:cog" />
          设置
        </el-button>
        <el-button @click="refreshStats">
          <Icon icon="mdi:refresh" />
          刷新
        </el-button>
      </div>
    </div>

    <!-- 导航标签页 -->
    <div class="module-navigation">
      <el-tabs v-model="activeModule" @tab-change="handleModuleChange">
        <el-tab-pane label="视频转换" name="video">
          <template #label>
            <div class="tab-label">
              <Icon icon="mdi:video-outline" />
              <span>视频转换</span>
              <el-badge 
                v-if="getModuleTaskCount('video-convert') > 0"
                :value="getModuleTaskCount('video-convert')"
                class="tab-badge"
              />
            </div>
          </template>
        </el-tab-pane>

        <el-tab-pane label="语音识别" name="asr">
          <template #label>
            <div class="tab-label">
              <Icon icon="mdi:microphone" />
              <span>语音识别</span>
              <el-badge 
                v-if="getModuleTaskCount('asr') > 0"
                :value="getModuleTaskCount('asr')"
                class="tab-badge"
              />
            </div>
          </template>
        </el-tab-pane>

        <el-tab-pane label="图片处理" name="image">
          <template #label>
            <div class="tab-label">
              <Icon icon="mdi:image-outline" />
              <span>图片处理</span>
              <el-badge 
                v-if="getModuleTaskCount('image-process') > 0"
                :value="getModuleTaskCount('image-process')"
                class="tab-badge"
              />
            </div>
          </template>
        </el-tab-pane>

        <el-tab-pane label="批量处理" name="batch">
          <template #label>
            <div class="tab-label">
              <Icon icon="mdi:playlist-check" />
              <span>批量处理</span>
              <el-badge 
                v-if="totalBatchTasks > 0"
                :value="totalBatchTasks"
                class="tab-badge"
              />
            </div>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 模块内容区域 -->
    <div class="module-content">
      <Transition name="fade" mode="out-in">
        <VideoConverter v-if="activeModule === 'video'" />
        <ASRProcessor v-else-if="activeModule === 'asr'" />
        <ImageProcessor v-else-if="activeModule === 'image'" />
        <BatchProcessor v-else-if="activeModule === 'batch'" />
      </Transition>
    </div>

    <!-- 全局任务状态栏 -->
    <GlobalTaskStatusBar
      v-if="globalStats.activeTasksCount > 0"
      :active-tasks="activeTasks"
      :show-details="false"
      @show-details="showTaskDetails = true"
    />

    <!-- 全局设置对话框 -->
    <GlobalSettingsDialog
      v-model:visible="showGlobalSettings"
      :settings="settings"
      @update="updateGlobalSettings"
    />

    <!-- 任务详情面板 -->
    <TaskDetailsPanel
      v-model:visible="showTaskDetails"
      :tasks="activeTasks"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useMediaStore } from '@/stores/media-store'
import VideoConverter from '@/components/media/single/VideoConverter.vue'
import ASRProcessor from '@/components/media/single/ASRProcessor.vue'
import ImageProcessor from '@/components/media/single/ImageProcessor.vue'
import BatchProcessor from '@/components/media/batch/BatchProcessor.vue'
import GlobalTaskStatusBar from '@/components/media/shared/GlobalTaskStatusBar.vue'
import GlobalSettingsDialog from '@/components/media/shared/GlobalSettingsDialog.vue'
import TaskDetailsPanel from '@/components/media/shared/TaskDetailsPanel.vue'

const mediaStore = useMediaStore()

// 组件状态
const activeModule = ref('video')
const showGlobalSettings = ref(false)
const showTaskDetails = ref(false)

// 计算属性
const globalStats = computed(() => mediaStore.globalStats)
const settings = computed(() => mediaStore.settings)
const totalBatchTasks = computed(() => mediaStore.batchTasks.length)

const activeTasks = computed(() => {
  const tasks = []
  
  // 收集单文件任务
  mediaStore.singleTasks.forEach(task => {
    if (task.status === 'processing') {
      tasks.push(task)
    }
  })
  
  // 收集批量任务中的活跃任务
  mediaStore.batchTasks.forEach(batch => {
    batch.tasks.forEach(task => {
      if (task.status === 'processing') {
        tasks.push({ ...task, batchName: batch.name })
      }
    })
  })
  
  return tasks
})

// 生命周期
onMounted(async () => {
  await mediaStore.initialize()
})

// 事件处理
const handleModuleChange = (moduleName: string) => {
  console.log(`切换到模块: ${moduleName}`)
}

const getModuleTaskCount = (taskType: string) => {
  let count = 0
  
  // 统计单文件任务
  mediaStore.singleTasks.forEach(task => {
    if (task.type === taskType && task.status === 'processing') {
      count++
    }
  })
  
  // 统计批量任务中的相关任务
  mediaStore.batchTasks.forEach(batch => {
    if (batch.type === taskType && batch.status === 'processing') {
      count += batch.tasks.filter(t => t.status === 'processing').length
    }
  })
  
  return count
}

const refreshStats = async () => {
  // 刷新统计信息，可能需要清理过期任务等
  ElMessage.success('统计信息已刷新')
}

const updateGlobalSettings = (newSettings: any) => {
  mediaStore.updateSettings(newSettings)
  ElMessage.success('设置已保存')
}
</script>

<style scoped lang="scss">
@use '@/assets/styles/index.scss' as *;

.media-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $spacing-base $spacing-lg;
  background: $background-card;
  border-bottom: 1px solid $border-lighter;

  .header-title {
    display: flex;
    align-items: center;
    gap: $spacing-base;

    h2 {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0;
      color: $text-primary;
      font-size: 20px;
      font-weight: 600;
    }

    .header-stats {
      display: flex;
      gap: $spacing-small;
    }
  }

  .header-actions {
    display: flex;
    gap: $spacing-small;
  }
}

.module-navigation {
  background: $background-card;
  border-bottom: 1px solid $border-lighter;

  :deep(.el-tabs__header) {
    margin: 0;
    padding: 0 $spacing-lg;
  }

  .tab-label {
    display: flex;
    align-items: center;
    gap: 6px;
    position: relative;

    .tab-badge {
      :deep(.el-badge__content) {
        font-size: 10px;
        min-width: 16px;
        height: 16px;
        line-height: 16px;
      }
    }
  }
}

.module-content {
  flex: 1;
  overflow-y: auto;
  padding: $spacing-lg;
}

// 过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
```

## 实施计划

### 时间安排（总计：7-10天）

| 阶段 | 任务 | 预计时间 | 负责人 |
|------|------|----------|--------|
| 阶段一 | 提取公共组件 | 1-2天 | 前端开发 |
| 阶段二 | 重构单文件处理模块 | 2-3天 | 前端开发 |
| 阶段三 | 增强批量处理模块 | 2-3天 | 前端开发 |
| 阶段四 | 统一状态管理 | 1-2天 | 前端开发 |
| 阶段五 | UI优化和测试 | 1-2天 | 前端开发 + 测试 |

### 风险评估

| 风险项 | 影响程度 | 应对措施 |
|--------|----------|----------|
| 状态迁移数据丢失 | 高 | 实施前完整备份，渐进式迁移 |
| 用户操作习惯改变 | 中 | 保持核心交互不变，添加引导提示 |
| 性能影响 | 中 | 分阶段测试，优化关键路径 |
| API兼容性 | 低 | 保持现有API接口不变 |

### 验收标准

1. **功能完整性**：所有原有功能正常工作
2. **代码质量**：重复代码减少50%以上，ESLint检查通过
3. **性能表现**：页面加载时间不超过原来的120%
4. **用户体验**：核心操作流程保持一致，新增便利功能
5. **稳定性**：连续运行24小时无内存泄漏或崩溃

### 回滚方案

1. **代码回滚**：使用Git分支管理，可快速回滚到重构前状态
2. **数据迁移**：保留原有数据结构兼容性，支持降级
3. **配置还原**：提供配置导入导出功能，快速恢复用户设置

## 预期收益

### 开发效率提升
- **减少50%以上重复代码**，降低维护成本
- **统一组件库**，加速新功能开发
- **清晰的职责分工**，便于团队协作

### 用户体验优化
- **统一的操作流程**，降低学习成本
- **更好的任务管理**，提升工作效率
- **实时状态反馈**，增强操作信心

### 系统稳定性
- **集中状态管理**，减少数据不一致
- **更好的错误处理**，提升系统健壮性
- **规范的组件设计**，便于测试和调试

## 后续优化方向

1. **任务调度优化**：支持定时任务、条件触发等高级功能
2. **云端同步**：支持任务配置和历史记录云端同步
3. **插件化架构**：支持第三方处理器插件
4. **批量模板**：预设常用的批量处理模板
5. **性能监控**：添加任务性能分析和优化建议

---

本设计方案基于现有代码结构和业务需求，采用渐进式重构策略，确保在不影响现有功能的前提下，大幅提升代码质量和用户体验。