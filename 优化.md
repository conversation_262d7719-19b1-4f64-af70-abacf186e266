# think hard 【媒体模块重构实施方案】

## 项目已经根据新的方案重构了第1~3个阶段,但是目前还存在挺多问题需要解决

- MediaDemo.vue基于是新方案的一个测试页面，功能集成还没有完整
- Media.vue是老的页面，功能集成已经完整，但是需要迁移到新方案

## 注意事项
- 迁移过程中，总是会出现一些IPC通信的问题，例如:"An object could not be cloned."，要注意规避


对比MediaDemo.vue和Media.vue的差异，找出需要迁移的功能


## 最新存在的问题
- 在VideoConverter-new中执行任务，任务执行成功后存在以下问题：
    - console.log打印的日志会出现3次执行完成的日志，同时界面会出现3次通知
    - 任务执行完成后，没有出现执行结果的列表
- ProcessingStatsPanel.vue中数据一直是空的
