{"name": "xhs-pc-app", "private": true, "version": "0.4.1", "description": "KOL小红书工具箱应用", "author": {"name": "KolCM Team", "email": "<EMAIL>"}, "main": "dist-electron/main/index.js", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "build:mac": "npm run build && electron-builder --mac", "build:mac-universal": "npm run build && electron-builder --mac --universal", "build:win": "npm run build && electron-builder --win", "build:all": "npm run build && electron-builder --mac --win"}, "dependencies": {"@ffmpeg-installer/ffmpeg": "^1.1.0", "axios": "^1.8.4", "axios-cookiejar-support": "^6.0.1", "echarts": "^5.5.0", "electron-store": "^10.0.1", "electron-updater": "^6.6.2", "element-plus": "^2.9.8", "fluent-ffmpeg": "^2.1.3", "mitt": "^3.0.1", "moment": "^2.30.1", "node-schedule": "^1.2.2", "node-xlsx": "^0.24.0", "path-browserify": "^1.0.1", "pinia": "^3.0.2", "sharp": "^0.34.1", "tough-cookie": "^5.1.2", "uuid": "^11.1.0", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@iconify/vue": "^4.3.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "electron": "^36.2.1", "electron-builder": "^26.0.12", "electron-devtools-installer": "^4.0.0", "sass-embedded": "^1.87.0", "typescript": "~5.8.3", "vite": "^6.3.3", "vite-plugin-electron": "^0.29.0", "vite-plugin-electron-renderer": "^0.14.6", "vue-tsc": "^2.2.10"}}