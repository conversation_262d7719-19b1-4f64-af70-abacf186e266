export class OperationQueue {
  private queue: Array<() => Promise<any>> = []
  private running = false
  
  async enqueue<T>(operation: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.queue.push(async () => {
        try {
          const result = await operation()
          resolve(result)
        } catch (error) {
          reject(error)
        }
      })
      
      if (!this.running) {
        this.processQueue()
      }
    })
  }
  
  private async processQueue() {
    if (this.running || this.queue.length === 0) return
    
    this.running = true
    
    while (this.queue.length > 0) {
      const operation = this.queue.shift()!
      await operation()
    }
    
    this.running = false
  }
}