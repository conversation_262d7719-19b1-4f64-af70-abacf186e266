import mitt from 'mitt'

// 统计更新事件类型定义
export type StatsEvents = {
  'stats-update': void
  'task-completed': { taskId: string, taskType: string }
  'task-failed': { taskId: string, taskType: string }
  'task-started': { taskId: string, taskType: string }
  'batch-completed': { batchId: string, taskType: string }
}

// 创建事件总线实例
export const statsEventBus = mitt<StatsEvents>()

// 便捷方法
export const emitStatsUpdate = () => {
  console.log('[StatsEventBus] 触发统计更新事件')
  statsEventBus.emit('stats-update')
}

export const emitTaskCompleted = (taskId: string, taskType: string) => {
  console.log(`[StatsEventBus] 任务完成事件: ${taskId} (${taskType})`)
  statsEventBus.emit('task-completed', { taskId, taskType })
  statsEventBus.emit('stats-update')
}

export const emitTaskFailed = (taskId: string, taskType: string) => {
  console.log(`[StatsEventBus] 任务失败事件: ${taskId} (${taskType})`)
  statsEventBus.emit('task-failed', { taskId, taskType })
  statsEventBus.emit('stats-update')
}

export const emitTaskStarted = (taskId: string, taskType: string) => {
  console.log(`[StatsEventBus] 任务开始事件: ${taskId} (${taskType})`)
  statsEventBus.emit('task-started', { taskId, taskType })
}

export const emitBatchCompleted = (batchId: string, taskType: string) => {
  console.log(`[StatsEventBus] 批量任务完成事件: ${batchId} (${taskType})`)
  statsEventBus.emit('batch-completed', { batchId, taskType })
  statsEventBus.emit('stats-update')
}
