import { mediaEventBus } from './media-event-bus'

export interface MediaEventData {
  taskId: string
  progress?: number
  currentStep?: string
  status?: string
  error?: string
  result?: any
}

/**
 * 媒体事件管理器 - 单例模式
 * 统一管理 Electron 主进程和渲染进程之间的事件通信
 * 避免重复注册监听器和事件循环问题
 */
class MediaEventManager {
  private static instance: MediaEventManager
  private isInitialized = false
  private electronListeners: Map<string, Function> = new Map()
  
  private constructor() {}
  
  static getInstance(): MediaEventManager {
    if (!MediaEventManager.instance) {
      MediaEventManager.instance = new MediaEventManager()
    }
    return MediaEventManager.instance
  }
  
  /**
   * 初始化事件监听器（只会执行一次）
   */
  initialize(): void {
    if (this.isInitialized || !window.electronAPI) {
      return
    }
    
    console.log('[MediaEventManager] 初始化媒体事件管理器...')
    
    // 注册 Electron 事件监听器
    this.registerElectronListener('media:task:progress', (data: MediaEventData) => {
      if (data.progress !== undefined) {
        mediaEventBus.emit('task:progress', {
          taskId: data.taskId,
          progress: data.progress,
          currentStep: data.currentStep
        })
      }
    })
    
    this.registerElectronListener('media:task:status', (data: MediaEventData) => {
      if (data.status !== undefined) {
        mediaEventBus.emit('task:status', {
          taskId: data.taskId,
          status: data.status,
          error: data.error
        })
      }
    })
    
    this.registerElectronListener('media:task:completed', (data: MediaEventData) => {
      if (data.result !== undefined) {
        mediaEventBus.emit('task:completed', {
          taskId: data.taskId,
          result: data.result
        })
      }
    })
    
    this.isInitialized = true
    console.log('[MediaEventManager] 媒体事件管理器初始化完成')
  }
  
  /**
   * 注册 Electron 事件监听器
   */
  private registerElectronListener(eventName: string, handler: Function): void {
    if (this.electronListeners.has(eventName)) {
      console.warn(`[MediaEventManager] 事件 ${eventName} 已注册，跳过重复注册`)
      return
    }
    
    const wrappedHandler = (_: any, data: any) => {
      console.log(`[MediaEventManager] 收到 Electron 事件: ${eventName}`, data)
      handler(data)
    }
    
    window.electronAPI.on(eventName, wrappedHandler)
    this.electronListeners.set(eventName, wrappedHandler)
  }
  
  /**
   * 清理所有事件监听器
   */
  cleanup(): void {
    if (!this.isInitialized || !window.electronAPI) {
      return
    }
    
    console.log('[MediaEventManager] 清理事件监听器...')
    
    for (const [eventName] of this.electronListeners) {
      window.electronAPI.removeAllListeners(eventName)
    }
    
    this.electronListeners.clear()
    this.isInitialized = false
    
    console.log('[MediaEventManager] 事件监听器清理完成')
  }
  
  /**
   * 获取内部事件总线实例（用于组件间通信）
   */
  getEventBus() {
    return mediaEventBus
  }
  
  /**
   * 获取初始化状态
   */
  getInitializationStatus(): boolean {
    return this.isInitialized
  }
  
  /**
   * 获取已注册的监听器数量
   */
  getListenerCount(): number {
    return this.electronListeners.size
  }
}

// 导出单例实例
export const mediaEventManager = MediaEventManager.getInstance()

// 在应用卸载时清理
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    mediaEventManager.cleanup()
  })
}