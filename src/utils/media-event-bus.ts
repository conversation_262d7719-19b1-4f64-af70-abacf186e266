import mitt from 'mitt'

export type MediaEvents = {
  // 来自 Electron 主进程的事件
  'task:progress': { taskId: string; progress: number; currentStep?: string }
  'task:status': { taskId: string; status: string; error?: string }
  'task:completed': { taskId: string; result: any }
  
  // 内部事件（组件间通信）
  'task:progress-changed': { taskId: string; progress: number; currentStep?: string }
  'task:status-changed': { taskId: string; status: string; error?: string; progress?: number }
  'task:result-added': { taskId: string; result: any }
  
  // 系统事件
  'stats:update': void
  'settings:update': void
}

export const mediaEventBus = mitt<MediaEvents>()