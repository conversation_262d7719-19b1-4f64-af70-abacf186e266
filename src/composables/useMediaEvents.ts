import { onMounted } from 'vue'
import { mediaEventManager } from '@/utils/media-event-manager'

/**
 * 媒体事件管理组合函数 - 优化版
 * 使用单例事件管理器，避免重复注册监听器
 */
export function useMediaEvents() {
  onMounted(() => {
    // 确保事件管理器已初始化（单例模式，只会初始化一次）
    mediaEventManager.initialize()
  })
  
  // 返回事件总线，用于组件间通信
  return { 
    mediaEventBus: mediaEventManager.getEventBus(),
    // 提供调试信息
    getManagerStatus: () => ({
      isInitialized: mediaEventManager.getInitializationStatus(),
      listenerCount: mediaEventManager.getListenerCount()
    })
  }
}