/* 小红书工具箱全局样式 */

// 主色调
$primary-color: #ff2442; // 小红书品牌红色
$primary-light: #ff6978;
$primary-dark: #d61f3b;

// 辅助色
$success-color: #67c23a;
$warning-color: #e6a23c;
$danger-color: #f56c6c;
$info-color: #909399;

// 中性色
$text-primary: #303133;
$text-regular: #606266;
$text-secondary: #909399;
$text-placeholder: #c0c4cc;

// 兼容旧代码，将 $text-color-secondary 设置为 $text-secondary
$text-color-secondary: $text-secondary;

// 边框颜色
$border-base: #dcdfe6;
$border-light: #e4e7ed;
$border-lighter: #ebeef5;

// 背景色
$background-base: #f5f7fa;
$background-light: #fafafa;
$background-card: #ffffff;

// 阴影
$box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
$box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
$box-shadow-dark: 0 2px 8px rgba(0, 0, 0, 0.2);
$box-shadow-hover: 0 12px 20px rgba(0, 0, 0, 0.08);

// 兼容旧代码，将 $box-shadow 设置为 $box-shadow-base
$box-shadow: $box-shadow-base;

// 字体
$font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC",
  "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial,
  sans-serif;

// 字体大小
$font-size-small: 12px;
$font-size-base: 14px;
$font-size-large: 18px;

// 圆角
$border-radius-small: 4px;
$border-radius-base: 8px;
$border-radius-large: 12px;
$border-radius-card: 12px;
$border-radius-image: 8px;

// 兼容旧代码，将 $border-radius 设置为 $border-radius-base
$border-radius: $border-radius-base;

// 间距
$spacing-mini: 4px;
$spacing-small: 8px;
$spacing-base: 16px;
$spacing-large: 24px;
$spacing-xl: 32px;

// 动画
$transition-base: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
$transition-hover: all 0.4s ease;

// 全局基础样式
html, body {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

body {
  font-family: $font-family;
  color: $text-primary;
  background-color: $background-base;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: block;
}

// 卡片样式
.app-card {
  background-color: $background-card;
  border-radius: $border-radius-card;
  box-shadow: $box-shadow-light;
  padding: $spacing-base;
  margin-bottom: $spacing-base;
  transition: $transition-base;
  border: 1px solid rgba(0, 0, 0, 0.05);

  &:hover {
    box-shadow: $box-shadow-base;
  }
}

// 统一卡片风格
.el-card {
  border-radius: $border-radius-card !important;
  border: 1px solid rgba(0, 0, 0, 0.05) !important;
  overflow: hidden;
  transition: $transition-base;
  box-shadow: $box-shadow-light !important;

  &:hover {
    transform: translateY(-5px);
    box-shadow: $box-shadow-hover !important;
    border-color: rgba(0, 0, 0, 0) !important;
  }

  .el-card__body {
    padding: $spacing-base;
  }
}

// 页面容器
.page-container {
  padding: $spacing-base;
}

// 主要按钮重写
.el-button--primary {
  background-color: $primary-color;
  border-color: $primary-color;
  border-radius: $border-radius-base;

  &:hover,
  &:focus {
    background-color: $primary-light;
    border-color: $primary-light;
  }

  &:active {
    background-color: $primary-dark;
    border-color: $primary-dark;
  }
}

// 统一按钮圆角
.el-button {
  border-radius: $border-radius-base;
}

// 表格优化
.el-table {
  border-radius: $border-radius-base;
  overflow: hidden;

  th {
    background-color: $background-base !important;
  }

  td,
  th {
    padding: $spacing-small $spacing-base;
  }
}

// 输入框优化
.el-input__inner {
  border-radius: $border-radius-base;
}

// 统一表单元素圆角
.el-input, .el-select, .el-textarea, .el-input-number {
  .el-input__wrapper, .el-textarea__inner {
    border-radius: $border-radius-base !important;
  }
}

// 分割线
.section-divider {
  margin: $spacing-base 0;
  color: $primary-color;
  font-weight: 500;
}

// 数据统计卡片
.stat-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: $spacing-base;
  background-color: $background-card;
  border-radius: $border-radius-card;
  box-shadow: $box-shadow-light;
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: $transition-base;

  &:hover {
    transform: translateY(-5px);
    box-shadow: $box-shadow-hover;
    border-color: rgba(0, 0, 0, 0);
  }

  .stat-number {
    font-size: 24px;
    font-weight: 600;
    color: $primary-color;
    margin: $spacing-small 0;
  }

  .stat-label {
    color: $text-secondary;
    font-size: 14px;
  }
}

// 工具栏
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-base;

  .tools-left,
  .tools-right {
    display: flex;
    align-items: center;
    gap: $spacing-small;
  }
}

// 图片预览优化
.image-preview, .thumbnail {
  border-radius: $border-radius-image;
  overflow: hidden;
  transition: $transition-hover;

  &:hover {
    transform: scale(1.05);
  }
}

// 图片容器统一样式
.note-image, .image-container {
  position: relative;
  overflow: hidden;
  border-radius: $border-radius-image;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  margin-bottom: $spacing-small;
}

// 加载状态
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: $spacing-large;

  .el-icon-loading {
    font-size: 24px;
    color: $primary-color;
  }
}

// 页面标题
.page-title {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: $spacing-large;
  color: $text-primary;
  border-left: 4px solid $primary-color;
  padding-left: $spacing-base;
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-xl;
  color: $text-secondary;

  i {
    font-size: 48px;
    margin-bottom: $spacing-base;
    color: $border-light;
  }

  .empty-text {
    font-size: 14px;
  }
}

// 用户头像样式
.el-avatar {
  border-radius: 50% !important;
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

// 标签样式统一
.el-tag {
  border-radius: $border-radius-base;
}

// 卡片内标题样式
.note-title, .card-title {
  font-size: 16px;
  font-weight: 500;
  margin: 4px 0 $spacing-small 0;
  color: $text-primary;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  max-height: 2.8em;
  transition: color 0.2s ease;

  &:hover {
    color: $primary-color;
  }
}

// 统计信息样式
.note-stats, .stat-info {
  display: flex;
  justify-content: flex-start;
  gap: $spacing-base;
  margin-top: auto;
  font-size: 12px;
  color: $text-secondary;
  padding-top: $spacing-small;
  border-top: 1px dashed rgba(0, 0, 0, 0.05);

  .stat-item {
    display: flex;
    align-items: center;
    padding: 4px 0;
    margin-right: $spacing-small;

    .stat-icon {
      margin-right: 4px;
      font-size: 14px;
    }

    .like-icon {
      color: #ff4757;
    }

    .collect-icon {
      color: #ffa502;
    }

    .comment-icon {
      color: #1e90ff;
    }
  }
}

// 图片预览弹窗样式
.el-image-viewer__wrapper {
  position: fixed !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
  z-index: 2050 !important;
}

.el-image-viewer__mask {
  position: fixed !important;
  width: 100% !important;
  height: 100% !important;
  top: 0 !important;
  left: 0 !important;
  opacity: 0.5 !important;
  background: #000 !important;
}
