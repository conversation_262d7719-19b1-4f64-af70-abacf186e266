import { createPinia } from 'pinia'

// 创建 Pinia 实例
export const pinia = createPinia()

// 简化持久化方案 - 每个 store 自己管理持久化
console.log('[PiniaIndex] 使用简化的持久化方案')


// 导出 store 创建函数（延迟加载）
export { useMediaTasksStore } from './media-tasks'
export { useMediaSettingsStore } from './media-settings'
export { useMediaMainStore } from './media-main'
export { useMediaStatsStore } from './media-stats'

// 导出类型
export type { MediaSettings } from './media-settings'
export type { ProcessingOptions, SingleTask, TaskResult } from './media-tasks'
export type { ProcessingStats } from './media-stats'