import type { PiniaPlugin } from 'pinia'
import { toRaw } from 'vue'

export interface PersistenceOptions {
  enabled: boolean
  key: string
  paths?: string[]
  exclude?: string[]
  serializer?: {
    serialize: (state: any) => string
    deserialize: (value: string) => any
  }
  storage?: {
    getItem: (key: string) => Promise<any>
    setItem: (key: string, value: any) => Promise<void>
    removeItem: (key: string) => Promise<void>
  }
  debounce?: number
  version?: number
  migrate?: (oldState: any, oldVersion: number) => any
}

// Electron IPC 存储适配器
const electronStorage = {
  async getItem(key: string) {
    try {
      console.log(`[PersistencePlugin] 获取数据: ${key}`)
      const response = await window.electronAPI.storage.get(key, 'data')
      console.log(`[PersistencePlugin] 获取响应:`, response)
      return response.success ? response.data : null
    } catch (error) {
      console.error(`[PersistencePlugin] 获取数据失败 (${key}):`, error)
      return null
    }
  },
  
  async setItem(key: string, value: any) {
    try {
      console.log(`[PersistencePlugin] 保存数据: ${key}`, value)
      const response = await window.electronAPI.storage.set(key, 'data', value)
      console.log(`[PersistencePlugin] 保存响应:`, response)
    } catch (error) {
      console.error(`[PersistencePlugin] 保存数据失败 (${key}):`, error)
    }
  },
  
  async removeItem(key: string) {
    try {
      console.log(`[PersistencePlugin] 删除数据: ${key}`)
      await window.electronAPI.storage.delete(key, 'data')
    } catch (error) {
      console.error(`[PersistencePlugin] 删除数据失败 (${key}):`, error)
    }
  }
}

export function createPersistencePlugin(options: PersistenceOptions): PiniaPlugin {
  return ({ store }) => {
    
    if (!options.enabled) return
    
    const {
      key,
      paths = [],
      exclude = [],
      serializer = {
        serialize: (state: any) => {
          // 使用 toRaw 去除 Vue 响应式包装器，避免循环引用
          try {
            console.log(`[Persistence] 默认序列化器开始处理:`, typeof state)
            const rawState = toRaw(state)
            
            // 进一步处理 ref 对象
            const serializable = deepExtractValues(rawState)
            
            console.log(`[Persistence] 默认序列化器处理完成:`, serializable)
            return JSON.stringify(serializable)
          } catch (error) {
            console.error(`[Persistence] 默认序列化器失败:`, error)
            // 返回空对象作为fallback
            return JSON.stringify({})
          }
        },
        deserialize: JSON.parse
      },
      storage = electronStorage,
      debounce = 1000,
      version = 1,
      migrate
    } = options
    
    // 恢复持久化数据
    const restore = async () => {
      try {
        const persisted = await storage.getItem(key)
        if (!persisted) {
          console.log(`[Persistence] 没有找到持久化数据 (${key})`)
          return
        }
        
        console.log(`[Persistence] 恢复数据 (${key}):`, persisted)
        
        // 检查数据结构
        let state: any
        if (typeof persisted === 'object' && persisted !== null && 'data' in persisted) {
          // 如果是包装的数据结构 {version, data, timestamp}
          console.log(`[Persistence] 检测到包装数据结构`)
          state = serializer.deserialize(persisted.data)
          
          // 版本迁移
          if (persisted.version !== version && migrate) {
            console.log(`[Persistence] 执行版本迁移: ${persisted.version} -> ${version}`)
            state = migrate(state, persisted.version || 0)
          }
        } else {
          // 如果是直接的数据
          console.log(`[Persistence] 检测到直接数据结构`)
          state = typeof persisted === 'string' ? serializer.deserialize(persisted) : persisted
        }
        
        console.log(`[Persistence] 准备恢复状态:`, state)
        
        // 合并状态
        if (paths.length > 0) {
          console.log(`[Persistence] 使用路径恢复:`, paths)
          paths.forEach(path => {
            const value = getNestedValue(state, path)
            console.log(`[Persistence] 恢复路径 ${path}:`, value)
            setNestedValue(store.$state, path, value)
          })
        } else {
          console.log(`[Persistence] 使用 patch 恢复完整状态`)
          store.$patch(state)
        }
        
        console.log(`[Persistence] 状态恢复完成 (${key})`)
      } catch (error) {
        console.error(`[Persistence] 恢复状态失败 (${key}):`, error)
      }
    }
    
    // 保存状态（带防抖）
    let saveTimer: NodeJS.Timeout
    const save = () => {
      clearTimeout(saveTimer)
      saveTimer = setTimeout(async () => {
        try {
          console.log(`[Persistence] 开始保存状态 (${key})`)
          const state = toRaw(store.$state)
          let dataToSave = state
          
          // 筛选需要持久化的字段
          if (paths.length > 0) {
            console.log(`[Persistence] 使用路径过滤:`, paths)
            dataToSave = {}
            paths.forEach(path => {
              if (!exclude.includes(path)) {
                const value = getNestedValue(state, path)
                console.log(`[Persistence] 保存路径 ${path}:`, value)
                setNestedValue(dataToSave, path, value)
              }
            })
          } else if (exclude.length > 0) {
            console.log(`[Persistence] 使用排除列表:`, exclude)
            dataToSave = { ...state }
            exclude.forEach(path => {
              deleteNestedValue(dataToSave, path)
            })
          }
          
          console.log(`[Persistence] 准备序列化数据:`, dataToSave)
          const serialized = serializer.serialize(dataToSave)
          console.log(`[Persistence] 序列化完成，长度:`, serialized.length)
          
          const packagedData = {
            version,
            data: serialized,
            timestamp: Date.now()
          }
          
          console.log(`[Persistence] 保存包装数据:`, packagedData)
          await storage.setItem(key, packagedData)
          console.log(`[Persistence] 状态保存完成 (${key})`)
        } catch (error) {
          console.error(`[Persistence] 保存状态失败 (${key}):`, error)
        }
      }, debounce)
    }
    
    // 初始化持久化
    const initialize = async () => {
      console.log(`[Persistence] 初始化持久化插件 (${key})`)
      
      // 首先恢复数据
      await restore()
      
      // 然后设置状态变化监听器
      store.$subscribe((mutation) => {
        console.log(`[Persistence] 状态变化检测 (${key}):`, mutation.type)
        save()
      })
      
      console.log(`[Persistence] 持久化插件初始化完成 (${key})`)
    }
    
    // 异步初始化
    initialize().catch(error => {
      console.error(`[Persistence] 初始化失败 (${key}):`, error)
    })
    
    // 提供手动方法
    ;(store as any).$persist = {
      save,
      restore,
      clear: async () => {
        console.log(`[Persistence] 清理持久化数据 (${key})`)
        await storage.removeItem(key)
      }
    }
  }
}

// 工具函数
function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((acc, key) => acc?.[key], obj)
}

function setNestedValue(obj: any, path: string, value: any): void {
  const keys = path.split('.')
  const lastKey = keys.pop()!
  const target = keys.reduce((acc, key) => {
    if (!acc[key]) acc[key] = {}
    return acc[key]
  }, obj)
  target[lastKey] = value
}

function deleteNestedValue(obj: any, path: string): void {
  const keys = path.split('.')
  const lastKey = keys.pop()!
  const target = keys.reduce((acc, key) => acc?.[key], obj)
  if (target) delete target[lastKey]
}

// 深度提取响应式对象的值
function deepExtractValues(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj
  }
  
  // 处理基本类型
  if (typeof obj !== 'object') {
    return obj
  }
  
  // 处理数组
  if (Array.isArray(obj)) {
    return obj.map(item => deepExtractValues(item))
  }
  
  // 处理 Map 对象
  if (obj instanceof Map) {
    return Object.fromEntries(
      Array.from(obj.entries()).map(([key, value]) => [key, deepExtractValues(value)])
    )
  }
  
  // 处理 Set 对象
  if (obj instanceof Set) {
    return Array.from(obj).map(item => deepExtractValues(item))
  }
  
  // 处理 ref 对象（检查是否有 value 属性）
  if (obj.value !== undefined && typeof obj.value !== 'function') {
    return deepExtractValues(obj.value)
  }
  
  // 处理普通对象
  const result: any = {}
  for (const [key, value] of Object.entries(obj)) {
    // 跳过函数和特殊属性
    if (typeof value === 'function' || key.startsWith('_') || key === 'dep' || key === 'effect') {
      continue
    }
    
    result[key] = deepExtractValues(value)
  }
  
  return result
}