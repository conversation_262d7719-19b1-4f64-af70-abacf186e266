import { defineStore } from 'pinia'
import { reactive, computed, toRefs } from 'vue'
import { useMediaEvents } from '@/composables/useMediaEvents'
import path from 'path-browserify'

// 任务相关类型定义
export interface BaseTask {
  id: string
  type: 'video-convert' | 'audio-extract' | 'asr' | 'image-process'
  fileName: string
  filePath: string
  outputPath: string
  status: 'pending' | 'processing' | 'completed' | 'error' | 'paused'
  progress: number
  error?: string
  createdAt: number
  startTime?: number
  completedAt?: number
  estimatedTime?: number
  currentStep?: string
  fileSize?: number
  fileUid?: string
}

export interface SingleTask extends BaseTask {
  mode: 'single'
  options: ProcessingOptions
  result?: TaskResult
}



export type MediaTask = SingleTask

export interface ProcessingOptions {
  outputFormat?: string
  quality?: string
  resizeEnabled?: boolean
  maxWidth?: number
  format?: string
  audioQuality?: string
  language?: string
  outputFormats?: string[]
  enableTimestamps?: boolean
  filterSilence?: boolean
  width?: number
  height?: number
  maintainAspectRatio?: boolean
  optimize?: boolean
  progressive?: boolean
  stripMetadata?: boolean
  colorSpace?: string
  saturation?: number
  [key: string]: any
}

export interface TaskResult {
  id: string
  taskId: string
  taskType: string
  fileName: string
  type: string
  success: boolean
  outputPath?: string
  completedAt: number
  size?: number
  duration?: number
  processingTime?: number
  error?: string
  data?: any
}

export interface ProcessingRecord {
  id: string
  taskId: string
  taskType: string
  fileName: string
  startTime: number
  endTime: number
  success: boolean
  error?: string
}

interface TaskUpdate {
  taskId: string
  changes: Partial<SingleTask>
}

export const useMediaTasksStore = defineStore('media-tasks', () => {
  // 状态定义 - 明确区分持久化和非持久化
  const state = reactive({
    // 持久化数据
    persistent: {
      singleTasks: new Map<string, SingleTask>(),
      taskResults: new Map<string, TaskResult>(),
      processingHistory: [] as ProcessingRecord[],
      // 组件级持久化状态
      pendingFiles: new Map<string, any[]>(),
      taskOptions: new Map<string, ProcessingOptions>()
    },

    // 会话数据（不持久化）
    session: {
      activeTasksCount: 0,
      processingQueues: new Map<string, Set<string>>(),
      taskProgress: new Map<string, number>(),
      currentSteps: new Map<string, string>()
    }
  })

  // 计算属性
  const allTasks = computed(() => {
    return Array.from(state.persistent.singleTasks.values())
  })

  const activeTasks = computed(() => {
    return allTasks.value.filter(task =>
      ['pending', 'processing'].includes(task.status)
    )
  })

  const completedTasks = computed(() => {
    return allTasks.value.filter(task => task.status === 'completed')
  })

  const errorTasks = computed(() => {
    return allTasks.value.filter(task => task.status === 'error')
  })

  // 使用事件总线替代直接状态修改
  const { mediaEventBus } = useMediaEvents()

  // 状态更新方法，支持事件来源标识
  const updateTaskStatus = (
    taskId: string,
    status: 'pending' | 'processing' | 'completed' | 'error' | 'paused',
    progress?: number,
    error?: string,
    source: 'internal' | 'electron' = 'internal'
  ) => {
    const task = state.persistent.singleTasks.get(taskId)
    if (!task) {
      console.warn(`[MediaTasksStore] 尝试更新不存在的任务: ${taskId}`)
      return
    }

    console.log(`[MediaTasksStore] 更新任务状态: ${taskId}, ${status}, 来源: ${source}`)

    // 更新状态
    task.status = status
    if (progress !== undefined) task.progress = progress
    if (error) task.error = error

    // 更新时间戳
    if (status === 'processing' && !task.startTime) {
      task.startTime = Date.now()
    }
    if (status === 'completed' || status === 'error') {
      task.completedAt = Date.now()
    }

    // 内部更新不发送事件，避免循环；来自 Electron 的更新也不发送，因为已经是响应
    // 只有在需要通知其他组件时才发送内部事件
    if (source === 'internal') {
      // 发送内部事件通知其他组件状态变化
      mediaEventBus.emit('task:status-changed', { taskId, status, error, progress })
    }
  }


  // 批量操作优化
  const batchUpdateTasks = async (updates: TaskUpdate[]) => {
    // 使用事务方式批量更新
    const updatedTasks: SingleTask[] = []

    for (const update of updates) {
      const task = state.persistent.singleTasks.get(update.taskId)
      if (task) {
        Object.assign(task, update.changes)
        updatedTasks.push(task)
      }
    }

    // 发送状态更新事件
    for (const task of updatedTasks) {
      mediaEventBus.emit('task:status', { taskId: task.id, status: task.status })
    }
  }

  // 组件状态管理
  const setPendingFiles = (taskType: string, files: any[]) => {
    state.persistent.pendingFiles.set(taskType, files)
  }

  const getPendingFiles = (taskType: string) => {
    return state.persistent.pendingFiles.get(taskType) || []
  }

  const setTaskOptions = (taskType: string, options: ProcessingOptions) => {
    state.persistent.taskOptions.set(taskType, options)
  }

  const getTaskOptions = (taskType: string): ProcessingOptions => {
    return state.persistent.taskOptions.get(taskType) || {}
  }

  // 任务创建和执行
  const createTasksFromFiles = async (
    taskType: string,
    files: any[],
    options: ProcessingOptions,
    outputDirectory: string
  ): Promise<string[]> => {
    const taskIds: string[] = []

    for (const file of files) {
      const taskId = generateTaskId()
      const filePath = window.electronAPI.media.getPathForFile(file.raw)
      const task: SingleTask = {
        id: taskId,
        mode: 'single',
        type: taskType as any,
        fileName: file.name,
        filePath: filePath,
        outputPath: path.join(outputDirectory, generateOutputFileName(file.name, options)),
        options,
        status: 'pending',
        progress: 0,
        createdAt: Date.now(),
        fileSize: file.size,
        fileUid: file.uid
      }

      state.persistent.singleTasks.set(taskId, task)
      taskIds.push(taskId)

      console.log(`[MediaTasksStore] 创建任务: ${taskId}, 类型: ${taskType}, 文件: ${file.name}`)
    }



    return taskIds
  }

  // 创建单个任务（兼容老的API）
  const createSingleTask = async (taskData: {
    type: string,
    fileName: string,
    filePath: string,
    outputPath: string,
    options: ProcessingOptions,
    fileSize?: number,
    fileUid?: string
  }): Promise<string> => {
    const taskId = generateTaskId()
    const task: SingleTask = {
      id: taskId,
      mode: 'single',
      type: taskData.type as any,
      fileName: taskData.fileName,
      filePath: taskData.filePath,
      outputPath: taskData.outputPath,
      options: taskData.options,
      status: 'pending',
      progress: 0,
      createdAt: Date.now(),
      fileSize: taskData.fileSize,
      fileUid: taskData.fileUid
    }

    state.persistent.singleTasks.set(taskId, task)
    console.log(`[MediaTasksStore] 创建单个任务: ${taskId}, 类型: ${taskData.type}, 文件: ${taskData.fileName}`)



    return taskId
  }


  const removeSingleTask = async (taskId: string) => {
    const task = state.persistent.singleTasks.get(taskId)
    if (!task) return

    // 如果任务正在进行，先取消
    if (task.status === 'processing') {
      await window.electronAPI.media.cancelTask(taskId)
    }

    state.persistent.singleTasks.delete(taskId)

    // 清理相关结果
    state.persistent.taskResults.delete(taskId)


  }


  const clearAllTasks = async () => {
    // 取消所有进行中的任务
    for (const task of allTasks.value) {
      if (task.status === 'processing') {
        await window.electronAPI.media.cancelTask(task.id)
      }
    }

    state.persistent.singleTasks.clear()
    state.persistent.taskResults.clear()
    state.persistent.processingHistory = []


  }

  // 任务结果管理
  const addTaskResult = async (result: TaskResult) => {
    state.persistent.taskResults.set(result.id, result)

    // 添加到处理历史
    state.persistent.processingHistory.push({
      id: generateTaskId(),
      taskId: result.taskId,
      taskType: result.taskType,
      fileName: result.fileName,
      startTime: result.completedAt - (result.processingTime || 0),
      endTime: result.completedAt,
      success: result.success,
      error: result.error
    })

    // 限制历史记录数量
    if (state.persistent.processingHistory.length > 1000) {
      state.persistent.processingHistory = state.persistent.processingHistory.slice(-1000)
    }


  }

  const getTaskResult = (taskId: string): TaskResult | undefined => {
    return state.persistent.taskResults.get(taskId)
  }







  // 工具方法
  const generateTaskId = () => `task_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`

  const generateOutputFileName = (originalName: string, options: ProcessingOptions) => {
    const baseName = path.basename(originalName, path.extname(originalName))
    const ext = options.outputFormat || path.extname(originalName).substring(1)
    return `${baseName}_processed.${ext}`
  }

  // 监听来自 Electron 主进程的事件更新任务状态
  mediaEventBus.on('task:progress', ({ taskId, progress, currentStep }) => {
    const task = state.persistent.singleTasks.get(taskId)
    if (task) {
      console.log(`[MediaTasksStore] 更新任务进度: ${taskId}, ${progress}%`)
      task.progress = progress
      if (currentStep) task.currentStep = currentStep

      // 发送内部进度更新事件
      mediaEventBus.emit('task:progress-changed', { taskId, progress, currentStep })
    }
  })

  mediaEventBus.on('task:status', ({ taskId, status, error }) => {
    // 来自 Electron 主进程的状态更新
    updateTaskStatus(taskId, status as any, undefined, error, 'electron')
  })

  mediaEventBus.on('task:completed', ({ taskId, result }) => {
    const task = state.persistent.singleTasks.get(taskId)
    if (task) {
      console.log(`[MediaTasksStore] 任务完成: ${taskId}`)

      // 更新任务状态为完成
      updateTaskStatus(taskId, 'completed', 100, undefined, 'electron')

      // 添加任务结果
      const taskResult: TaskResult = {
        id: generateTaskId(),
        taskId,
        taskType: task.type,
        fileName: task.fileName,
        type: task.type,
        success: true,
        outputPath: result.outputPath,
        completedAt: Date.now(),
        processingTime: result.processingTime,
        data: result,
      }

      // 异步添加任务结果
      addTaskResult(taskResult).catch(error => {
        console.error('[MediaTasksStore] 添加任务结果失败:', error)
      })

      // 发送任务完成的内部事件
      mediaEventBus.emit('task:result-added', { taskId, result: taskResult })
    }
  })



  return {
    // 持久化状态
    ...toRefs(state.persistent),
    // 会话状态
    ...toRefs(state.session),
    // 计算属性
    allTasks,
    activeTasks,
    completedTasks,
    errorTasks,
    // 方法
    updateTaskStatus,
    batchUpdateTasks,
    setPendingFiles,
    getPendingFiles,
    setTaskOptions,
    getTaskOptions,
    createTasksFromFiles,
    createSingleTask,
    removeSingleTask,
    clearAllTasks,
    addTaskResult,
    getTaskResult,

    generateTaskId,
    generateOutputFileName
  }
})