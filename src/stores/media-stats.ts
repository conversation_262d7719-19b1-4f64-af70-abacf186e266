import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface ProcessingStats {
  totalProcessed: number
  totalSize: number
  totalTime: number
  successRate: number
  averageProcessingTime: number
  tasksByType: Record<string, number>
  tasksByStatus: Record<string, number>
  lastUpdated?: number
}

/**
 * 媒体统计管理 Store
 * 负责处理统计数据的管理和持久化
 */
export const useMediaStatsStore = defineStore('media-stats', () => {
  // 统计状态
  const stats = ref<ProcessingStats>({
    totalProcessed: 0,
    totalSize: 0,
    totalTime: 0,
    successRate: 0,
    averageProcessingTime: 0,
    tasksByType: {},
    tasksByStatus: {}
  })

  const isLoaded = ref(false)
  const isManuallyCleared = ref(false) // 标记是否被用户手动清空

  // 刷新统计信息（基于当前任务数据计算）
  const refreshStats = async (allTasks: any[], allResults: any[]): Promise<void> => {
    try {
      // 检查输入参数是否有效
      if (!Array.isArray(allTasks)) {
        console.warn('[StatsStore] 刷新统计失败: allTasks 不是有效数组')
        return
      }

      if (!Array.isArray(allResults)) {
        console.warn('[StatsStore] 刷新统计失败: allResults 不是有效数组')
        return
      }

      // 如果用户手动清空了统计数据，不要重新计算
      if (isManuallyCleared.value) {
        console.log('[StatsStore] 统计数据已被用户手动清空，跳过自动刷新')
        return
      }

      // 只有当任务和结果都为空时才保留现有统计数据
      // 注意：如果有任务但没有结果，或者有结果但没有任务，仍应更新统计
      if (allTasks.length === 0 && allResults.length === 0) {
        console.log('[StatsStore] 任务和结果均为空，保留现有统计数据')
        return
      }

      console.log(`[StatsStore] 开始刷新统计: ${allTasks.length} 个任务, ${allResults.length} 个结果`)

      // 如果有新的任务或结果数据，重置手动清空标记
      if (allTasks.length > 0 || allResults.length > 0) {
        isManuallyCleared.value = false
      }

      // 保存当前的历史统计数据
      const currentStats = { ...stats.value }
      console.log(`[StatsStore] 当前历史统计: 已处理 ${currentStats.totalProcessed} 个任务`)

      // 基于结果数据计算统计（结果数据包含历史数据）
      const successfulResults = allResults.filter(r => r.success)
      const currentProcessed = successfulResults.length

      const currentSize = allResults
        .filter(r => r.success && r.size)
        .reduce((sum, r) => sum + (r.size || 0), 0)

      const resultsWithDuration = allResults.filter(r =>
        r.success && r.processingTime
      )

      const currentTime = resultsWithDuration.reduce((sum, r) =>
        sum + (r.processingTime || 0), 0
      )

      console.log(`[StatsStore] 处理时间统计: 有效结果数 ${resultsWithDuration.length}, 总时间 ${currentTime}ms (${Math.floor(currentTime / 1000)}秒)`)
      if (resultsWithDuration.length > 0) {
        console.log(`[StatsStore] 处理时间详情:`, resultsWithDuration.map(r => ({
          fileName: r.fileName,
          processingTime: r.processingTime,
          processingTimeSeconds: Math.floor((r.processingTime || 0) / 1000)
        })))
      }

      // 基于结果数据统计类型和状态（只统计结果，不重复计算）
      const resultsByType: Record<string, number> = {}
      const resultsByStatus: Record<string, number> = {}

      allResults.forEach(result => {
        // 统计类型
        if (result.type) {
          resultsByType[result.type] = (resultsByType[result.type] || 0) + 1
        }

        // 统计状态
        const status = result.success ? 'completed' : 'error'
        resultsByStatus[status] = (resultsByStatus[status] || 0) + 1
      })

      // 不再补充任务统计，因为：
      // 1. 已完成的任务都会有对应的结果
      // 2. 未完成的任务不应该计入统计（还没完成）
      // 3. 避免重复计算导致统计错误

      // 简化统计逻辑：直接基于结果数据计算，因为结果数据包含完整的历史信息
      const totalProcessed = currentProcessed
      const totalSize = currentSize
      const totalTimeMs = currentTime
      const tasksByType = resultsByType
      const tasksByStatus = resultsByStatus

      // 成功率计算
      const totalTasks = Object.values(tasksByStatus).reduce((sum, count) => sum + count, 0)
      const successRate = totalTasks > 0 ? ((tasksByStatus['completed'] || 0) / totalTasks) * 100 : 0

      // 平均处理时间计算
      const averageProcessingTime = (tasksByStatus['completed'] || 0) > 0 ?
        totalTimeMs / (tasksByStatus['completed'] || 1) : 0

      console.log(`[StatsStore] 统计计算结果: 总处理 ${totalProcessed} (历史: ${currentStats.totalProcessed}, 当前: ${currentProcessed})`)

      // 更新统计信息
      stats.value = {
        totalProcessed,
        totalSize,
        totalTime: Math.floor(totalTimeMs / 1000), // 转换为秒
        successRate: Math.round(successRate * 100) / 100,
        averageProcessingTime: Math.floor(averageProcessingTime / 1000), // 转换为秒
        tasksByType,
        tasksByStatus,
        lastUpdated: Date.now()
      }



      console.log('[StatsStore] 统计信息已刷新:', JSON.stringify(stats.value))
    } catch (error) {
      console.error('[StatsStore] 刷新统计失败:', error)
    }
  }


  // 更新统计数据
  const updateStats = async (newStats: Partial<ProcessingStats>): Promise<void> => {
    Object.assign(stats.value, newStats, { lastUpdated: Date.now() })

  }

  // 重置统计数据
  const resetStats = async (): Promise<void> => {
    stats.value = {
      totalProcessed: 0,
      totalSize: 0,
      totalTime: 0,
      successRate: 0,
      averageProcessingTime: 0,
      tasksByType: {},
      tasksByStatus: {},
      lastUpdated: Date.now()
    }
    isManuallyCleared.value = true // 标记为手动清空

    console.log('[StatsStore] 统计数据已重置')
  }

  // 清空统计数据
  const clearStats = async (): Promise<boolean> => {
    try {
      // 重置本地状态
      stats.value = {
        totalProcessed: 0,
        totalSize: 0,
        totalTime: 0,
        successRate: 0,
        averageProcessingTime: 0,
        tasksByType: {},
        tasksByStatus: {},
        lastUpdated: Date.now()
      }
      isManuallyCleared.value = true // 标记为手动清空



      console.log('[StatsStore] 统计数据已清空')
      return true
    } catch (error) {
      console.error('[StatsStore] 清空统计数据出错:', error)
      return false
    }
  }

  // 格式化统计数据
  const getFormattedStats = () => {
    return {
      totalProcessed: stats.value.totalProcessed,
      totalSize: formatFileSize(stats.value.totalSize),
      totalTime: formatDuration(stats.value.totalTime),
      successRate: `${stats.value.successRate.toFixed(1)}%`,
      averageProcessingTime: formatDuration(stats.value.averageProcessingTime),
      tasksByType: stats.value.tasksByType,
      tasksByStatus: stats.value.tasksByStatus,
      lastUpdated: stats.value.lastUpdated ? new Date(stats.value.lastUpdated).toLocaleString() : '未知'
    }
  }

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 格式化时长
  const formatDuration = (seconds: number): string => {
    if (seconds < 60) return `${seconds}秒`
    if (seconds < 3600) return `${Math.floor(seconds / 60)}分钟`
    return `${Math.floor(seconds / 3600)}小时`
  }



  return {
    // 状态
    stats,
    isLoaded,
    isManuallyCleared,

    refreshStats,
    updateStats,
    resetStats,
    getFormattedStats,
    formatFileSize,
    formatDuration,
    clearStats
  }
})