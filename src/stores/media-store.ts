import { defineStore } from 'pinia'
import { useMediaMainStore } from './media-main'

// 重新导出类型定义（保持向后兼容）
export type {
  BaseTask,
  SingleTask,
  MediaTask,
  ProcessingOptions,
  TaskResult
} from './media-tasks'

export type { ProcessingStats } from './media-stats'
export type { MediaSettings } from './media-settings'

/**
 * 媒体 Store（统一入口）
 * 委托给新的模块化架构，保持向后兼容
 */
export const useMediaStore = defineStore('media', () => {
  // 使用主 store
  const mainStore = useMediaMainStore()

  // 初始化主 store
  const initialize = async () => {
    await mainStore.initialize()
  }

  // 委托所有方法和属性给主 store
  return {
    // 状态
    isInitialized: mainStore.isInitialized,
    isProcessing: mainStore.isProcessing,
    ffmpegStatus: mainStore.ffmpegStatus,
    
    // 子 store 引用
    tasksStore: mainStore.tasksStore,
    settingsStore: mainStore.settingsStore,
    
    // 计算属性
    singleTasks: mainStore.tasksStore.singleTasks,
    batchTasks: mainStore.tasksStore.batchTasks,
    taskResults: mainStore.tasksStore.taskResults,
    allTasks: mainStore.tasksStore.allTasks,
    activeTasks: mainStore.tasksStore.activeTasks,
    completedTasks: mainStore.tasksStore.completedTasks,
    failedTasks: mainStore.tasksStore.errorTasks,
    settings: mainStore.settingsStore.settings,

    // 方法
    initialize,
    
    // 任务管理
    createSingleTask: mainStore.tasksStore.createSingleTask,
    startSingleTask: mainStore.startSingleTask,
    pauseSingleTask: mainStore.pauseSingleTask,
    retrySingleTask: mainStore.resumeSingleTask,
    removeSingleTask: mainStore.tasksStore.removeSingleTask,

    // 全局操作
    pauseAllTasks: mainStore.pauseAllTasks,
    clearAllTasks: mainStore.tasksStore.clearAllTasks,
    refreshStats: mainStore.refreshStats,
    clearResults: mainStore.cleanupCompletedTasks,
    cleanupCompletedTasks: mainStore.cleanupCompletedTasks,

    // 工具方法
    generateTaskId: mainStore.tasksStore.generateTaskId,
    generateOutputFileName: mainStore.tasksStore.generateOutputFileName
  }
})
