<template>
    <div v-if="show" class="progress-container">
        <div class="progress-bar">
            <div class="progress" :style="{ width: progress + '%' }"></div>
        </div>
        <div class="progress-info">
            <p class="info-text">新版本下载中 {{ props.progress.toFixed(2) }}% ({{ formattedReceivedBytes }} / {{ formattedTotalBytes }})</p>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'; // 移除defineProps导入，它是编译宏

// Define props received from parent component (App.vue)
const props = defineProps<{
    show: boolean;
    progress: number;
    receivedBytes: number;
    totalBytes: number;
}>();

// Helper function to convert bytes to human-readable size
const bytesToSize = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    const size = bytes / (1024 ** i);
    return size.toFixed(2) + ' ' + sizes[i];
};

// Computed properties to format bytes for display
const formattedReceivedBytes = computed(() => bytesToSize(props.receivedBytes));
const formattedTotalBytes = computed(() => bytesToSize(props.totalBytes));

// No need for onMounted or onBeforeUnmount as IPC listeners are in App.vue
</script>

<style lang="scss">
.progress-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 9999;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background-color: #efeaea;
}

.progress {
    height: 100%;
    background-color: #ff2442;
    transition: width 0.3s ease;
}

.progress-info {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px 15px;
    border-radius: 4px;
    font-size: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.info-text {
    margin: 0;
}
</style>