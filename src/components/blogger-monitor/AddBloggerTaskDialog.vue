<template>
    <el-dialog title="批量添加监控任务" v-model="dialogVisible" width="50%" @close="resetForm">
        <el-form :model="taskForm" ref="formRef">
            <el-form-item label="博主ID列表" prop="bloggerIds">
                <el-input v-model="taskForm.bloggerIds" type="textarea" :rows="6"
                    placeholder="请输入博主ID，多个博主ID用换行分隔"></el-input>
            </el-form-item>
            <el-form-item label="监控频率">
                <el-select v-model="taskForm.frequency" placeholder="请选择监控频率">
                    <el-option label="每小时" value="hourly"></el-option>
                    <el-option label="每天" value="daily"></el-option>
                    <el-option label="每周" value="weekly"></el-option>
                    <el-option label="每月" value="monthly"></el-option>
                </el-select>
            </el-form-item>
        </el-form>

        <!-- 进度条组件 -->
        <div class="progress-container" v-if="isProcessing">
            <el-progress :percentage="processingProgress" :format="progressFormat"
                :status="processingStatus"></el-progress>
            <div class="progress-info">
                {{ processingMessage }}
            </div>
        </div>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="closeDialog">取消</el-button>
                <el-button type="primary" @click="addTasks" :loading="isProcessing">确认添加</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineEmits, defineProps, watchEffect } from 'vue';
import { ElMessage } from 'element-plus';
import { v4 as uuidv4 } from 'uuid';

// 接收父组件传递的属性
const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false
    }
});

// 定义组件暴露的事件
const emit = defineEmits(['update:modelValue', 'task-added']);

// 计算属性，处理v-model
const dialogVisible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
});

// 表单引用
const formRef = ref();

// 表单数据
const taskForm = reactive({
    bloggerIds: '',
    frequency: 'daily'
});

// 进度处理相关状态
const isProcessing = ref(false);
const processedCount = ref(0);
const totalCount = ref(0);
const successCount = ref(0);
const failCount = ref(0);
const processingMessage = ref('');
const processingStatus = ref('');

// 处理进度百分比
const processingProgress = computed(() => {
    if (totalCount.value === 0) return 0;
    return Math.floor((processedCount.value / totalCount.value) * 100);
});

// 格式化进度显示文字
const progressFormat = (percentage: number) => {
    return `${processedCount.value}/${totalCount.value} (${percentage}%)`;
};

// 获取ipcRenderer
const ipcRenderer = (window as any).ipcRenderer;

/**
 * 重置表单数据
 */
const resetForm = () => {
    taskForm.bloggerIds = '';
    taskForm.frequency = 'daily';
    isProcessing.value = false;
    processedCount.value = 0;
    totalCount.value = 0;
    successCount.value = 0;
    failCount.value = 0;
    processingMessage.value = '';
    processingStatus.value = '';
};

/**
 * 关闭对话框
 */
const closeDialog = () => {
    if (isProcessing.value) {
        ElMessage.warning('正在处理中，请等待处理完成');
        return;
    }
    dialogVisible.value = false;
};

/**
 * 批量添加任务
 */
const addTasks = async () => {
    // 检查输入
    if (!taskForm.bloggerIds.trim()) {
        ElMessage.warning('请输入至少一个博主ID');
        return;
    }

    // 解析博主ID列表
    const bloggerIds = taskForm.bloggerIds
        .split('\n')
        .map(id => {
            // 尝试从URL中提取博主ID
            id = id.trim();
            if (!id) return '';

            try {
                // 处理完整URL的情况
                if (id.includes('xiaohongshu.com')) {
                    const url = new URL(id);
                    const pathParts = url.pathname.split('/');
                    // 获取路径最后一部分作为用户ID
                    return pathParts[pathParts.length - 1];
                }
                // 处理分享链接的情况 (例如: https://xhslink.com/xxxx)
                else if (id.includes('xhslink.com')) {
                    // 对于分享链接，需要先获取重定向后的URL
                    // 这里直接返回原始ID，让后端处理重定向
                    return id;
                }
                // 如果不是URL，则假定为直接的博主ID
                return id;
            } catch (error) {
                console.warn('解析博主ID失败:', id, error);
                return id; // 如果解析失败，返回原始输入
            }
        })
        .filter(id => id);

    if (bloggerIds.length === 0) {
        ElMessage.warning('请输入有效的博主ID');
        return;
    }

    // 初始化进度状态
    isProcessing.value = true;
    processedCount.value = 0;
    totalCount.value = bloggerIds.length;
    successCount.value = 0;
    failCount.value = 0;
    processingMessage.value = '开始批量添加监控任务...';
    processingStatus.value = 'active';

    try {
        // 逐个处理博主ID
        for (const bloggerId of bloggerIds) {
            processingMessage.value = `正在处理: ${bloggerId}`;

            try {
                // 获取博主信息
                const bloggerInfo = await ipcRenderer.invoke('xhs-user-info', bloggerId);

                if (bloggerInfo) {
                    // 添加监控任务
                    const result = await ipcRenderer.invoke('monitor:add-task', {
                        id: uuidv4(),
                        type: 'blogger',
                        bloggerId: bloggerId,
                        name: bloggerInfo.nickname || bloggerId,
                        frequency: taskForm.frequency,
                        data: {
                            // 博主用户信息
                            nickname: bloggerInfo.nickname || bloggerId,
                            avatar: bloggerInfo.avatar || '',
                            desc: bloggerInfo.desc || '',
                            gender: bloggerInfo.gender,
                            fans: bloggerInfo.fans || 0,
                            follows: bloggerInfo.follows || 0,
                            interaction: bloggerInfo.interaction || 0,
                            notes: bloggerInfo.notes || 0
                        }
                    });

                    if (result && result.success) {
                        successCount.value++;
                    } else {
                        failCount.value++;
                    }
                } else {
                    failCount.value++;
                }
            } catch (error: any) {
                console.error('处理博主ID失败:', bloggerId, error);
                failCount.value++;
            }

            // 更新进度
            processedCount.value++;
        }

        // 更新最终状态
        if (failCount.value === 0) {
            processingStatus.value = 'success';
            processingMessage.value = `全部成功! 已添加 ${successCount.value} 个博主监控任务`;
        } else if (successCount.value === 0) {
            processingStatus.value = 'exception';
            processingMessage.value = `全部失败! 未能添加博主监控任务`;
        } else {
            processingStatus.value = 'warning';
            processingMessage.value = `部分成功! 成功: ${successCount.value}, 失败: ${failCount.value}`;
        }

        // 通知父组件任务添加完成
        if (successCount.value > 0) {
            emit('task-added');
        }

        // 延时关闭处理状态
        setTimeout(() => {
            if (failCount.value === 0) {
                dialogVisible.value = false;
                resetForm();
            } else {
                isProcessing.value = false; // 只隐藏进度条，让用户可以修改失败的ID再次尝试
            }
        }, 2000);

    } catch (error: any) {
        console.error('批量添加任务失败:', error);
        processingStatus.value = 'exception';
        processingMessage.value = '处理失败: ' + (error.message || '未知错误');
        isProcessing.value = false;
    }
};
</script>

<style scoped lang="scss">
@use '../../assets/styles/global.scss' as *;

.progress-container {
    margin: 20px 0;
    padding: 15px;
    border-radius: 8px;
    background-color: #f9f9f9;

    .progress-info {
        margin-top: 10px;
        font-size: 14px;
        color: $text-color-secondary;
        text-align: center;
    }
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}
</style>