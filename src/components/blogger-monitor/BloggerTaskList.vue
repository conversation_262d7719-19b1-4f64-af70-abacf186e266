<template>
    <el-card class="blogger-task-list-card">
        <template #header>
            <div class="card-header">
                <span>博主监控任务列表</span>
            </div>
        </template>

        <el-table :data="tasks" style="width: 100%" v-loading="loading" @sort-change="handleSortChange">
            <!-- 博主头像 -->
            <el-table-column label="博主头像" width="80">
                <template #default="scope">
                    <el-avatar :size="50" :src="scope.row.data?.avatar || defaultAvatar"></el-avatar>
                </template>
            </el-table-column>

            <!-- 博主名称 -->
            <el-table-column label="博主名称" min-width="120" sortable="custom">
                <template #default="scope">
                    {{ scope.row.data?.nickname || scope.row.name || scope.row.bloggerId }}
                </template>
            </el-table-column>

            <!-- 博主ID -->
            <el-table-column prop="bloggerId" label="博主ID" min-width="120" sortable="custom"></el-table-column>

            <!-- 粉丝数 -->
            <el-table-column label="粉丝数" min-width="100" sortable="custom">
                <template #default="scope">
                    {{ formatNumber(scope.row.data?.fans) }}
                </template>
            </el-table-column>

            <!-- 关注数 -->
            <el-table-column label="关注数" min-width="100" sortable="custom">
                <template #default="scope">
                    {{ formatNumber(scope.row.data?.follows) }}
                </template>
            </el-table-column>

            <!-- 获赞数 -->
            <el-table-column label="获赞数" min-width="100" sortable="custom">
                <template #default="scope">
                    {{ formatNumber(scope.row.data?.interaction) }}
                </template>
            </el-table-column>

            <!-- 作品数 -->
            <el-table-column label="作品数" width="100" sortable="custom">
                <template #default="scope">
                    <div class="works-count-wrapper" @click="viewWorks(scope.row)">
                        <span class="works-count">{{ formatNumber(scope.row.data?.notes) }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 状态 -->
            <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                    <el-tag :type="getStatusTagType(scope.row.status)">
                        {{ getStatusText(scope.row.status) }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 上次更新时间 -->
            <el-table-column prop="lastRunTime" label="上次更新" min-width="150" sortable="custom">
                <template #default="scope">
                    {{ formatDate(scope.row.lastRunTime) }}
                </template>
            </el-table-column>

            <!-- 操作 -->
            <el-table-column label="操作" width="350" fixed="right">
                <template #default="scope">
                    <el-button size="small" @click="pauseTask(scope.row.id)"
                        :disabled="scope.row.status === 'paused'">暂停</el-button>
                    <el-button size="small" @click="resumeTask(scope.row.id)"
                        :disabled="scope.row.status === 'running'">恢复</el-button>
                    <el-button size="small" type="primary" @click="editTask(scope.row)">编辑</el-button>
                    <el-tooltip content="刷新博主信息" placement="top">
                        <el-button size="small" type="info" @click="refreshBloggerInfo(scope.row)" :loading="scope.row.refreshing">
                            刷新
                        </el-button>
                    </el-tooltip>
                    <el-popconfirm title="确认删除此监控任务?" @confirm="deleteTask(scope.row.id)">
                        <template #reference>
                            <el-button size="small" type="danger">删除</el-button>
                        </template>
                    </el-popconfirm>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-container" v-if="tasks.length > 0">
            <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize"
                :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="totalTasks"
                @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </div>
    </el-card>

    <!-- 编辑任务对话框 -->
    <el-dialog v-model="editDialogVisible" title="编辑监控任务" width="500px" :before-close="handleEditDialogClose">
        <el-form :model="editForm" :rules="editFormRules" ref="editFormRef" label-width="100px">
            <el-form-item label="任务名称" prop="name">
                <el-input v-model="editForm.name" placeholder="请输入任务名称"></el-input>
            </el-form-item>
            <el-form-item label="博主ID" prop="bloggerId">
                <el-input v-model="editForm.bloggerId" placeholder="请输入博主ID" disabled></el-input>
                <div class="form-tip">博主ID不可修改</div>
            </el-form-item>
            <el-form-item label="监控频率" prop="frequency">
                <el-select v-model="editForm.frequency" placeholder="请选择监控频率" style="width: 100%">
                    <el-option label="每小时" :value="60"></el-option>
                    <el-option label="每天" :value="1440"></el-option>
                    <el-option label="每周" :value="10080"></el-option>
                    <el-option label="每月" :value="43200"></el-option>
                </el-select>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="handleEditDialogClose">取消</el-button>
                <el-button type="primary" @click="saveEditTask" :loading="editLoading">保存</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
// 使用简单路径
const defaultAvatar = ''; // 默认为空字符串，会显示Element Plus默认头像

// 定义组件接收的属性
const props = defineProps({
    tasks: {
        type: Array,
        default: () => []
    }
});

// 定义组件发出的事件
const emit = defineEmits(['view-works', 'refresh-tasks']);

// 获取ipcRenderer
const ipcRenderer = (window as any).ipcRenderer;

// 本地状态
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const totalTasks = ref(0);

// 编辑对话框相关状态
const editDialogVisible = ref(false);
const editLoading = ref(false);
const editFormRef = ref<FormInstance>();
const editForm = ref({
    id: '',
    name: '',
    bloggerId: '',
    frequency: 60
});

// 编辑表单验证规则
const editFormRules: FormRules = {
    name: [
        { required: true, message: '请输入任务名称', trigger: 'blur' },
        { min: 1, max: 50, message: '任务名称长度在 1 到 50 个字符', trigger: 'blur' }
    ],
    frequency: [
        { required: true, message: '请选择监控频率', trigger: 'change' }
    ]
};

// 计算属性：获取分页后的任务列表
const paginatedTasks = computed(() => {
    const start = (currentPage.value - 1) * pageSize.value;
    const end = start + pageSize.value;
    return props.tasks.slice(start, end);
});

// 更新总任务数
totalTasks.value = computed(() => props.tasks.length).value;

/**
 * 格式化数字显示，如1000显示为1k
 * @param num 需要格式化的数字
 * @returns 格式化后的字符串
 */
const formatNumber = (num: number | undefined): string => {
    if (num === undefined || num === null) return '0';

    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }

    return num.toString();
};

/**
 * 格式化日期显示
 * @param timestamp 时间戳
 * @returns 格式化后的日期字符串
 */
const formatDate = (timestamp: number | undefined): string => {
    if (!timestamp) return '未运行';

    try {
        const date = new Date(timestamp);
        if (isNaN(date.getTime())) return '未知时间';

        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (error) {
        return '时间格式错误';
    }
};

/**
 * 获取状态对应的标签类型
 * @param status 任务状态
 * @returns 对应的Element Plus标签类型
 */
const getStatusTagType = (status: string): string => {
    switch (status) {
        case 'running':
            return 'success';
        case 'paused':
            return 'warning';
        case 'stopped':
            return 'info';
        default:
            return 'info';
    }
};

/**
 * 获取状态对应的中文文本
 * @param status 任务状态
 * @returns 状态的中文名称
 */
const getStatusText = (status: string): string => {
    switch (status) {
        case 'running':
            return '运行中';
        case 'paused':
            return '已暂停';
        case 'stopped':
            return '已停止';
        default:
            return '未知';
    }
};

/**
 * 查看博主作品
 * @param task 任务对象
 */
const viewWorks = (task: any) => {
    emit('view-works', task.bloggerId, task.name);
};

/**
 * 暂停任务
 * @param id 任务ID
 */
const pauseTask = async (id: string) => {
    try {
        loading.value = true;
        const result = await ipcRenderer.invoke('monitor:pause-task', id);

        if (result && result.success) {
            ElMessage.success('任务已暂停');
            emit('refresh-tasks'); // 通知父组件刷新任务列表
        } else {
            const errorMessage = result && result.message ? result.message : '未知错误';
            ElMessage.error(`暂停任务失败: ${errorMessage}`);
        }
    } catch (error: any) {
        console.error('暂停任务失败:', error);
        ElMessage.error('暂停任务失败: ' + (error.message || '未知错误'));
    } finally {
        loading.value = false;
    }
};

/**
 * 恢复任务
 * @param id 任务ID
 */
const resumeTask = async (id: string) => {
    try {
        loading.value = true;
        const result = await ipcRenderer.invoke('monitor:resume-task', id);

        if (result && result.success) {
            ElMessage.success('任务已恢复');
            emit('refresh-tasks'); // 通知父组件刷新任务列表
        } else {
            const errorMessage = result && result.message ? result.message : '未知错误';
            ElMessage.error(`恢复任务失败: ${errorMessage}`);
        }
    } catch (error: any) {
        console.error('恢复任务失败:', error);
        ElMessage.error('恢复任务失败: ' + (error.message || '未知错误'));
    } finally {
        loading.value = false;
    }
};

/**
 * 删除任务
 * @param id 任务ID
 */
const deleteTask = async (id: string) => {
    try {
        loading.value = true;
        const result = await ipcRenderer.invoke('monitor:delete-task', id);

        if (result && result.success) {
            ElMessage.success('任务已删除');
            emit('refresh-tasks'); // 通知父组件刷新任务列表
        } else {
            const errorMessage = result && result.message ? result.message : '未知错误';
            ElMessage.error(`删除任务失败: ${errorMessage}`);
        }
    } catch (error: any) {
        console.error('删除任务失败:', error);
        ElMessage.error('删除任务失败: ' + (error.message || '未知错误'));
    } finally {
        loading.value = false;
    }
};

/**
 * 编辑任务
 * @param task 任务对象
 */
const editTask = (task: any) => {
    editForm.value = {
        id: task.id,
        name: task.name,
        bloggerId: task.bloggerId,
        frequency: task.frequency
    };
    editDialogVisible.value = true;
};

/**
 * 保存编辑任务
 */
const saveEditTask = async () => {
    if (!editFormRef.value) return;
    
    try {
        // 验证表单
        await editFormRef.value.validate();
        
        editLoading.value = true;
        const result = await ipcRenderer.invoke('monitor:update-task', {
            id: editForm.value.id,
            name: editForm.value.name,
            frequency: editForm.value.frequency
        });

        if (result && result.success) {
            ElMessage.success('任务更新成功');
            editDialogVisible.value = false;
            emit('refresh-tasks'); // 通知父组件刷新任务列表
        } else {
            const errorMessage = result && result.message ? result.message : '未知错误';
            ElMessage.error(`更新任务失败: ${errorMessage}`);
        }
    } catch (error: any) {
        console.error('更新任务失败:', error);
        if (error.message !== 'Form validation failed') {
            ElMessage.error('更新任务失败: ' + (error.message || '未知错误'));
        }
    } finally {
        editLoading.value = false;
    }
};

/**
 * 处理编辑对话框关闭
 */
const handleEditDialogClose = () => {
    editDialogVisible.value = false;
    editFormRef.value?.resetFields();
};

/**
 * 刷新博主信息
 * @param task 任务对象
 */
const refreshBloggerInfo = async (task: any) => {
    try {
        // 设置刷新状态
        task.refreshing = true;
        
        // 获取最新的博主信息
        const result = await ipcRenderer.invoke('xhs-user-info', task.bloggerId);
        
        if (result) {
            // 更新任务信息
            const updateResult = await ipcRenderer.invoke('monitor:update-task', {
                id: task.id,
                name: result.nickname || result.name || task.name,
                frequency: task.frequency
            });

            if (updateResult && updateResult.success) {
                ElMessage.success(`博主 ${result.nickname || result.name} 信息刷新成功`);
                emit('refresh-tasks'); // 通知父组件刷新任务列表
            } else {
                ElMessage.error('刷新博主信息失败: ' + (updateResult.message || '未知错误'));
            }
        } else {
            ElMessage.error('获取博主信息失败，请检查博主ID是否正确');
        }
    } catch (error: any) {
        console.error('刷新博主信息失败:', error);
        ElMessage.error('刷新博主信息失败: ' + (error.message || '未知错误'));
    } finally {
        task.refreshing = false;
    }
};

/**
 * 处理页大小变化
 * @param newSize 新的页大小
 */
const handleSizeChange = (newSize: number) => {
    pageSize.value = newSize;
    currentPage.value = 1; // 重置为第一页
};

/**
 * 处理当前页变化
 * @param newPage 新的当前页
 */
const handleCurrentChange = (newPage: number) => {
    currentPage.value = newPage;
};

/**
 * 处理表格排序变化
 * @param param 排序参数
 */
const handleSortChange = (param: { column: any; prop: string; order: string }) => {
    // 实现本地排序，生产环境可以改为向服务端请求排序
    if (!param.prop || !param.order) return;

    const isAsc = param.order === 'ascending';

    props.tasks.sort((a: any, b: any) => {
        const valueA = a[param.prop];
        const valueB = b[param.prop];

        // 处理undefined或null
        if (valueA === undefined || valueA === null) return isAsc ? -1 : 1;
        if (valueB === undefined || valueB === null) return isAsc ? 1 : -1;

        // 数字比较
        if (typeof valueA === 'number' && typeof valueB === 'number') {
            return isAsc ? valueA - valueB : valueB - valueA;
        }

        // 字符串比较
        return isAsc
            ? String(valueA).localeCompare(String(valueB))
            : String(valueB).localeCompare(String(valueA));
    });
};
</script>

<style scoped lang="scss">
@use '../../assets/styles/global.scss' as *;

.blogger-task-list-card {
    margin-bottom: 20px;
    border-radius: $border-radius;
    box-shadow: $box-shadow;

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: $font-size-large;
        font-weight: bold;
        color: $primary-color;
    }

    .el-table {
        --el-table-header-bg-color: #f5f7fa;
        --el-table-row-hover-bg-color: #f0f2f5;

        .el-button {
            margin-right: 5px;

            &:last-child {
                margin-right: 0;
            }
        }

        .works-count-wrapper {
            cursor: pointer;
            display: inline-block;
        }

        .works-count {
            display: inline-block;
            min-width: 24px;
            padding: 0 8px;
            font-size: 14px;
            line-height: 24px;
            text-align: center;
            background-color: #f0f7ff;
            color: #409eff;
            border-radius: 12px;
            font-weight: bold;
            border: 1px solid #d9ecff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            transition: all 0.2s ease;

            &:hover {
                background-color: #ecf5ff;
                transform: translateY(-1px);
                box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08);
            }
        }
    }

    .pagination-container {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;
    }
}

.form-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}
</style>