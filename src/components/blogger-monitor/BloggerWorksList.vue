<template>
    <el-card class="blogger-works-card">
        <template #header>
            <div class="card-header">
                <span>{{ bloggerName || bloggerId }} 的作品列表</span>
                <el-button @click="$emit('close')" circle type="text">
                    <el-icon>
                        <Close />
                    </el-icon>
                </el-button>
            </div>
        </template>

        <div v-loading="loading" class="works-container">
            <!-- 作品为空时显示 -->
            <el-empty v-if="!loading && (!works || works.length === 0)" description="暂无作品数据"></el-empty>

            <!-- 作品列表 -->
            <el-row :gutter="20" v-else>
                <el-col :span="6" v-for="work in works" :key="work.noteId" class="work-col">
                    <div class="work-card" :class="{ 'deleted-work': work.deleted }">
                        <!-- 作品封面 -->
                        <div class="work-cover">
                            <el-image :src="work.cover" fit="cover" lazy>
                                <template #error>
                                    <div class="image-error">
                                        <el-icon>
                                            <PictureFilled />
                                        </el-icon>
                                    </div>
                                </template>
                            </el-image>

                            <!-- 添加笔记类型和置顶标签 -->
                            <div class="work-tags">
                                <el-tag v-if="work.type === 'video'" size="small" type="danger" effect="dark">
                                    <el-icon class="tag-icon">
                                        <VideoCamera />
                                    </el-icon>视频
                                </el-tag>
                                <el-tag v-else size="small" type="success" effect="dark">
                                    <el-icon class="tag-icon">
                                        <Picture />
                                    </el-icon>图文
                                </el-tag>
                            </div>

                            <!-- 置顶标签 -->
                            <div v-if="work.sticky" class="sticky-tag">
                                置顶
                            </div>

                            <!-- 删除状态标签 -->
                            <div v-if="work.deleted" class="deleted-tag">
                                已删除
                            </div>
                        </div>

                        <!-- 作品信息区域 -->
                        <div class="work-info">
                            <!-- 作品标题 -->
                            <div class="work-title">{{ work.title || '无标题' }}</div>

                            <!-- 点赞数和操作按钮放在同一行 -->
                            <div class="work-footer">
                                <!-- 点赞数 -->
                                <div class="work-stats">
                                    <Icon class="like-icon" icon="mdi:heart" />
                                    <span>{{ work.likeCount }}</span>
                                </div>

                                <!-- 操作按钮 -->
                                <div class="work-actions">
                                    <!-- 查看详情 -->
                                    <el-tooltip content="查看详情" placement="top">
                                        <el-link :href="work.url" target="_blank" :underline="false"
                                            class="action-icon-link">
                                            <el-icon>
                                                <View />
                                            </el-icon>
                                        </el-link>
                                    </el-tooltip>

                                    <!-- 复制链接 -->
                                    <el-tooltip content="复制链接" placement="top">
                                        <el-button circle size="small" @click="copyLink(work)" type="info" text>
                                            <el-icon>
                                                <CopyDocument />
                                            </el-icon>
                                        </el-button>
                                    </el-tooltip>

                                    <!-- 添加监控 -->
                                    <el-tooltip content="添加到监控任务" placement="top">
                                        <el-button circle size="small" @click="addNoteMonitorTask(work)" type="info"
                                            text>
                                            <el-icon>
                                                <Monitor />
                                            </el-icon>
                                        </el-button>
                                    </el-tooltip>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-col>
            </el-row>

            <!-- 分页组件 -->
            <div class="pagination-container" v-if="works && works.length > 0">
                <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[50, 100]"
                    layout="total, sizes, prev, pager, next" :total="totalWorks" @size-change="fetchWorks"
                    @current-change="fetchWorks" />
            </div>
        </div>
    </el-card>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, defineProps, defineEmits } from 'vue';
import { ElMessage } from 'element-plus';
import { Icon } from "@iconify/vue";
import { Close, PictureFilled, Star, View, CopyDocument, Monitor, VideoCamera, Picture, Top } from '@element-plus/icons-vue';
import { v4 as uuidv4 } from 'uuid';

// 定义组件接收的属性
const props = defineProps({
    bloggerId: {
        type: String,
        required: true
    },
    bloggerName: {
        type: String,
        default: ''
    }
});

// 定义组件发出的事件
defineEmits(['close']);

// 获取ipcRenderer
const ipcRenderer = (window as any).ipcRenderer;

// 本地状态
const loading = ref(false);
const works = ref<any[]>([]);
const currentPage = ref(1);
const pageSize = ref(50);
const totalWorks = ref(0);

// 监听bloggerId变化
watch(() => props.bloggerId, (newVal) => {
    if (newVal) {
        currentPage.value = 1; // 重置到第一页
        fetchWorks();
    } else {
        works.value = [];
    }
});

/**
 * 获取博主作品列表
 */
const fetchWorks = async () => {
    if (!props.bloggerId) return;

    loading.value = true;
    try {
        const result = await ipcRenderer.invoke('xhs:get-blogger-notes',
            props.bloggerId,
            {
                page: currentPage.value,
                pageSize: pageSize.value,
                sort: 'original' // 使用原始爬取顺序，保持与小红书显示一致
            }
        );

        if (result && result.notes) {
            works.value = result.notes;
            totalWorks.value = result.total || result.notes.length;
        } else {
            works.value = [];
            totalWorks.value = 0;
            ElMessage.warning('未获取到博主作品数据');
        }
    } catch (error: any) {
        console.error('获取博主作品失败:', error);
        ElMessage.error('获取博主作品失败: ' + (error.message || '未知错误'));
        works.value = [];
        totalWorks.value = 0;
    } finally {
        loading.value = false;
    }
};

/**
 * 打开作品详情页
 * @param work 作品数据对象
 */
const openWork = (work: any) => {
    if (!work || !work.url) {
        ElMessage.warning('作品链接不可用');
        return;
    }

    // 使用系统浏览器打开链接
    ipcRenderer.send('open-external-link', work.url);
};

/**
 * 复制作品链接
 * @param work 作品数据对象
 */
const copyLink = (work: any) => {
    if (!work || !work.url) {
        ElMessage.warning('作品链接不可用');
        return;
    }

    // 复制链接到剪贴板
    navigator.clipboard.writeText(work.url)
        .then(() => ElMessage.success('链接已复制到剪贴板'))
        .catch(() => ElMessage.error('复制链接失败'));
};

/**
 * 格式化数字显示，如1000显示为1k
 * @param num 需要格式化的数字
 * @returns 格式化后的字符串
 */
const formatNumber = (num: number | undefined): string => {
    if (num === undefined || num === null) return '0';

    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }

    return num.toString();
};

/**
 * 添加笔记监控任务
 * @param work 作品数据对象
 */
const addNoteMonitorTask = async (work: any) => {
    if (!work || !work.url) {
        ElMessage.warning('作品信息不完整，无法添加监控');
        return;
    }

    try {
        // 创建任务配置
        const taskConfig = {
            type: 'note', // 指定为笔记类型
            noteUrl: work.url,
            frequency: 1440, // 默认每天监控一次
        };

        // 添加监控任务
        const result = await ipcRenderer.invoke('monitor:add-task', taskConfig);

        if (result && result.success) {
            ElMessage.success('已添加到笔记监控任务');
        } else {
            ElMessage.error('添加监控任务失败: ' + (result.message || '未知错误'));
        }
    } catch (error: any) {
        console.error('添加笔记监控任务失败:', error);
        ElMessage.error('添加监控任务失败: ' + (error.message || '未知错误'));
    }
};

// 组件挂载后获取数据
onMounted(() => {
    if (props.bloggerId) {
        fetchWorks();
    }
});
</script>

<style scoped lang="scss">
@use '../../assets/styles/global.scss' as *;

.blogger-works-card {
    margin-top: 20px;
    border-radius: $border-radius;
    box-shadow: $box-shadow;

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: $font-size-large;
        font-weight: bold;
        color: $primary-color;
    }

    .works-container {
        min-height: 400px;
        position: relative;

        .work-col {
            margin-bottom: 20px;
        }

        .work-card {
            border-radius: $border-radius-small;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

            &:hover {
                transform: translateY(-5px);
                box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
            }

            .work-cover {
                position: relative;
                width: 100%;
                height: 0;
                padding-bottom: 100%;
                overflow: hidden;
                cursor: pointer;

                .el-image {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }

                .image-error {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    background-color: #f5f7fa;
                    color: #909399;
                    font-size: 24px;
                }

                /* 添加标签样式 */
                .work-tags {
                    position: absolute;
                    top: 10px;
                    left: 10px;
                    display: flex;
                    gap: 5px;
                    z-index: 10;

                    .el-tag {
                        display: flex;
                        align-items: center;
                        padding: 0 6px;
                        height: 22px;
                        font-size: 12px;
                        border-radius: 4px;
                        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);

                        .tag-icon {
                            margin-right: 2px;
                            font-size: 12px;
                        }
                    }
                }

                /* 置顶标签样式 */
                .sticky-tag {
                    position: absolute;
                    top: 10px;
                    right: 0;
                    background-color: #ff4d4f;
                    color: white;
                    padding: 2px 10px;
                    margin-right: 10px;
                    font-size: 12px;
                    border-radius: 12px;
                    border-bottom-left-radius: 12px;
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
                    z-index: 10;
                }

                /* 删除状态标签样式 */
                .deleted-tag {
                    position: absolute;
                    top: 40px;
                    right: 0;
                    background-color: #909399;
                    color: white;
                    padding: 2px 10px;
                    margin-right: 10px;
                    font-size: 12px;
                    border-radius: 12px;
                    border-bottom-left-radius: 12px;
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
                    z-index: 10;
                }
            }

            .work-info {
                padding: 10px;

                .work-title {
                    font-size: 14px;
                    font-weight: bold;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    cursor: pointer;
                    margin-bottom: 8px;
                }

                .work-footer {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding-top: 8px;
                    border-top: 1px solid #f0f0f0;
                }

                .work-stats {
                    display: flex;
                    align-items: center;
                    color: $primary-light;
                    font-size: 14px;

                    .like-icon {
                        margin-right: 4px;
                        font-size: 16px;
                    }
                }
            }

            .work-actions {
                display: flex;
                align-items: center;
                gap: 8px;

                .el-button {
                    margin: 0;

                    .el-icon {
                        font-size: 16px;
                    }
                }

                .action-icon-link {
                    color: var(--el-text-color-secondary);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: 24px;
                    height: 24px;
                    border-radius: 50%;

                    &:hover {
                        color: var(--el-color-primary);
                        background: var(--el-fill-color-light);
                    }

                    .el-icon {
                        font-size: 16px;
                    }
                }
            }

            // 删除状态的作品卡片样式
            &.deleted-work {
                opacity: 0.6;
                filter: grayscale(0.5);
                
                &:hover {
                    transform: none;
                    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
                }

                .work-info {
                    .work-title {
                        color: #909399;
                        text-decoration: line-through;
                    }

                    .work-stats {
                        color: #c0c4cc;
                    }
                }
            }
        }
    }

    .pagination-container {
        margin-top: 20px;
        display: flex;
        justify-content: center;
    }
}
</style>