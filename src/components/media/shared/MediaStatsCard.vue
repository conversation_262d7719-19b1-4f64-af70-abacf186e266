<template>
  <div class="media-stats-card" :class="{ compact }">
    <div class="stats-grid">
      <div class="stat-item" v-for="(stat, key) in formattedStats" :key="key">
        <div class="stat-icon">
          <Icon :icon="getStatIcon(key)" :class="getStatIconClass(key)" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stat.value }}</div>
          <div class="stat-label">{{ stat.label }}</div>
        </div>
      </div>
    </div>
    
    <div class="stats-actions" v-if="showActions && !compact">
      <el-button size="small" @click="$emit('refresh')" :loading="loading">
        <Icon icon="mdi:refresh" />
        刷新
      </el-button>
      <el-button size="small" @click="$emit('toggle-details')">
        <Icon icon="mdi:chart-line" />
        {{ showDetails ? '隐藏' : '显示' }}详情
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Icon } from '@iconify/vue'

interface StatsData {
  totalProcessed: number
  activeTasks: number
  successRate: number
  totalSize: number
}

interface Props {
  stats: StatsData
  compact?: boolean
  showActions?: boolean
  showDetails?: boolean
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  compact: false,
  showActions: true,
  showDetails: false,
  loading: false
})

defineEmits<{
  refresh: []
  'toggle-details': []
}>()

const formattedStats = computed(() => ({
  totalProcessed: {
    value: props.stats.totalProcessed.toString(),
    label: '已处理'
  },
  activeTasks: {
    value: props.stats.activeTasks.toString(),
    label: '进行中'
  },
  successRate: {
    value: `${props.stats.successRate.toFixed(1)}%`,
    label: '成功率'
  },
  totalSize: {
    value: formatFileSize(props.stats.totalSize),
    label: '总大小'
  }
}))

const getStatIcon = (key: string): string => {
  const icons = {
    totalProcessed: 'mdi:check-circle',
    activeTasks: 'mdi:play-circle',
    successRate: 'mdi:chart-line-variant',
    totalSize: 'mdi:harddisk'
  }
  return icons[key as keyof typeof icons] || 'mdi:information'
}

const getStatIconClass = (key: string): string => {
  const classes = {
    totalProcessed: 'text-success',
    activeTasks: 'text-primary',
    successRate: 'text-warning',
    totalSize: 'text-info'
  }
  return classes[key as keyof typeof classes] || ''
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}
</script>

<style lang="scss" scoped>
.media-stats-card {
  padding: $spacing-base;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid $border-light;
  border-radius: $border-radius-lg;
  
  &.compact {
    padding: $spacing-sm;
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: $spacing-base;
    margin-bottom: $spacing-base;
    
    .stat-item {
      display: flex;
      align-items: center;
      gap: $spacing-sm;
      padding: $spacing-sm;
      background: rgba(255, 255, 255, 0.7);
      border-radius: $border-radius;
      transition: transform 0.2s ease;
      
      &:hover {
        transform: translateY(-2px);
        background: rgba(255, 255, 255, 0.9);
      }
      
      .stat-icon {
        font-size: 24px;
        
        &.text-success { color: $color-success; }
        &.text-primary { color: $color-primary; }
        &.text-warning { color: $color-warning; }
        &.text-info { color: $color-info; }
      }
      
      .stat-content {
        .stat-value {
          font-size: 18px;
          font-weight: 600;
          color: $text-primary;
          line-height: 1;
          margin-bottom: 2px;
        }
        
        .stat-label {
          font-size: 12px;
          color: $text-secondary;
          line-height: 1;
        }
      }
    }
  }
  
  .stats-actions {
    display: flex;
    gap: $spacing-sm;
    justify-content: center;
    padding-top: $spacing-sm;
    border-top: 1px solid $border-light;
  }
  
  &.compact {
    .stats-grid {
      grid-template-columns: repeat(4, 1fr);
      gap: $spacing-sm;
      margin-bottom: 0;
      
      .stat-item {
        flex-direction: column;
        text-align: center;
        padding: $spacing-xs;
        
        .stat-icon {
          font-size: 20px;
        }
        
        .stat-content {
          .stat-value {
            font-size: 16px;
          }
          
          .stat-label {
            font-size: 11px;
          }
        }
      }
    }
    
    .stats-actions {
      display: none;
    }
  }
}

@media (max-width: 768px) {
  .media-stats-card {
    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }
    
    &.compact .stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}
</style>