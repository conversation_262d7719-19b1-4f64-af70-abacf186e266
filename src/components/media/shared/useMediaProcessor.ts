import { computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useMediaTasksStore, useMediaSettingsStore, useMediaMainStore } from '@/stores'
import { useMediaEvents } from '@/composables/useMediaEvents'

export interface MediaProcessorConfig {
  taskType: 'video-convert' | 'audio-extract' | 'asr' | 'image-process'
  defaultOptions?: Record<string, any>
  maxFileSize?: number
  maxFileCount?: number
  supportedFormats?: string[]
}

export function useMediaProcessor(config: MediaProcessorConfig) {
  const tasksStore = useMediaTasksStore()
  const settingsStore = useMediaSettingsStore()
  const mainStore = useMediaMainStore()
  const { mediaEventBus } = useMediaEvents()

  // 内置默认选项配置
  const builtInDefaults = {
    'video-convert': {
      outputFormat: 'mp3',
      quality: '192k',
      resizeEnabled: false,
      maxWidth: 1280
    },
    'audio-extract': {
      format: 'mp3',
      quality: 'high'
    },
    'asr': {
      language: 'zh',
      outputFormats: ['txt', 'srt'] as string[],
      audioQuality: 'high',
      enableTimestamps: true,
      filterSilence: false
    },
    'image-process': {
      outputFormat: 'jpeg',
      quality: '80',
      resizeEnabled: false,
      width: 1920,
      height: 1080,
      maintainAspectRatio: true,
      optimize: true,
      progressive: false,
      stripMetadata: true,
      colorSpace: 'srgb',
      saturation: 1.0
    }
  } as const

  // 完全使用 Store 状态，不维护本地状态
  const fileList = computed({
    get: () => tasksStore.getPendingFiles(config.taskType),
    set: (files) => tasksStore.setPendingFiles(config.taskType, files)
  })

  const processingOptions = computed({
    get: () => {
      // 优先级：store中的选项 > 传入的默认选项 > 内置默认选项
      const storeOptions = tasksStore.getTaskOptions(config.taskType)
      const taskDefaults = builtInDefaults[config.taskType]

      return {
        ...taskDefaults,
        ...(config.defaultOptions || {}),
        ...(storeOptions || {})
      }
    },
    set: (options) => tasksStore.setTaskOptions(config.taskType, options)
  })

  const outputDirectory = computed({
    get: () => settingsStore.getOutputDirectory(config.taskType),
    set: (dir) => settingsStore.setOutputDirectory(config.taskType, dir)
  })

  // 活动任务和结果
  const activeTasks = computed(() => {
    return tasksStore.allTasks.filter(task =>
      task.type === config.taskType &&
      ['pending', 'processing'].includes(task.status)
    )
  })

  const results = computed(() => {
    return Array.from(tasksStore.taskResults.values())
      .filter(result => result.taskType === config.taskType)
      .sort((a, b) => b.completedAt - a.completedAt)
  })

  // 任务操作直接委托给 Store
  const startProcessing = async () => {
    if (fileList.value.length === 0) {
      ElMessage.warning('请先选择文件')
      return
    }

    if (!outputDirectory.value) {
      ElMessage.warning('请选择输出目录')
      return
    }

    try {
      await mainStore.processFiles(
        config.taskType,
        fileList.value,
        JSON.parse(JSON.stringify(processingOptions.value)),
        outputDirectory.value
      )
    } catch (error: any) {
      ElMessage.error(error.message || '处理失败')
    }
  }

  const pauseProcessing = async () => {
    for (const task of activeTasks.value) {
      if (task.status === 'processing') {
        await mainStore.pauseSingleTask(task.id)
      }
    }
  }

  const stopProcessing = async () => {
    for (const task of activeTasks.value) {
      await tasksStore.removeSingleTask(task.id)
    }
    fileList.value = []
  }

  // 事件处理方法（仅用于日志记录，不发送事件避免循环）
  const updateTaskProgress = (taskId: string, progress: number, currentStep?: string) => {
    console.log(`[MediaProcessor] 任务进度更新: ${taskId} - ${progress}%`, currentStep)
  }

  const updateTaskStatus = async (taskId: string, status: string, error?: string) => {
    console.log(`[MediaProcessor] 任务状态更新: ${taskId} - ${status}`, error)
  }


  // 文件处理
  const handleFileAdded = (file: any) => {
    console.log('文件已添加:', file.name)

    // 自动设置输出目录（如果未设置）
    if (!outputDirectory.value) {
      outputDirectory.value = settingsStore.settings.defaultOutputDir
    }
  }

  const handleFileRemoved = (file: any) => {
    console.log('文件已移除:', file.name)
  }

  const handleError = (error: string) => {
    ElMessage.error(error)
  }


  // 初始化和监听任务完成事件
  onMounted(() => {
    // 初始化默认选项（如果 store 中没有）
    if (!tasksStore.getTaskOptions(config.taskType)) {
      const defaultOptions = {
        ...builtInDefaults[config.taskType],
        ...(config.defaultOptions || {})
      }
      tasksStore.setTaskOptions(config.taskType, defaultOptions)
    }

    const handleTaskCompleted = ({ taskId, result }: { taskId: string, result: any }) => {
      const task = tasksStore.singleTasks.get(taskId)
      if (task && task.type === config.taskType) {
        console.log(`[MediaProcessor] 处理任务完成: ${taskId}, 类型: ${config.taskType}`)

        // 从待处理文件列表中移除已完成的文件
        const fileUid = result.fileUid || task.fileUid
        if (fileUid) {
          const fileIndex = fileList.value.findIndex(f =>
            (f as any).uid === fileUid || (f as any).uid?.toString() === fileUid.toString()
          )
          if (fileIndex > -1) {
            const newFiles = [...fileList.value]
            newFiles.splice(fileIndex, 1)
            fileList.value = newFiles
            console.log(`[MediaProcessor] 已从文件列表移除文件, uid: ${fileUid}`)
          } else {
            console.warn(`[MediaProcessor] 未找到对应的文件, uid: ${fileUid}`)
          }
        } else {
          console.warn(`[MediaProcessor] 任务无fileUid信息，无法移除文件: ${task.fileName}`)
        }

        tasksStore.saveToPersistent()

        // 显示完成通知
        if (settingsStore.settings.enableNotifications) {
          ElMessage.success(`任务完成: ${result.fileName || task.fileName}`)
        }
      }
    }

    // 监听内部任务完成事件
    mediaEventBus.on('task:result-added', handleTaskCompleted)

    onUnmounted(() => {
      mediaEventBus.off('task:result-added', handleTaskCompleted)
    })
  })

  return {
    // 状态
    fileList,
    processingOptions,
    outputDirectory,
    activeTasks,
    results,

    // 方法
    startProcessing,
    pauseProcessing,
    stopProcessing,
    updateTaskProgress,
    updateTaskStatus,
    handleFileAdded,
    handleFileRemoved,
    handleError
  }
}