<template>
  <div class="output-directory-selector">
    <el-form-item :label="label">
      <div class="selector-container">
        <el-input 
          :model-value="modelValue" 
          :placeholder="placeholder"
          readonly
          class="directory-input"
        >
          <template #prefix>
            <Icon icon="mdi:folder-outline" />
          </template>
        </el-input>
        <el-button @click="selectDirectory" :loading="selecting">
          <Icon icon="mdi:folder-open-outline" />
          浏览
        </el-button>
        <el-button 
          v-if="showReset && modelValue" 
          @click="resetToDefault"
          type="text"
        >
          <Icon icon="mdi:refresh" />
          重置
        </el-button>
      </div>
      
      <!-- 目录信息显示 -->
      <div class="directory-info" v-if="modelValue && showInfo">
        <div class="info-item">
          <Icon icon="mdi:information" />
          <span>输出目录: {{ displayPath }}</span>
        </div>
        <div class="info-item" v-if="showValidation">
          <Icon :icon="isValidPath ? 'mdi:check-circle' : 'mdi:alert-circle'" 
                :class="isValidPath ? 'success-icon' : 'error-icon'" />
          <span>{{ pathValidationMessage }}</span>
        </div>
      </div>
    </el-form-item>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Icon } from '@iconify/vue'

// Props
interface Props {
  modelValue: string
  label?: string
  placeholder?: string
  defaultPath?: string
  showReset?: boolean
  showInfo?: boolean
  showValidation?: boolean
  required?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  label: '输出目录',
  placeholder: '选择输出目录',
  defaultPath: '~/Downloads/xhs-media-output',
  showReset: true,
  showInfo: true,
  showValidation: false,
  required: true
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [path: string]
  'directorySelected': [path: string]
  'error': [message: string]
}>()

// 响应式数据
const selecting = ref(false)

// 计算属性
const displayPath = computed(() => {
  if (!props.modelValue) return ''
  
  // 缩短过长的路径显示
  const maxLength = 50
  if (props.modelValue.length > maxLength) {
    return '...' + props.modelValue.slice(-maxLength)
  }
  return props.modelValue
})

const isValidPath = computed(() => {
  if (!props.modelValue) return false
  
  // 基本路径格式验证
  try {
    // 检查路径是否包含非法字符（Windows/macOS/Linux通用）
    const invalidChars = /[<>:"|?*]/
    if (invalidChars.test(props.modelValue)) {
      return false
    }
    
    // 检查路径长度
    if (props.modelValue.length > 260) { // Windows路径长度限制
      return false
    }
    
    return true
  } catch {
    return false
  }
})

const pathValidationMessage = computed(() => {
  if (!props.modelValue) {
    return props.required ? '请选择输出目录' : '未选择目录'
  }
  
  if (!isValidPath.value) {
    return '路径格式无效'
  }
  
  return '路径格式正确'
})

// 方法
const selectDirectory = async () => {
  if (selecting.value) return
  
  selecting.value = true
  
  try {
    const result = await window.electronAPI.app.showOpenDialog({
      properties: ['openDirectory'],
      title: '选择输出目录',
      defaultPath: props.modelValue || props.defaultPath
    })

    console.log('选择目录结果:', result)

    if (result && !result.canceled && result.filePaths && result.filePaths.length > 0) {
      const selectedPath = result.filePaths[0]
      
      emit('update:modelValue', selectedPath)
      emit('directorySelected', selectedPath)
      
      ElMessage.success('目录选择成功')
    }
  } catch (error: any) {
    console.error('选择目录失败:', error)
    const errorMessage = `选择目录失败: ${error?.message || '未知错误'}`
    ElMessage.error(errorMessage)
    emit('error', errorMessage)
  } finally {
    selecting.value = false
  }
}

const resetToDefault = () => {
  emit('update:modelValue', props.defaultPath)
  emit('directorySelected', props.defaultPath)
  ElMessage.info('已重置为默认目录')
}

// 设置默认目录（如果当前为空且提供了默认值）
const setDefaultIfEmpty = () => {
  if (!props.modelValue && props.defaultPath) {
    emit('update:modelValue', props.defaultPath)
  }
}

// 公开方法
const validate = (): boolean => {
  if (props.required && !props.modelValue) {
    emit('error', '请选择输出目录')
    return false
  }
  
  if (props.modelValue && !isValidPath.value) {
    emit('error', '输出目录路径格式无效')
    return false
  }
  
  return true
}

// 暴露方法给父组件
defineExpose({
  validate,
  setDefaultIfEmpty,
  selectDirectory,
  resetToDefault
})
</script>

<style scoped lang="scss">
@use '@/assets/styles/index.scss' as *;

.output-directory-selector {
  .selector-container {
    display: flex;
    gap: $spacing-small;
    align-items: flex-start;

    .directory-input {
      flex: 1;
      
      :deep(.el-input__prefix) {
        color: $text-secondary;
      }
    }
  }

  .directory-info {
    margin-top: $spacing-small;
    
    .info-item {
      display: flex;
      align-items: center;
      gap: $spacing-small;
      font-size: 12px;
      margin-bottom: 4px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .success-icon {
        color: $success-color;
      }
      
      .error-icon {
        color: $danger-color;
      }
      
      span {
        color: $text-secondary;
      }
    }
  }
}

:deep(.el-form-item) {
  margin-bottom: $spacing-base;

  .el-form-item__label {
    color: $text-regular;
    font-weight: 500;
  }
}
</style>