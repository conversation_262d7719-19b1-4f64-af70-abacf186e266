<template>
  <div class="task-result-panel">
    <!-- 结果概览 -->
    <div class="result-overview" v-if="results.length > 0">
      <div class="overview-stats">
        <div class="stat-item">
          <Icon icon="mdi:check-circle" class="success-icon" />
          <span class="stat-label">成功:</span>
          <span class="stat-value">{{ successCount }}</span>
        </div>
        <div class="stat-item" v-if="failedCount > 0">
          <Icon icon="mdi:alert-circle" class="error-icon" />
          <span class="stat-label">失败:</span>
          <span class="stat-value">{{ failedCount }}</span>
        </div>
        <div class="stat-item" v-if="totalSize > 0">
          <Icon icon="mdi:harddisk" />
          <span class="stat-label">总大小:</span>
          <span class="stat-value">{{ formatFileSize(totalSize) }}</span>
        </div>
        <div class="stat-item" v-if="totalDuration > 0">
          <Icon icon="mdi:clock" />
          <span class="stat-label">总时长:</span>
          <span class="stat-value">{{ formatDuration(totalDuration) }}</span>
        </div>
      </div>

      <!-- 批量操作 -->
      <div class="batch-actions" v-if="results.length > 1">
        <el-button size="small" @click="exportAllResults" :disabled="successCount === 0">
          <Icon icon="mdi:export" />
          导出所有结果
        </el-button>
        <el-button size="small" @click="openAllResults" :disabled="successCount === 0">
          <Icon icon="mdi:folder-open-multiple" />
          打开所有文件
        </el-button>
        <el-button size="small" @click="copyAllPaths" :disabled="successCount === 0">
          <Icon icon="mdi:content-copy" />
          复制路径
        </el-button>
      </div>
    </div>

    <!-- 结果列表 -->
    <div class="result-list" v-if="displayResults.length > 0">
      <div class="result-item" v-for="result in displayResults" :key="result.id"
        :class="{ 'result-error': !result.success }">
        <!-- 结果信息 -->
        <div class="result-info">
          <div class="result-icon">
            <Icon :icon="getResultIcon(result)" :class="getResultIconClass(result)" />
          </div>

          <div class="result-details">
            <div class="result-title">
              <span class="file-name">{{ result.fileName }}</span>
              <el-tag :type="result.success ? 'success' : 'danger'" size="small">
                {{ result.success ? '成功' : '失败' }}
              </el-tag>
            </div>

            <div class="result-meta" v-if="result.success">
              <span class="meta-item" v-if="result.outputPath">
                <Icon icon="mdi:file-outline" />
                {{ getFileName(result.outputPath) }}
              </span>
              <span class="meta-item" v-if="result.size">
                <Icon icon="mdi:harddisk" />
                {{ formatFileSize(result.size) }}
              </span>
              <span class="meta-item" v-if="result.duration">
                <Icon icon="mdi:clock" />
                {{ formatDuration(result.duration) }}
              </span>
              <span class="meta-item" v-if="result.completedAt">
                <Icon icon="mdi:calendar-clock" />
                {{ formatTime(result.completedAt) }}
              </span>
            </div>

            <!-- 错误信息 -->
            <div class="result-error-info" v-if="!result.success && result.error">
              <Icon icon="mdi:alert-circle" />
              <span>{{ result.error }}</span>
            </div>

            <!-- ASR特殊结果显示 -->
            <div class="asr-result-preview" v-if="result.success && result.type === 'asr' && result.data">
              <div class="text-preview" v-if="result.data.text">
                <div class="preview-label">识别文本预览:</div>
                <div class="preview-content">{{ getTextPreview(result.data.text) }}</div>
              </div>
              <div class="asr-stats" v-if="result.data.statistics">
                <span class="stat-chip">
                  <Icon icon="mdi:text" />
                  {{ result.data.statistics.segmentCount }} 片段
                </span>
                <span class="stat-chip">
                  <Icon icon="mdi:format-letter-case" />
                  {{ result.data.statistics.totalCharacters }} 字符
                </span>
                <span class="stat-chip" v-if="result.data.statistics.wordsPerMinute">
                  <Icon icon="mdi:speedometer" />
                  {{ Math.round(result.data.statistics.wordsPerMinute) }} 字/分
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 结果操作 -->
        <div class="result-actions">
          <!-- 通用操作 -->
          <el-button size="small" v-if="result.success && result.outputPath" @click="openResult(result)">
            <Icon icon="mdi:folder-open-outline" />
            打开文件夹
          </el-button>

          <el-button size="small" v-if="result.success && result.outputPath" @click="copyPath(result.outputPath)">
            <Icon icon="mdi:content-copy" />
            复制路径
          </el-button>

          <!-- ASR特殊操作 -->
          <template v-if="result.success && result.type === 'asr' && result.data">
            <el-button size="small" @click="viewASRResult(result)">
              <Icon icon="mdi:text-box-outline" />
              查看详情
            </el-button>

            <el-button size="small" v-if="result.data.text" @click="copyText(result.data.text)">
              <Icon icon="mdi:content-copy" />
              复制文本
            </el-button>
          </template>

          <!-- 重试操作 -->
          <el-button size="small" type="warning" v-if="!result.success && allowRetry" @click="retryResult(result)">
            <Icon icon="mdi:refresh" />
            重试
          </el-button>

          <!-- 移除操作 -->
          <el-button size="small" type="danger" @click="removeResult(result)">
            <Icon icon="mdi:close" />
            移除
          </el-button>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div class="empty-state" v-if="results.length === 0">
      <Icon icon="mdi:clipboard-check-multiple-outline" class="empty-icon" />
      <div class="empty-text">{{ emptyText }}</div>
      <div class="empty-hint">{{ emptyHint }}</div>
    </div>

    <!-- 分页控制 -->
    <div class="pagination-wrapper" v-if="showPagination && results.length > pageSize">
      <el-pagination v-model:current-page="currentPage" :page-size="pageSize" :total="results.length"
        layout="prev, pager, next, total" small />
    </div>

    <!-- ASR结果详情弹窗 -->
    <el-dialog v-model="asrDialogVisible" title="语音识别结果详情" width="80%" :before-close="closeASRDialog">
      <div class="asr-dialog-content" v-if="currentASRResult">
        <el-tabs v-model="activeASRTab">
          <el-tab-pane label="文本内容" name="text" v-if="currentASRResult.data.text">
            <div class="asr-content">
              <div class="content-actions">
                <el-button size="small" @click="copyText(currentASRResult.data.text)">
                  <Icon icon="mdi:content-copy" />
                  复制文本
                </el-button>
                <el-button size="small" @click="saveTextToFile(currentASRResult.data.text, 'txt')">
                  <Icon icon="mdi:download" />
                  保存文本
                </el-button>
              </div>
              <el-input type="textarea" :rows="15" v-model="currentASRResult.data.text" readonly class="asr-textarea" />
            </div>
          </el-tab-pane>

          <el-tab-pane label="SRT字幕" name="srt" v-if="currentASRResult.data.srt">
            <div class="asr-content">
              <div class="content-actions">
                <el-button size="small" @click="copyText(currentASRResult.data.srt)">
                  <Icon icon="mdi:content-copy" />
                  复制字幕
                </el-button>
                <el-button size="small" @click="saveTextToFile(currentASRResult.data.srt, 'srt')">
                  <Icon icon="mdi:download" />
                  保存SRT
                </el-button>
              </div>
              <el-input type="textarea" :rows="15" v-model="currentASRResult.data.srt" readonly class="asr-textarea" />
            </div>
          </el-tab-pane>

          <el-tab-pane label="VTT字幕" name="vtt" v-if="currentASRResult.data.vtt">
            <div class="asr-content">
              <div class="content-actions">
                <el-button size="small" @click="copyText(currentASRResult.data.vtt)">
                  <Icon icon="mdi:content-copy" />
                  复制VTT
                </el-button>
                <el-button size="small" @click="saveTextToFile(currentASRResult.data.vtt, 'vtt')">
                  <Icon icon="mdi:download" />
                  保存VTT
                </el-button>
              </div>
              <el-input type="textarea" :rows="15" v-model="currentASRResult.data.vtt" readonly class="asr-textarea" />
            </div>
          </el-tab-pane>

          <el-tab-pane label="统计信息" name="stats" v-if="currentASRResult.data.statistics">
            <div class="asr-stats-detail">
              <div class="stats-grid">
                <div class="stat-card">
                  <div class="stat-icon">
                    <Icon icon="mdi:text" />
                  </div>
                  <div class="stat-info">
                    <div class="stat-label">识别片段</div>
                    <div class="stat-value">{{ currentASRResult.data.statistics.segmentCount }} 个</div>
                  </div>
                </div>
                <div class="stat-card">
                  <div class="stat-icon">
                    <Icon icon="mdi:clock" />
                  </div>
                  <div class="stat-info">
                    <div class="stat-label">总时长</div>
                    <div class="stat-value">{{ formatDuration(currentASRResult.data.statistics.totalDuration) }}</div>
                  </div>
                </div>
                <div class="stat-card">
                  <div class="stat-icon">
                    <Icon icon="mdi:format-letter-case" />
                  </div>
                  <div class="stat-info">
                    <div class="stat-label">字符数</div>
                    <div class="stat-value">{{ currentASRResult.data.statistics.totalCharacters }}</div>
                  </div>
                </div>
                <div class="stat-card">
                  <div class="stat-icon">
                    <Icon icon="mdi:speedometer" />
                  </div>
                  <div class="stat-info">
                    <div class="stat-label">语速</div>
                    <div class="stat-value">{{ Math.round(currentASRResult.data.statistics.wordsPerMinute) }} 字/分钟</div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Icon } from '@iconify/vue'

// Props
interface Props {
  results: any[]
  allowRetry?: boolean
  showPagination?: boolean
  pageSize?: number
  emptyText?: string
  emptyHint?: string
}

const props = withDefaults(defineProps<Props>(), {
  allowRetry: true,
  showPagination: true,
  pageSize: 10,
  emptyText: '暂无处理结果',
  emptyHint: '完成任务后结果将显示在这里'
})

// Emits
const emit = defineEmits<{
  retry: [result: any]
  remove: [result: any]
  exportAll: [results: any[]]
}>()

// 响应式数据
const currentPage = ref(1)
const asrDialogVisible = ref(false)
const currentASRResult = ref<any>(null)
const activeASRTab = ref('text')

// 计算属性
const successCount = computed(() => {
  return props.results.filter(r => r.success).length
})

const failedCount = computed(() => {
  return props.results.filter(r => !r.success).length
})

const totalSize = computed(() => {
  return props.results
    .filter(r => r.success && r.size)
    .reduce((sum, r) => sum + r.size, 0)
})

const totalDuration = computed(() => {
  return props.results
    .filter(r => r.success && r.duration)
    .reduce((sum, r) => sum + r.duration, 0)
})

const displayResults = computed(() => {
  if (!props.showPagination) return props.results

  const start = (currentPage.value - 1) * props.pageSize
  const end = start + props.pageSize
  return props.results.slice(start, end)
})

// 事件处理
const openResult = async (result: any) => {
  try {
    await window.electronAPI.app.showItemInFolder(result.outputPath)
  } catch (error: any) {
    ElMessage.error(`打开文件失败: ${error.message}`)
  }
}

const copyPath = async (path: string) => {
  try {
    await window.electronAPI.app.clipboardWriteText(path)
    ElMessage.success('路径已复制到剪贴板')
  } catch (error: any) {
    ElMessage.error(`复制失败: ${error.message}`)
  }
}

const copyText = async (text: string) => {
  try {
    await window.electronAPI.app.clipboardWriteText(text)
    ElMessage.success('文本已复制到剪贴板')
  } catch (error: any) {
    ElMessage.error(`复制失败: ${error.message}`)
  }
}

const viewASRResult = (result: any) => {
  currentASRResult.value = result
  activeASRTab.value = 'text'
  asrDialogVisible.value = true
}

const closeASRDialog = () => {
  asrDialogVisible.value = false
  currentASRResult.value = null
}

const saveTextToFile = async (content: string, extension: string) => {
  try {
    const result = await window.electronAPI.app.showSaveDialog({
      title: '保存文件',
      defaultPath: `result.${extension}`,
      filters: [
        { name: `${extension.toUpperCase()} 文件`, extensions: [extension] }
      ]
    })

    if (!result.canceled && result.filePath) {
      // 这里需要调用保存文件的API
      ElMessage.success('文件保存成功')
    }
  } catch (error: any) {
    ElMessage.error(`保存失败: ${error.message}`)
  }
}

const retryResult = (result: any) => {
  emit('retry', result)
}

const removeResult = async (result: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要移除 "${result.fileName}" 的结果吗？`,
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    emit('remove', result)
  } catch {
    // 用户取消
  }
}

const exportAllResults = () => {
  emit('exportAll', props.results.filter(r => r.success))
}

const openAllResults = async () => {
  const successResults = props.results.filter(r => r.success && r.outputPath)

  if (successResults.length === 0) {
    ElMessage.warning('没有可打开的文件')
    return
  }

  try {
    for (const result of successResults.slice(0, 10)) { // 限制同时打开的文件数量
      await window.electronAPI.app.showItemInFolder(result.outputPath)
    }

    if (successResults.length > 10) {
      ElMessage.info(`已打开前10个文件，共有${successResults.length}个文件`)
    } else {
      ElMessage.success(`已打开${successResults.length}个文件`)
    }
  } catch (error: any) {
    ElMessage.error(`打开文件失败: ${error.message}`)
  }
}

const copyAllPaths = async () => {
  const successResults = props.results.filter(r => r.success && r.outputPath)

  if (successResults.length === 0) {
    ElMessage.warning('没有可复制的路径')
    return
  }

  const paths = successResults.map(r => r.outputPath).join('\n')
  await copyPath(paths)
}

// 工具方法
const getResultIcon = (result: any): string => {
  if (!result.success) return 'mdi:alert-circle'

  const icons = {
    'video-convert': 'mdi:video-check',
    'audio-extract': 'mdi:music-note-outline',
    'asr': 'mdi:microphone-outline',
    'image-process': 'mdi:image-check'
  }

  return icons[result.type] || 'mdi:check-circle'
}

const getResultIconClass = (result: any): string => {
  return result.success ? 'success-icon' : 'error-icon'
}

const getFileName = (filePath: string): string => {
  return filePath.split(/[\\/]/).pop() || filePath
}

const getTextPreview = (text: string): string => {
  const maxLength = 100
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return (bytes / Math.pow(k, i)).toFixed(1) + ' ' + sizes[i]
}

const formatDuration = (seconds: number): string => {
  if (seconds < 60) return `${seconds}秒`
  if (seconds < 3600) return `${Math.floor(seconds / 60)}分${seconds % 60}秒`
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  return `${hours}时${minutes}分`
}

const formatTime = (timestamp: number): string => {
  return new Date(timestamp).toLocaleString('zh-CN')
}
</script>

<style scoped lang="scss">
@use '@/assets/styles/index.scss' as *;

.task-result-panel {
  .result-overview {
    margin-bottom: $spacing-base;
    padding: $spacing-base;
    background: $background-light;
    border-radius: $border-radius-base;

    .overview-stats {
      display: flex;
      gap: $spacing-base;
      margin-bottom: $spacing-base;
      flex-wrap: wrap;

      .stat-item {
        display: flex;
        align-items: center;
        gap: $spacing-small;
        font-size: 13px;

        .stat-label {
          color: $text-secondary;
        }

        .stat-value {
          font-weight: 600;
          color: $text-primary;
        }

        .success-icon {
          color: $success-color;
        }

        .error-icon {
          color: $danger-color;
        }
      }
    }

    .batch-actions {
      display: flex;
      gap: $spacing-small;
      justify-content: center;
    }
  }

  .result-list {
    .result-item {
      display: flex;
      align-items: flex-start;
      padding: $spacing-base;
      border: 1px solid $border-lighter;
      border-radius: $border-radius-base;
      margin-bottom: $spacing-small;
      background: $background-light;
      transition: $transition-base;

      &:hover {
        background: $background-card;
        border-color: $border-light;
      }

      &.result-error {
        border-color: $danger-color;
        background: #fef0f0;
      }

      .result-info {
        display: flex;
        flex: 1;

        .result-icon {
          margin-right: $spacing-base;
          font-size: 18px;
          margin-top: 2px;

          .success-icon {
            color: $success-color;
          }

          .error-icon {
            color: $danger-color;
          }
        }

        .result-details {
          flex: 1;

          .result-title {
            display: flex;
            align-items: center;
            gap: $spacing-small;
            margin-bottom: 4px;

            .file-name {
              font-weight: 500;
              color: $text-primary;
            }
          }

          .result-meta {
            display: flex;
            gap: $spacing-base;
            flex-wrap: wrap;
            font-size: 12px;
            margin-bottom: 4px;

            .meta-item {
              display: flex;
              align-items: center;
              gap: 4px;
              color: $text-secondary;
            }
          }

          .result-error-info {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: $danger-color;
            background: #fff2f0;
            padding: 4px 8px;
            border-radius: 4px;
            margin-bottom: 4px;
          }

          .asr-result-preview {
            margin-top: $spacing-small;

            .text-preview {
              margin-bottom: $spacing-small;

              .preview-label {
                font-size: 12px;
                color: $text-secondary;
                margin-bottom: 4px;
              }

              .preview-content {
                font-size: 13px;
                color: $text-regular;
                background: $background-card;
                padding: $spacing-small;
                border-radius: 4px;
                line-height: 1.5;
              }
            }

            .asr-stats {
              display: flex;
              gap: $spacing-small;
              flex-wrap: wrap;

              .stat-chip {
                display: flex;
                align-items: center;
                gap: 4px;
                font-size: 11px;
                background: #f0f5ff;
                color: $primary-color;
                padding: 2px 6px;
                border-radius: 12px;
              }
            }
          }
        }
      }

      .result-actions {
        display: flex;
        gap: $spacing-small;
        flex-shrink: 0;
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: $spacing-xl;
    color: $text-secondary;

    .empty-icon {
      font-size: 64px;
      margin-bottom: $spacing-base;
      color: $border-light;
    }

    .empty-text {
      font-size: 16px;
      margin-bottom: $spacing-small;
    }

    .empty-hint {
      font-size: 14px;
      opacity: 0.8;
    }
  }

  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: $spacing-base;
  }
}

// ASR Dialog样式
.asr-dialog-content {
  .asr-content {
    .content-actions {
      display: flex;
      gap: $spacing-small;
      margin-bottom: $spacing-base;
    }

    .asr-textarea {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 14px;
      line-height: 1.6;
    }
  }

  .asr-stats-detail {
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: $spacing-base;

      .stat-card {
        display: flex;
        align-items: center;
        gap: $spacing-base;
        padding: $spacing-base;
        background: $background-light;
        border-radius: $border-radius-base;

        .stat-icon {
          font-size: 24px;
          color: $primary-color;
        }

        .stat-info {
          .stat-label {
            font-size: 12px;
            color: $text-secondary;
            margin-bottom: 4px;
          }

          .stat-value {
            font-size: 16px;
            font-weight: 600;
            color: $text-primary;
          }
        }
      }
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .task-result-panel {
    .result-overview {
      .overview-stats {
        flex-direction: column;
        align-items: stretch;

        .stat-item {
          justify-content: space-between;
        }
      }

      .batch-actions {
        flex-direction: column;
      }
    }

    .result-list {
      .result-item {
        flex-direction: column;
        gap: $spacing-base;

        .result-actions {
          justify-content: center;
        }
      }
    }
  }
}
</style>