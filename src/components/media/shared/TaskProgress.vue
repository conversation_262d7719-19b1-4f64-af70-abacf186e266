<template>
  <div class="task-progress">
    <div class="task-progress-item" 
         v-for="task in displayTasks" 
         :key="task.id"
         :class="{ 'task-error': task.status === 'error' }">
      
      <!-- 任务信息 -->
      <div class="task-info">
        <div class="task-icon">
          <Icon :icon="getFileTypeIcon(task.fileName)" class="file-type-icon" />
          <Icon :icon="getTaskStatusIcon(task.status)" :class="getTaskStatusClass(task.status)" />
        </div>
        
        <div class="task-details">
          <div class="task-name">{{ task.fileName }}</div>
          <div class="task-meta">
            <el-tag :type="getTaskStatusType(task.status)" size="small">
              {{ getTaskStatusLabel(task.status) }}
            </el-tag>
            <span class="task-type" v-if="showTaskType">{{ getTaskTypeLabel(task.type) }}</span>
            <span class="task-step" v-if="task.currentStep">{{ task.currentStep }}</span>
            <span class="task-duration" v-if="task.estimatedTime && task.status === 'processing'">
              预计: {{ formatTime(task.estimatedTime) }}
            </span>
            <span class="task-elapsed" v-if="task.startTime && task.status === 'processing'">
              已用: {{ formatElapsedTime(task.startTime) }}
            </span>
          </div>
          
          <!-- 错误信息 -->
          <div class="task-error-message" v-if="task.error">
            <Icon icon="mdi:alert-circle" />
            {{ task.error }}
          </div>
          
          <!-- 任务结果信息 -->
          <div class="task-result-info" v-if="task.status === 'completed' && task.result && showResultInfo">
            <div class="result-item" v-if="task.result.outputPath">
              <Icon icon="mdi:file-check" />
              <span>输出: {{ getFileName(task.result.outputPath) }}</span>
            </div>
            <div class="result-item" v-if="task.result.size">
              <Icon icon="mdi:harddisk" />
              <span>大小: {{ formatFileSize(task.result.size) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 进度条 -->
      <div class="task-progress-bar">
        <el-progress 
          :percentage="task.progress" 
          :status="getProgressStatus(task.status)"
          :stroke-width="strokeWidth"
          :show-text="showProgressText"
        />
        <div class="progress-text" v-if="!showProgressText">
          {{ task.progress }}%
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="task-actions">
        <el-button 
          size="small" 
          v-if="task.status === 'error' && allowRetry"
          @click="handleRetry(task)"
        >
          <Icon icon="mdi:refresh" />
          重试
        </el-button>
        
        <el-button 
          size="small" 
          v-if="task.status === 'processing' && allowPause"
          @click="handlePause(task)"
        >
          <Icon icon="mdi:pause" />
          暂停
        </el-button>
        
        <el-button 
          size="small" 
          v-if="task.status === 'paused' && allowResume"
          @click="handleResume(task)"
        >
          <Icon icon="mdi:play" />
          继续
        </el-button>
        
        <el-button 
          size="small" 
          v-if="task.status === 'completed' && allowViewResult"
          @click="handleViewResult(task)"
        >
          <Icon icon="mdi:text-box-outline" v-if="task.type === 'asr'" />
          <Icon icon="mdi:folder-open-outline" v-else />
          {{ task.type === 'asr' ? '查看结果' : '打开文件' }}
        </el-button>
        
        <el-button 
          size="small" 
          type="danger" 
          v-if="allowRemove && !['processing'].includes(task.status)"
          @click="handleRemove(task)"
        >
          <Icon icon="mdi:close" />
          移除
        </el-button>
      </div>
    </div>

    <!-- 空状态 -->
    <div class="empty-state" v-if="displayTasks.length === 0">
      <Icon icon="mdi:clipboard-list-outline" class="empty-icon" />
      <div class="empty-text">{{ emptyText }}</div>
    </div>

    <!-- 统计信息 -->
    <div class="progress-summary" v-if="displayTasks.length > 0 && showSummary">
      <div class="summary-stats">
        <div class="stat-item">
          <span class="stat-label">总数:</span>
          <span class="stat-value">{{ displayTasks.length }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">已完成:</span>
          <span class="stat-value success">{{ completedCount }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">处理中:</span>
          <span class="stat-value processing">{{ processingCount }}</span>
        </div>
        <div class="stat-item" v-if="failedCount > 0">
          <span class="stat-label">失败:</span>
          <span class="stat-value error">{{ failedCount }}</span>
        </div>
      </div>
      
      <div class="overall-progress">
        <el-progress 
          :percentage="overallProgress" 
          :stroke-width="6"
          :status="overallProgressStatus"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Icon } from '@iconify/vue'

// Props
interface Props {
  tasks: any[]
  allowRetry?: boolean
  allowPause?: boolean
  allowResume?: boolean
  allowViewResult?: boolean
  allowRemove?: boolean
  showTaskType?: boolean
  showResultInfo?: boolean
  showSummary?: boolean
  showProgressText?: boolean
  strokeWidth?: number
  emptyText?: string
  maxDisplayTasks?: number
}

const props = withDefaults(defineProps<Props>(), {
  allowRetry: true,
  allowPause: true,
  allowResume: true,
  allowViewResult: true,
  allowRemove: true,
  showTaskType: false,
  showResultInfo: true,
  showSummary: true,
  showProgressText: false,
  strokeWidth: 8,
  emptyText: '暂无任务',
  maxDisplayTasks: 0
})

// Emits
const emit = defineEmits<{
  retry: [task: any]
  pause: [task: any]
  resume: [task: any]
  viewResult: [task: any]
  remove: [task: any]
}>()

// 计算属性
const displayTasks = computed(() => {
  if (props.maxDisplayTasks > 0) {
    return props.tasks.slice(0, props.maxDisplayTasks)
  }
  return props.tasks
})

const completedCount = computed(() => {
  return displayTasks.value.filter(task => task.status === 'completed').length
})

const processingCount = computed(() => {
  return displayTasks.value.filter(task => ['processing', 'pending'].includes(task.status)).length
})

const failedCount = computed(() => {
  return displayTasks.value.filter(task => task.status === 'error').length
})

const overallProgress = computed(() => {
  if (displayTasks.value.length === 0) return 0
  const totalProgress = displayTasks.value.reduce((sum, task) => sum + task.progress, 0)
  return Math.floor(totalProgress / displayTasks.value.length)
})

const overallProgressStatus = computed(() => {
  if (failedCount.value > 0) return 'exception'
  if (completedCount.value === displayTasks.value.length && displayTasks.value.length > 0) return 'success'
  return undefined
})

// 事件处理
const handleRetry = (task: any) => {
  emit('retry', task)
}

const handlePause = (task: any) => {
  emit('pause', task)
}

const handleResume = (task: any) => {
  emit('resume', task)
}

const handleViewResult = (task: any) => {
  emit('viewResult', task)
}

const handleRemove = (task: any) => {
  emit('remove', task)
}

// 工具方法
const getTaskStatusIcon = (status: string): string => {
  const icons = {
    pending: 'mdi:clock-outline',
    processing: 'mdi:loading',
    paused: 'mdi:pause-circle',
    completed: 'mdi:check-circle',
    error: 'mdi:alert-circle'
  }
  return icons[status] || 'mdi:help-circle'
}

const getTaskStatusClass = (status: string): string => {
  const classes = {
    pending: 'status-pending',
    processing: 'status-processing',
    paused: 'status-paused',
    completed: 'status-success',
    error: 'status-error'
  }
  return classes[status] || ''
}

const getTaskStatusType = (status: string): any => {
  const types = {
    pending: 'info',
    processing: 'warning',
    paused: 'info',
    completed: 'success',
    error: 'danger'
  }
  return types[status] || 'info'
}

const getTaskStatusLabel = (status: string): string => {
  const labels = {
    pending: '等待中',
    processing: '处理中',
    paused: '已暂停',
    completed: '已完成',
    error: '失败'
  }
  return labels[status] || status
}

const getTaskTypeLabel = (type: string): string => {
  const labels = {
    'video-convert': '视频转换',
    'audio-extract': '音频提取',
    'asr': '语音识别',
    'image-process': '图片处理'
  }
  return labels[type] || type
}

const getFileTypeIcon = (fileName: string): string => {
  const ext = fileName.split('.').pop()?.toLowerCase()
  const videoFormats = ['mp4', 'avi', 'mov', 'mkv', 'flv', 'wmv']
  const audioFormats = ['mp3', 'wav', 'm4a', 'aac', 'flac', 'ogg']
  const imageFormats = ['jpg', 'jpeg', 'png', 'webp', 'bmp', 'gif']
  
  if (videoFormats.includes(ext || '')) {
    return 'mdi:video'
  } else if (audioFormats.includes(ext || '')) {
    return 'mdi:music'
  } else if (imageFormats.includes(ext || '')) {
    return 'mdi:image'
  }
  return 'mdi:file'
}

const getProgressStatus = (status: string) => {
  if (status === 'error') return 'exception'
  if (status === 'completed') return 'success'
  return undefined
}

const formatTime = (seconds: number): string => {
  if (seconds < 60) return `${seconds}秒`
  if (seconds < 3600) return `${Math.floor(seconds / 60)}分钟`
  return `${Math.floor(seconds / 3600)}小时`
}

const formatElapsedTime = (startTime: number): string => {
  const elapsed = Math.floor((Date.now() - startTime) / 1000)
  return formatTime(elapsed)
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return (bytes / Math.pow(k, i)).toFixed(1) + ' ' + sizes[i]
}

const getFileName = (filePath: string): string => {
  return filePath.split(/[\\/]/).pop() || filePath
}
</script>

<style scoped lang="scss">
@use '@/assets/styles/index.scss' as *;

.task-progress {
  .task-progress-item {
    display: flex;
    align-items: center;
    padding: $spacing-base;
    border: 1px solid $border-lighter;
    border-radius: $border-radius-base;
    margin-bottom: $spacing-small;
    background: $background-light;
    transition: $transition-base;

    &:hover {
      background: $background-card;
      border-color: $border-light;
    }

    &.task-error {
      border-color: $danger-color;
      background: #fef0f0;
    }

    .task-info {
      display: flex;
      align-items: flex-start;
      flex: 1;

      .task-icon {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: $spacing-base;
        gap: 4px;

        .file-type-icon {
          font-size: 16px;
          color: $text-secondary;
        }

        .status-pending { color: $info-color; }
        .status-processing { 
          color: $primary-color; 
          animation: spin 2s linear infinite;
        }
        .status-paused { color: $warning-color; }
        .status-success { color: $success-color; }
        .status-error { color: $danger-color; }
      }

      .task-details {
        flex: 1;

        .task-name {
          font-weight: 500;
          color: $text-primary;
          margin-bottom: 4px;
        }

        .task-meta {
          display: flex;
          gap: $spacing-small;
          align-items: center;
          flex-wrap: wrap;
          font-size: 12px;
          margin-bottom: 4px;

          .task-type {
            color: $text-secondary;
          }

          .task-step {
            color: $primary-color;
            font-style: italic;
          }

          .task-duration,
          .task-elapsed {
            color: $text-placeholder;
          }
        }

        .task-error-message {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          color: $danger-color;
          background: #fff2f0;
          padding: 4px 8px;
          border-radius: 4px;
          margin-bottom: 4px;
        }

        .task-result-info {
          display: flex;
          gap: $spacing-small;
          font-size: 12px;
          
          .result-item {
            display: flex;
            align-items: center;
            gap: 4px;
            color: $success-color;
            background: #f6ffed;
            padding: 2px 6px;
            border-radius: 4px;
          }
        }
      }
    }

    .task-progress-bar {
      flex: 2;
      margin: 0 $spacing-base;

      .progress-text {
        text-align: center;
        font-size: 12px;
        color: $text-secondary;
        margin-top: 4px;
      }
    }

    .task-actions {
      display: flex;
      gap: $spacing-small;
      flex-shrink: 0;
    }
  }

  .empty-state {
    text-align: center;
    padding: $spacing-xl;
    color: $text-secondary;

    .empty-icon {
      font-size: 48px;
      margin-bottom: $spacing-base;
      color: $border-light;
    }

    .empty-text {
      font-size: 14px;
    }
  }

  .progress-summary {
    margin-top: $spacing-base;
    padding: $spacing-base;
    background: $background-light;
    border-radius: $border-radius-base;

    .summary-stats {
      display: flex;
      gap: $spacing-base;
      margin-bottom: $spacing-base;
      justify-content: center;

      .stat-item {
        display: flex;
        gap: $spacing-small;
        font-size: 12px;

        .stat-label {
          color: $text-secondary;
        }

        .stat-value {
          font-weight: 600;

          &.success { color: $success-color; }
          &.processing { color: $primary-color; }
          &.error { color: $danger-color; }
        }
      }
    }

    .overall-progress {
      text-align: center;
    }
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>