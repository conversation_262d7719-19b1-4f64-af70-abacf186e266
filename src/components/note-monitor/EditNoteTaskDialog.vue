<template>
    <el-dialog v-model="dialogVisible" title="编辑监控任务" width="500px" :close-on-click-modal="false">
        <el-form v-if="formReady" :model="editForm" label-width="120px" class="edit-form">
            <el-form-item label="笔记链接/ID">
                <el-input v-model="editForm.noteId" placeholder="请输入小红书笔记链接或ID" disabled></el-input>
            </el-form-item>
            <el-form-item label="任务名称">
                <el-input v-model="editForm.name" placeholder="请输入任务名称"></el-input>
            </el-form-item>
            <el-form-item label="监控频率">
                <el-select v-model="editForm.frequency" placeholder="选择监控频率" class="frequency-select">
                    <el-option label="每小时" :value="60"></el-option>
                    <el-option label="每天" :value="1440"></el-option>
                    <el-option label="每周" :value="10080"></el-option>
                </el-select>
            </el-form-item>
        </el-form>
        <template #footer>
            <span>
                <el-button @click="closeDialog">取消</el-button>
                <el-button type="primary" @click="saveTaskEdit" :loading="isEditLoading">保存</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineProps, defineEmits, watch } from 'vue';
import { ElMessage } from 'element-plus';

// 定义任务类型接口
interface NoteTask {
    id: string;
    name: string;
    noteId: string;
    frequency: number;
    status: 'running' | 'paused' | 'stopped';
    lastRunTime: number;
    coverUrl?: string;
    title?: string;
    authorAvatar?: string;
    authorName?: string;
}

// 接收父组件传递的属性
const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false
    },
    task: {
        type: Object as () => NoteTask | Record<string, any> | undefined,
        default: undefined
    }
});

// 定义组件暴露的事件
const emit = defineEmits(['update:modelValue', 'task-updated']);

// 计算属性，处理v-model
const dialogVisible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
});

// 获取ipcRenderer
const ipcRenderer = (window as any).ipcRenderer;

// 本地状态
const isEditLoading = ref(false);
const formReady = ref(false);
const editForm = reactive({
    id: '',
    name: '',
    noteId: '',
    frequency: 0,
});

// 监听task变化
watch(() => props.task, (newTask) => {
    if (newTask) {
        editForm.id = newTask.id;
        editForm.name = newTask.name;
        editForm.noteId = newTask.noteId;
        editForm.frequency = newTask.frequency;
        formReady.value = true;
    } else {
        formReady.value = false;
    }
}, { immediate: true });

// 关闭对话框
const closeDialog = () => {
    dialogVisible.value = false;
};

// 保存编辑
const saveTaskEdit = async () => {
    if (!editForm.name.trim()) {
        ElMessage.warning('任务名称不能为空');
        return;
    }

    isEditLoading.value = true;
    try {
        const result = await ipcRenderer.invoke('monitor:update-task', {
            id: editForm.id,
            name: editForm.name,
            frequency: editForm.frequency,
        });

        if (result.success) {
            ElMessage.success('任务更新成功');
            closeDialog();
            emit('task-updated');
        } else {
            ElMessage.error(`更新任务失败: ${result.message}`);
        }
    } catch (error) {
        ElMessage.error('更新任务失败');
        console.error('更新笔记监控任务失败:', error);
    } finally {
        isEditLoading.value = false;
    }
};
</script>

<style scoped>
.frequency-select {
    width: 100%;
}

.edit-form {
    margin: 20px 0;
}
</style>