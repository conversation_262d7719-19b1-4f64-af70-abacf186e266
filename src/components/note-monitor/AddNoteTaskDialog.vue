<template>
    <el-dialog v-model="dialogVisible" title="添加笔记监控任务" width="500px" :before-close="handleDialogClose">
        <el-form :model="taskForm" :rules="taskFormRules" ref="taskFormRef" label-width="100px">
            <el-form-item label="笔记链接" prop="noteUrl">
                <el-input v-model="taskForm.noteUrl" placeholder="请输入小红书笔记链接或ID" clearable>
                </el-input>
                <div class="form-tip">支持小红书笔记分享链接或笔记ID</div>
            </el-form-item>
            <el-form-item label="监控频率" prop="frequency">
                <el-select v-model="taskForm.frequency" placeholder="请选择监控频率" style="width: 100%">
                    <el-option label="每小时" :value="60"></el-option>
                    <el-option label="每天" :value="1440"></el-option>
                    <el-option label="每周" :value="10080"></el-option>
                    <el-option label="每月" :value="43200"></el-option>
                </el-select>
            </el-form-item>
        </el-form>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="handleDialogClose">取消</el-button>
                <el-button type="primary" @click="addTask" :loading="isAddingTask">添加任务</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineProps, defineEmits } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import { v4 as uuidv4 } from 'uuid';

// 接收父组件传递的属性
const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false
    }
});

// 定义组件暴露的事件
const emit = defineEmits(['update:modelValue', 'task-added']);

// 计算属性，处理v-model
const dialogVisible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value)
});

// 获取ipcRenderer
const ipcRenderer = (window as any).ipcRenderer;

// 本地状态
const isAddingTask = ref(false);
const taskFormRef = ref<FormInstance>();
const taskForm = reactive({
    noteUrl: '',
    frequency: 1440, // 默认每天
});

// 表单验证规则
const taskFormRules: FormRules = {
    noteUrl: [
        { required: true, message: '请输入笔记链接或ID', trigger: 'blur' },
        { min: 1, max: 500, message: '链接长度在 1 到 500 个字符', trigger: 'blur' }
    ],
    frequency: [
        { required: true, message: '请选择监控频率', trigger: 'change' }
    ]
};

// 处理对话框关闭
const handleDialogClose = () => {
    dialogVisible.value = false;
    taskFormRef.value?.resetFields();
    resetForm();
};

// 重置表单
const resetForm = () => {
    taskForm.noteUrl = '';
    taskForm.frequency = 1440;
};

// 添加任务
const addTask = async () => {
    if (!taskFormRef.value) return;
    
    try {
        // 验证表单
        await taskFormRef.value.validate();
        
        isAddingTask.value = true;
        const result = await ipcRenderer.invoke('monitor:add-task', {
            id: uuidv4(),
            type: 'note',
            noteUrl: taskForm.noteUrl,
            frequency: taskForm.frequency,
        });

        if (result && result.success) {
            ElMessage.success('任务添加成功');
            handleDialogClose();
            emit('task-added');
        } else {
            const errorMessage = result && result.message ? result.message : '未知错误';
            ElMessage.error(`任务添加失败: ${errorMessage}`);
        }
    } catch (error) {
        ElMessage.error('添加任务失败');
        console.error('添加笔记监控任务失败:', error);
    } finally {
        isAddingTask.value = false;
    }
};
</script>

<style scoped>
.form-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 5px;
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}
</style>