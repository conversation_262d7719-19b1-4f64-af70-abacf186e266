<template>
    <el-card class="note-task-list-card">
        <template #header>
            <div class="card-header">
                <span>笔记监控任务列表</span>
            </div>
        </template>

        <el-table :data="tasks" style="width: 100%" v-loading="loading" @sort-change="handleSortChange">
            <!-- 笔记封面 -->
            <el-table-column label="笔记封面" width="80">
                <template #default="scope">
                    <el-image :src="scope.row.data?.coverUrl || defaultCover" 
                        style="width: 50px; height: 50px; border-radius: 6px;" fit="cover">
                        <template #error>
                            <div class="image-slot">
                                <el-icon><Picture /></el-icon>
                            </div>
                        </template>
                    </el-image>
                </template>
            </el-table-column>

            <!-- 笔记标题 -->
            <el-table-column label="笔记标题" min-width="200" sortable="custom">
                <template #default="scope">
                    <div class="note-title">{{ scope.row.data?.title || scope.row.name || '无标题' }}</div>
                </template>
            </el-table-column>

            <!-- 博主信息 -->
            <el-table-column label="博主" min-width="150">
                <template #default="scope">
                    <div class="author-info">
                        <el-avatar :size="40" :src="scope.row.data?.authorAvatar || defaultAvatar" 
                            shape="circle" fit="cover"></el-avatar>
                        <span class="author-name">{{ scope.row.data?.authorName || '未知博主' }}</span>
                    </div>
                </template>
            </el-table-column>

            <!-- 监控频率 -->
            <el-table-column label="监控频率" width="100" sortable="custom">
                <template #default="scope">
                    {{ formatFrequency(scope.row.frequency) }}
                </template>
            </el-table-column>

            <!-- 状态 -->
            <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                    <el-tag :type="getStatusTagType(scope.row.status)">
                        {{ getStatusText(scope.row.status) }}
                    </el-tag>
                </template>
            </el-table-column>

            <!-- 上次更新时间 -->
            <el-table-column prop="lastRunTime" label="上次更新" min-width="150" sortable="custom">
                <template #default="scope">
                    {{ formatDate(scope.row.lastRunTime) }}
                </template>
            </el-table-column>
            <!-- 操作 -->
            <el-table-column label="操作" width="320" fixed="right">
                <template #default="scope">
                    <div class="action-buttons">
                        <div class="button-row">
                            <el-button size="small" @click="pauseTask(scope.row.id)"
                                :disabled="scope.row.status === 'paused'">暂停</el-button>
                            <el-button size="small" @click="resumeTask(scope.row.id)"
                                :disabled="scope.row.status === 'running'">恢复</el-button>
                            <el-button size="small" type="primary" @click="editTask(scope.row)">编辑</el-button>
                        </div>
                        <div class="button-row">
                            <el-button size="small" type="info" @click="viewData(scope.row)">数据</el-button>
                            <el-popconfirm title="确认删除此监控任务?" @confirm="deleteTask(scope.row.id)">
                                <template #reference>
                                    <el-button size="small" type="danger">删除</el-button>
                                </template>
                            </el-popconfirm>
                        </div>
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </el-card>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits } from 'vue';
import { ElMessage } from 'element-plus';
import { Picture } from '@element-plus/icons-vue';

// 定义组件接收的属性
const props = defineProps({
    tasks: {
        type: Array,
        default: () => []
    }
});

// 定义组件发出的事件
const emit = defineEmits(['view-data', 'edit-task', 'refresh-tasks']);

// 获取ipcRenderer
const ipcRenderer = (window as any).ipcRenderer;

// 本地状态
const loading = ref(false);
const defaultAvatar = ''; // 默认头像
const defaultCover = ''; // 默认封面

/**
 * 格式化日期显示
 * @param timestamp 时间戳
 * @returns 格式化后的日期字符串
 */
const formatDate = (timestamp: number | undefined): string => {
    if (!timestamp) return '未运行';

    try {
        const date = new Date(timestamp);
        if (isNaN(date.getTime())) return '未知时间';

        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (error) {
        return '时间格式错误';
    }
};

/**
 * 格式化监控频率
 * @param frequency 频率（分钟）
 * @returns 格式化后的频率字符串
 */
const formatFrequency = (frequency: number): string => {
    if (frequency >= 1440) {
        return `${frequency / 1440}天`;
    } else if (frequency >= 60) {
        return `${frequency / 60}小时`;
    } else {
        return `${frequency}分钟`;
    }
};

/**
 * 获取状态对应的标签类型
 * @param status 任务状态
 * @returns 对应的Element Plus标签类型
 */
const getStatusTagType = (status: string): string => {
    switch (status) {
        case 'running':
            return 'success';
        case 'paused':
            return 'warning';
        case 'stopped':
            return 'info';
        default:
            return 'info';
    }
};

/**
 * 获取状态对应的中文文本
 * @param status 任务状态
 * @returns 状态的中文名称
 */
const getStatusText = (status: string): string => {
    switch (status) {
        case 'running':
            return '运行中';
        case 'paused':
            return '已暂停';
        case 'stopped':
            return '已停止';
        default:
            return '未知';
    }
};

/**
 * 查看数据面板
 * @param task 任务对象
 */
const viewData = (task: any) => {
    emit('view-data', task.id);
};

/**
 * 编辑任务
 * @param task 任务对象
 */
const editTask = (task: any) => {
    emit('edit-task', task);
};

/**
 * 暂停任务
 * @param id 任务ID
 */
const pauseTask = async (id: string) => {
    try {
        loading.value = true;
        const result = await ipcRenderer.invoke('monitor:pause-task', id);

        if (result && result.success) {
            ElMessage.success('任务已暂停');
            emit('refresh-tasks'); // 通知父组件刷新任务列表
        } else {
            const errorMessage = result && result.message ? result.message : '未知错误';
            ElMessage.error(`暂停任务失败: ${errorMessage}`);
        }
    } catch (error: any) {
        console.error('暂停任务失败:', error);
        ElMessage.error('暂停任务失败: ' + (error.message || '未知错误'));
    } finally {
        loading.value = false;
    }
};

/**
 * 恢复任务
 * @param id 任务ID
 */
const resumeTask = async (id: string) => {
    try {
        loading.value = true;
        const result = await ipcRenderer.invoke('monitor:resume-task', id);

        if (result && result.success) {
            ElMessage.success('任务已恢复');
            emit('refresh-tasks'); // 通知父组件刷新任务列表
        } else {
            const errorMessage = result && result.message ? result.message : '未知错误';
            ElMessage.error(`恢复任务失败: ${errorMessage}`);
        }
    } catch (error: any) {
        console.error('恢复任务失败:', error);
        ElMessage.error('恢复任务失败: ' + (error.message || '未知错误'));
    } finally {
        loading.value = false;
    }
};

/**
 * 删除任务
 * @param id 任务ID
 */
const deleteTask = async (id: string) => {
    try {
        loading.value = true;
        const result = await ipcRenderer.invoke('monitor:delete-task', id);

        if (result && result.success) {
            ElMessage.success('任务已删除');
            emit('refresh-tasks'); // 通知父组件刷新任务列表
        } else {
            const errorMessage = result && result.message ? result.message : '未知错误';
            ElMessage.error(`删除任务失败: ${errorMessage}`);
        }
    } catch (error: any) {
        console.error('删除任务失败:', error);
        ElMessage.error('删除任务失败: ' + (error.message || '未知错误'));
    } finally {
        loading.value = false;
    }
};

/**
 * 处理表格排序变化
 * @param param 排序参数
 */
const handleSortChange = (param: { column: any; prop: string; order: string }) => {
    // 实现本地排序，生产环境可以改为向服务端请求排序
    if (!param.prop || !param.order) return;

    const isAsc = param.order === 'ascending';

    props.tasks.sort((a: any, b: any) => {
        const valueA = a[param.prop];
        const valueB = b[param.prop];

        // 处理undefined或null
        if (valueA === undefined || valueA === null) return isAsc ? -1 : 1;
        if (valueB === undefined || valueB === null) return isAsc ? 1 : -1;

        // 数字比较
        if (typeof valueA === 'number' && typeof valueB === 'number') {
            return isAsc ? valueA - valueB : valueB - valueA;
        }

        // 字符串比较
        return isAsc
            ? String(valueA).localeCompare(String(valueB))
            : String(valueB).localeCompare(String(valueA));
    });
};
</script>

<style scoped lang="scss">
@use '../../assets/styles/global.scss' as *;

.note-task-list-card {
    margin-bottom: 20px;
    border-radius: $border-radius;
    box-shadow: $box-shadow;

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: $font-size-large;
        font-weight: bold;
        color: $primary-color;
    }

    .el-table {
        --el-table-header-bg-color: #f5f7fa;
        --el-table-row-hover-bg-color: #f0f2f5;

        .el-button {
            margin-right: 5px;

            &:last-child {
                margin-right: 0;
            }
        }
    }
}

.note-title {
    font-weight: 500;
    color: #303133;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.author-info {
    display: flex;
    align-items: center;
    gap: 10px;
    min-height: 50px; // 确保行高一致

    .el-avatar {
        flex-shrink: 0; // 防止头像被压缩
        width: 40px !important;
        height: 40px !important;
    }

    .author-name {
        font-size: 14px;
        color: #606266;
        line-height: 1.4;
        flex: 1;
        min-width: 0; // 允许文本截断
    }
}

.image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: #f5f7fa;
    color: #909399;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
    
    .button-row {
        display: flex;
        gap: 6px;
        flex-wrap: nowrap;
        
        .el-button {
            margin: 0;
            font-size: 12px;
            padding: 6px 12px;
            height: 28px;
            line-height: 1;
        }
    }
}
</style>