<template>
  <div class="blogger-monitor-container">
    <div class="header">
      <h2>博主监控</h2>
      <el-button type="primary" @click="showAddTaskDialog">批量添加监控任务</el-button>
    </div>

    <!-- 博主监控任务列表组件 -->
    <blogger-task-list :tasks="bloggerTasks" @view-works="viewBloggerWorks" @refresh-tasks="fetchBloggerTasks" />

    <!-- 博主作品列表组件，当选择了博主时显示 -->
    <blogger-works-list v-if="selectedBloggerId" :bloggerId="selectedBloggerId" :bloggerName="selectedBloggerName"
      @close="closeWorksList" />

    <!-- 批量添加监控任务弹框组件 -->
    <add-blogger-task-dialog v-model="addTaskDialogVisible" @task-added="handleTaskAdded" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
// 导入组件
import BloggerTaskList from '@/components/blogger-monitor/BloggerTaskList.vue';
import BloggerWorksList from '@/components/blogger-monitor/BloggerWorksList.vue';
import AddBloggerTaskDialog from '@/components/blogger-monitor/AddBloggerTaskDialog.vue';

// 导入 ipcRenderer
const ipcRenderer = (window as any).ipcRenderer;

// 状态管理
const bloggerTasks = ref<any[]>([]); // 博主监控任务列表
const selectedBloggerId = ref<string | null>(null); // 当前选中的博主ID
const selectedBloggerName = ref<string>(''); // 当前选中的博主名称
const addTaskDialogVisible = ref<boolean>(false); // 控制添加任务弹框的显示

// 保存事件监听器引用，以便后续移除
const taskUpdatedListener = (event: any, updatedTask: any) => {
  if (updatedTask && updatedTask.type === 'blogger') {
    const index = bloggerTasks.value.findIndex(task => task.id === updatedTask.id);
    if (index !== -1) {
      bloggerTasks.value[index] = updatedTask;
    } else {
      bloggerTasks.value.push(updatedTask);
    }
  }
};

// 获取所有博主监控任务
const fetchBloggerTasks = async () => {
  try {
    const tasks = await ipcRenderer.invoke('monitor:get-tasks');
    bloggerTasks.value = tasks.filter((task: any) => task.type === 'blogger');
  } catch (error) {
    ElMessage.error('获取任务列表失败');
    console.error('获取博主监控任务失败:', error);
  }
};

// 显示添加任务弹框
const showAddTaskDialog = () => {
  addTaskDialogVisible.value = true;
};

// 处理任务添加成功的回调
const handleTaskAdded = () => {
  fetchBloggerTasks(); // 刷新任务列表
};

// 查看博主作品
const viewBloggerWorks = (bloggerId: string, bloggerName: string) => {
  selectedBloggerId.value = bloggerId;
  selectedBloggerName.value = bloggerName;
};

// 关闭作品列表
const closeWorksList = () => {
  selectedBloggerId.value = null;
  selectedBloggerName.value = '';
};

onMounted(() => {
  fetchBloggerTasks();
  // 监听主进程推送的任务状态更新
  ipcRenderer.on('monitor:task-updated', taskUpdatedListener);
});

onUnmounted(() => {
  // 解除事件监听，传入相同的监听器函数引用
  ipcRenderer.off('monitor:task-updated', taskUpdatedListener);
});
</script>

<style scoped lang="scss">
@use '../assets/styles/global.scss' as *;

.blogger-monitor-container {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h2 {
      margin: 0;
      color: $primary-color;
    }
  }
}
</style>