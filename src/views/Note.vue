<template>
	<div class="page-container">
		<h1 class="page-title">笔记批量获取</h1>

		<!-- 搜索和控制区域 -->
		<div class="app-card search-card">
			<el-form :inline="true" class="search-form">
				<el-form-item label="关键词">
					<el-input v-model="searchKeyword" placeholder="请输入搜索关键词" clearable :prefix-icon="Search">
					</el-input>
				</el-form-item>

				<el-form-item label="排序方式">
					<el-select v-model="crawlType" placeholder="选择排序">
						<el-option label="最新" value="time_descending">
							<span>
								<Icon class="stat-icon" icon="fluent:new-16-filled" /> 最新
							</span>
						</el-option>
						<el-option label="热门" value="popularity_descending">
							<span>
								<Icon class="stat-icon" icon="bxs:hot" /> 热门
							</span>
						</el-option>
						<el-option label="综合" value="general">
							<span>
								<Icon class="stat-icon" icon="material-symbols:all-out" /> 综合
							</span>
						</el-option>
					</el-select>
				</el-form-item>

				<el-form-item label="抓取页数">
					<el-input-number v-model="pageCount" :min="1" :max="10" controls-position="right" size="default">
					</el-input-number>
				</el-form-item>
			</el-form>

			<div class="action-bar">
				<el-button type="primary" :icon="Download" :loading="isFetching" @click="startCrawling">
					{{ isFetching ? '抓取中...' : '开始抓取' }}
				</el-button>

				<el-button type="success" :icon="Folder" :disabled="items.length === 0" @click="saveToDir">
					保存至文件夹
				</el-button>

				<el-button type="info" :icon="Document" :disabled="items.length === 0" @click="exportToExcel">
					导出Excel
				</el-button>

				<el-button type="danger" :icon="Delete" :disabled="items.length === 0" @click="confirmReset">
					清空数据
				</el-button>
			</div>

			<!-- 进度条 -->
			<div v-if="isFetching" class="progress-area">
				<el-progress :percentage="progress" :stroke-width="16" :show-text="false" status="success">
				</el-progress>

				<div class="progress-info">
					<span class="progress-text">
						正在抓取第 {{ Math.ceil(progress / (100 / pageCount)) }} 页，共 {{ pageCount }} 页
					</span>
					<span class="progress-percentage">{{ progress.toFixed(0) }}%</span>
				</div>
			</div>
		</div>

		<!-- 笔记列表 -->
		<div v-if="items.length > 0" class="results-section">
			<div class="section-header">
				<h2 class="section-title">
					<el-icon>
						<Document />
					</el-icon> 笔记列表
					<span class="count-badge">{{ items.length }}</span>
				</h2>
			</div>

			<div class="note-list">
				<el-card v-for="item in items" :key="item.id" shadow="hover" class="note-card" @click="openItem(item)">
					<div class="note-header">
						<div class="user-info">
							<el-avatar :size="36" :src="item.note_card.user?.avatar"></el-avatar>
							<span class="username">{{ item.note_card.user?.nickname }}</span>
						</div>

						<div class="note-actions">
							<el-tooltip content="在小红书中查看" placement="top">
								<el-button link :icon="View" @click.stop="openInBrowser(item)">
								</el-button>
							</el-tooltip>

							<el-tooltip content="复制链接" placement="top">
								<el-button link :icon="Link" @click.stop="copyLink(item)">
								</el-button>
							</el-tooltip>

							<el-tooltip content="分析详情" placement="top">
								<el-button link :icon="DataAnalysis" @click.stop="analyzeNote(item)">
								</el-button>
							</el-tooltip>
						</div>
					</div>

					<div class="note-content">
						<!-- 图片区域 -->
						<div class="note-image">
							<!-- 点击查看多图 -->
							<el-image @click.stop class="thumbnail" :src="item.note_card.cover?.url_pre" fit="cover"
								:preview-src-list="getNoteImages(item)" :preview-teleported="true">
								<template #error>
									<div class="image-error">
										<el-icon>
											<Picture />
										</el-icon>
									</div>
								</template>
							</el-image>

							<div class="image-tag">
								<el-icon :class="getTypeIcon(item.note_card)"></el-icon>
								{{ getType(item.note_card) }}
							</div>
						</div>

						<!-- 标题区域 - 移到图片下方 -->
						<h3 class="note-title" :title="item.note_card.display_title">
							{{ item.note_card.display_title }}
						</h3>

						<!-- 互动数据统计 -->
						<div class="note-stats">
							<div class="stat-item">
								<Icon class="stat-icon like-icon" icon="mdi:heart" />
								<span>{{ formatCount(Number(item.note_card.interact_info?.liked_count)) }}</span>
							</div>

							<div class="stat-item">
								<Icon class="stat-icon collect-icon" icon="tabler:star-filled" />
								<span>{{ formatCount(Number(item.note_card.interact_info?.collected_count || 0))
								}}</span>
							</div>

							<div class="stat-item">
								<Icon class="stat-icon comment-icon" icon="mdi:comment" />
								<span>{{ formatCount(Number(item.note_card.interact_info?.comment_count || 0)) }}</span>
							</div>
						</div>
					</div>
				</el-card>
			</div>

			<!-- 无数据状态 -->
			<div v-if="items.length === 0 && !isFetching" class="empty-state">
				<el-icon>
					<Search />
				</el-icon>
				<p>请输入关键词并点击"开始抓取"按钮</p>
			</div>
		</div>
	</div>
</template>

<script lang='ts' setup>
import { ref, onMounted, onBeforeUnmount } from "vue";
import { useRouter } from 'vue-router';
import { Icon } from "@iconify/vue";
import { ElMessage, ElMessageBox, ElIcon } from 'element-plus';
import { Search, Download, Folder, Document, Delete, View, Link, DataAnalysis, Picture } from '@element-plus/icons-vue'; // Removed PictureOutline
import type { XhsNoteItem } from "../../electron/main/api/models/note";

const router = useRouter();

const searchKeyword = ref("");
const pageCount = ref(1);
const crawlType = ref("time_descending");
const crawlResults = ref<any[]>([]);
const progress = ref(0);
const isFetching = ref(false);
const items = ref<XhsNoteItem[]>([]);
const downloadDir = ref("");

onMounted(() => {
	const cachedNoteList = localStorage.getItem("cachedNoteList");
	if (cachedNoteList) {
		items.value = JSON.parse(cachedNoteList);
	}
	const searchKeywordCache = localStorage.getItem("searchKeyword");
	if (searchKeywordCache) {
		searchKeyword.value = searchKeywordCache;
	}
});

onBeforeUnmount(() => {
	localStorage.setItem("searchKeyword", searchKeyword.value);
});

const reset = () => {
	items.value = [];
	localStorage.setItem("cachedNoteList", JSON.stringify(items.value));
	ElMessage.success("已清空笔记列表");
};

const confirmReset = () => {
	ElMessageBox.confirm('确定要清空所有笔记数据吗?', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	}).then(() => {
		reset();
	}).catch(() => {
		// 取消操作
	});
};

// 获取笔记中的图片列表
const getNoteImages = (item: any): string[] => {
	// 如果笔记是视频，返回封面图
	if (item.note_card.type === "video") {
		return [item.note_card.cover.url_pre];
	} else {
		return item.note_card.image_list.map((image: any) => image.info_list[0]?.url);
	}
}

// 拼接笔记link
const getNoteLink = (item: any): string => {
	return `https://www.xiaohongshu.com/discovery/item/${item.id}?source=webshare&xhsshare=pc_web&xsec_token=${item.xsec_token}&xsec_source=pc_share`;
};

const copyLink = async (item: any) => {
	// 拼接为完整的小红书笔记链接
	const url = getNoteLink(item);
	await window.electronAPI.app.clipboardWriteText(url);
	ElMessage({
		message: "链接已复制到剪贴板",
		type: "success",
		duration: 1500
	});
};

const analyzeNote = (item: any) => {
	const url = getNoteLink(item);
	router.push({
		name: "Home",
		query: { noteUrl: url }
	});
};

const openInBrowser = (item: any) => {
	const url = getNoteLink(item);
	window.electronAPI.app.showLoginWindow(url);
};

const showLargeImage = (url?: string) => {
	// 该函数不再需要，使用Element Plus的图片预览功能代替
	// 保留函数避免调整模板代码
};

const getType = (noteCard: XhsNoteItem['note_card']): string => { // Updated type
	if (noteCard.type === "video") {
		return "视频";
	} else {
		const imageCount = noteCard.image_list ? noteCard.image_list.length : 0;
		return imageCount > 1 ? `${imageCount}图` : "单图";
	}
};

const getExportType = (noteCard: XhsNoteItem['note_card']): string => { // Updated type
	console.log('getExportType');
	if (noteCard.type === "video") {
		return "视频";
	} else {
		return "图片";
	}
};

const getTypeIcon = (noteCard: XhsNoteItem['note_card']): string => { // Updated type
	if (noteCard.type === "video") {
		return "VideoCamera"; // Using Element Plus icon name
	} else {
		const imageCount = noteCard.image_list ? noteCard.image_list.length : 0;
		return "Picture"; // Using Element Plus icon name for both single and multiple images
	}
};

const formatCount = (count: number): string => {
	if (count >= 10000) {
		return (count / 10000).toFixed(1) + 'w';
	} else if (count >= 1000) {
		return (count / 1000).toFixed(1) + 'k';
	}
	return count.toString();
};

const getNoteDesc = (noteCard: XhsNoteItem['note_card']): string => { // Updated type
	// Assuming description is in `desc` or similar field
	return (noteCard as any).desc?.toString() || ''; // Using type assertion and optional chaining
};

const formatDesc = (desc: string): string => {
	// Basic formatting, can be expanded
	return desc.substring(0, 100) + (desc.length > 100 ? '...' : '');
};


const openItem = (item: any) => {
	const url = getNoteLink(item);
	window.electronAPI.app.showLoginWindow(url);
};


const saveToDir = async () => {
	if (items.value.length === 0) {
		ElMessage.warning("没有可保存的数据");
		return;
	}

	try {
		const dirs = await window.electronAPI.app.openDialog({
			properties: ["openDirectory"],
		});

		if (dirs && dirs.length > 0) {
			downloadDir.value = `${dirs[0]}/${searchKeyword.value}`;

			await window.electronAPI.xhs.notesSave(JSON.parse(JSON.stringify({
				dir: `${downloadDir.value}`,
				notes: items.value,
			})));

			ElMessage({
				message: "数据已保存到文件夹",
				type: "success",
			});

			window.electronAPI.app.showItemInFolder(downloadDir.value);
		}
	} catch (error: any) {
		console.error("保存失败:", error);
		ElMessage.error(`保存失败: ${error.message}`);
	}
};

const exportToExcel = async () => {
	if (items.value.length === 0) {
		ElMessage({
			message: "没有数据可导出",
			type: "warning",
		});
		return;
	}

	const excelData = items.value.map(item => ({
		标题: item.note_card.display_title,
		作者: item.note_card.user?.nickname,
		点赞数: item.note_card.interact_info?.liked_count,
		收藏数: item.note_card.interact_info?.collected_count,
		评论数: item.note_card.interact_info?.comment_count,
		分享数: item.note_card.interact_info?.shared_count,
		类型: getExportType(item.note_card),
		链接: getNoteLink(item)
	}));

	try {
		const dirs = await window.electronAPI.app.openDialog({
			properties: ["openDirectory"],
			title: "选择保存Excel文件的文件夹"
		});

		if (dirs && dirs.length > 0) {
			const fileName = `${searchKeyword.value || '小红书笔记'}_${new Date().toISOString().split('T')[0]}.xlsx`;
			const filePath = `${dirs[0]}/${fileName}`;

			await window.electronAPI.excel.fileBuild({
				data: [{
					name: '小红书笔记数据',
					data: [
						Object.keys(excelData[0]),
						...excelData.map(item => Object.values(item))
					]
				}],
				path: filePath
			});

			ElMessage({
				message: "导出成功",
				type: "success"
			});
			window.electronAPI.app.showItemInFolder(filePath);
		}
	} catch (error: any) {
		console.error('导出Excel失败:', error);
		ElMessage({
			message: `导出失败: ${error.message}`,
			type: "error"
		});
	}
};

const startCrawling = async () => {
	if (!searchKeyword.value) {
		ElMessage.warning("请输入关键词");
		return;
	}

	isFetching.value = true;
	progress.value = 0;
	crawlResults.value = [];

	try {
		for (let page = 1; page <= pageCount.value; page++) {
			const res = await window.electronAPI.xhs.notesFetch({
				keyword: searchKeyword.value,
				page,
				type: crawlType.value,
			});

			const items = res.items.filter((item: any) => item.model_type === "note");
			console.log(items);
			if (items && items.length > 0) {
				crawlResults.value.push(...items);
				progress.value = (page / pageCount.value) * 100;

				// 暂停一下，避免频繁请求
				await new Promise(resolve => setTimeout(resolve, 1000)); // Renderer-safe sleep
			} else {
				break;
			}
		}

		items.value = crawlResults.value;
		localStorage.setItem("cachedNoteList", JSON.stringify(items.value));

		ElMessage({
			message: `成功获取 ${items.value.length} 条笔记`,
			type: "success",
		});
	} catch (error: any) {
		console.error("爬取失败:", error);
		ElMessage.error(`爬取失败，请检查网络或重试: ${error.message}`);
	} finally {
		isFetching.value = false;
		progress.value = 100;
	}
};

</script>

<style lang='scss' scoped>
@use '@/assets/styles/index.scss' as *;

.page-container {
	padding: $spacing-base;
}

.search-card {
	margin-bottom: $spacing-large;

	.search-form {
		margin-bottom: $spacing-base;

		.el-form-item {
			margin-bottom: $spacing-small;
			margin-right: $spacing-base;

			.el-input,
			.el-select,
			.el-input-number {
				width: 200px; // Adjust width as needed
			}
		}
	}

	.action-bar {
		display: flex;
		flex-wrap: wrap;
		gap: $spacing-small;
		margin-bottom: $spacing-base;
	}

	.progress-area {
		margin-top: $spacing-base;

		.progress-info {
			display: flex;
			justify-content: space-between;
			font-size: 12px;
			color: $text-secondary;
			margin-top: 4px;
		}
	}
}

.results-section {
	margin-top: $spacing-large;

	.section-header {
		display: flex;
		align-items: center;
		margin-bottom: $spacing-base;

		.section-title {
			display: flex;
			align-items: center;
			font-size: 18px;
			font-weight: 500;
			margin: 0;

			.el-icon {
				margin-right: $spacing-small;
				color: $primary-color;
			}

			.count-badge {
				display: inline-block;
				background-color: $primary-color;
				color: white;
				border-radius: 12px;
				padding: 2px 8px;
				font-size: 12px;
				margin-left: $spacing-small;
			}
		}
	}

	.note-list {
		display: grid;
		grid-template-columns: repeat(auto-fill, minmax(220px, 1fr)); // 调整卡片宽度，显示更多列
		gap: $spacing-base;

		.note-card {
			cursor: pointer;
			transition: all 0.3s ease-in-out;
			overflow: hidden;
			height: 100%;
			border-radius: 12px;
			border: 1px solid rgba(0, 0, 0, 0.05);

			&:hover {
				transform: translateY(-5px);
				box-shadow: 0 12px 20px rgba(0, 0, 0, 0.08);
				border-color: rgba(0, 0, 0, 0);
			}

			.el-card__body {
				padding: $spacing-base;
				display: flex;
				flex-direction: column;
				height: 100%;
			}

			.note-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: $spacing-small;

				.user-info {
					display: flex;
					align-items: center;
					overflow: hidden;

					.el-avatar {
						margin-right: $spacing-small;
						flex-shrink: 0;
						border: 2px solid rgba(255, 255, 255, 0.8);
						box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
					}

					.username {
						font-weight: 500;
						color: $text-primary;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
					}
				}

				.note-actions {
					display: flex;
					gap: 4px; // Smaller gap for text buttons
					flex-shrink: 0;
					opacity: 0.7;
					transition: opacity 0.2s ease;

					&:hover {
						opacity: 1;
					}
				}
			}

			.note-content {
				display: flex;
				flex-direction: column;
				flex-grow: 1;

				.note-image {
					width: 100%;
					aspect-ratio: 1 / 1; // 保持正方形
					position: relative;
					overflow: hidden;
					border-radius: 8px;
					margin-bottom: $spacing-small;
					box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);

					.thumbnail {
						width: 100%;
						height: 100%;
						object-fit: cover;
						transition: transform 0.4s ease;

						&:hover {
							transform: scale(1.05);
						}
					}

					.image-error {
						display: flex;
						justify-content: center;
						align-items: center;
						width: 100%;
						height: 100%;
						background-color: $background-light;
						color: $text-secondary;
						font-size: 24px;
					}

					.image-tag {
						position: absolute;
						bottom: $spacing-small;
						right: $spacing-small;
						background-color: rgba(0, 0, 0, 0.6);
						color: white;
						padding: 2px 8px;
						border-radius: 12px;
						font-size: 12px;
						display: flex;
						align-items: center;
						z-index: 1;
						backdrop-filter: blur(4px);

						.el-icon {
							margin-right: 4px;
						}
					}
				}

				.note-title {
					font-size: 16px;
					font-weight: 500;
					margin: 4px 0 $spacing-small 0;
					color: $text-primary;
					line-height: 1.4;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 2; // 限制为两行
					-webkit-box-orient: vertical;
					max-height: 2.8em; // 大约两行的高度
					transition: color 0.2s ease;

					&:hover {
						color: $primary-color;
					}
				}

				.note-stats {
					display: flex;
					justify-content: flex-start;
					gap: $spacing-base;
					margin-top: auto;
					font-size: 12px;
					color: $text-secondary;
					padding-top: $spacing-small;
					border-top: 1px dashed rgba(0, 0, 0, 0.05);

					.stat-item {
						display: flex;
						align-items: center;
						padding: 4px 0;
						margin-right: $spacing-small;

						.stat-icon {
							margin-right: 4px;
							font-size: 14px;
						}

						.like-icon {
							color: #ff4757; // Red
						}

						.collect-icon {
							color: #ffa502; // Orange
						}

						.comment-icon {
							color: #1e90ff; // Blue
						}
					}
				}
			}
		}
	}
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 300px; // Adjust height as needed
	color: $text-secondary;

	.el-icon {
		font-size: 60px;
		margin-bottom: $spacing-base;
	}

	p {
		font-size: 16px;
	}
}

/* 全局样式，确保图片预览在全屏显示 */
:deep(.el-image-viewer__wrapper) {
	position: fixed !important;
	top: 0 !important;
	right: 0 !important;
	bottom: 0 !important;
	left: 0 !important;
	z-index: 2050 !important;
}

:deep(.el-image-viewer__mask) {
	position: fixed !important;
	width: 100% !important;
	height: 100% !important;
	top: 0 !important;
	left: 0 !important;
	opacity: 0.5 !important;
	background: #000 !important;
}
</style>
