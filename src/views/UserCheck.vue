<template>
  <div class="page-container">
    <h1 class="page-title">用户检测</h1>

    <!-- 搜索区域 -->
    <div class="app-card">
      <div class="search-area">
        <el-input v-model="userText" placeholder="请输入博主链接或ID" class="search-input">
          <template #prefix>
            <el-icon>
              <User />
            </el-icon>
          </template>
        </el-input>

        <div class="action-buttons">
          <el-button type="primary" :loading="loading" @click="getInfo">
            <el-icon>
              <Search />
            </el-icon> 搜索
          </el-button>
          <el-button type="success" :loading="loading" @click="checkUser">
            <el-icon>
              <DataAnalysis />
            </el-icon> 分析
          </el-button>
        </div>
      </div>
    </div>

    <!-- 用户信息卡片 -->
    <div class="app-card user-profile-card" v-if="user.nickname">
      <div class="user-profile">
        <el-avatar :size="120" :src="user.image"></el-avatar>

        <div class="user-info">
          <h2 class="user-name">{{ user.nickname }}</h2>

          <div class="user-desc">
            {{ user.desc }}
          </div>

          <div class="user-location">
            <img src="//ci.xiaohongshu.com/2ea66ffa-d9a5-4866-802d-f912a7900111" class="location-icon" />
            <span>{{ user.location }}</span>
          </div>

          <div class="user-stats">
            <div class="stat-block">
              <span class="stat-number">{{ user.follows }}</span>
              <span class="stat-label">关注</span>
            </div>

            <div class="stat-block">
              <span class="stat-number">{{ user.fans }}</span>
              <span class="stat-label">粉丝</span>
            </div>

            <div class="stat-block">
              <span class="stat-number">{{ user.collected + user.liked }}</span>
              <span class="stat-label">获赞与收藏</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 笔记列表 -->
    <div class="app-card notes-card" v-if="notes.length > 0">
      <div class="section-header">
        <h3 class="section-title">
          <el-icon>
            <Document />
          </el-icon> 最新笔记
          <el-tag v-if="collectionRate" type="success" effect="plain" size="small" class="rate-tag">
            收录率: {{ collectionRate }}
          </el-tag>
        </h3>
      </div>

      <div class="note-list">
        <div v-for="note in notes" :key="note.id" class="note-item" @click="link(note.id)">
          <div class="note-image">
            <el-image :src="note.cover.url" fit="cover"></el-image>
          </div>

          <div class="note-content">
            <h4 class="note-title">{{ note.title }}</h4>

            <div class="note-status" v-if="collectionRate">
              <el-tag :type="note.include ? 'success' : 'danger'" size="small">
                {{ note.include ? '已收录' : '未收录' }}
              </el-tag>
            </div>

            <div class="note-stats">
              <div class="likes">
                <iconify-icon class="stat-icon like-icon" icon="mdi:heart" />
                <span>{{ note.likes }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { ElMessage, ElIcon } from 'element-plus';
import { User, Search, DataAnalysis, Document } from '@element-plus/icons-vue';
// import { MainInfo } from "@/main/api/xhs-spider"; // Need to migrate or replace

const userText = ref<string>("");
const user = ref({
  id: "",
  image: "",
  nickname: "",
  location: "",
  desc: "",
  liked: 0,
  collected: 0,
  fans: 0,
  follows: 0,
});
const notes = ref<Array<any>>([]);
const collectionRate = ref<string>("");
const count = ref<number>(10);
const loading = ref<boolean>(false);
const noMore = ref<boolean>(false);
// const mainInfo = ref<MainInfo | undefined>(undefined); // Need to migrate or replace

const initUser = (mainInfo: any) => { // Updated type to any for now
  user.value = mainInfo.userDetail;
  notes.value = mainInfo.notesDetail;
};

const getInfo = async () => {
  // Replace with window.electronAPI call for xhs-user-info
  const userId = getUserId();
  if (!userId) {
    ElMessage.warning("请输入有效的用户链接或ID");
    return;
  }

  console.log(`getInfo() -- 用户ID为:${userId}`);
  loading.value = true;
  try {
    const mainInfo = await window.electronAPI.xhs.userInfo(userId);
    initUser(mainInfo);
    collectionRate.value = ""; // Reset collection rate
  } catch (error) {
    console.error("获取用户信息失败", error);
    ElMessage.error("获取用户信息失败");
  } finally {
    loading.value = false;
  }
};

const checkUser = async () => {
  const userId = getUserId();
  if (!userId) {
    ElMessage.warning("请输入有效的用户链接或ID");
    return;
  }

  console.log(`checkUser() -- 用户ID为:${userId}`);
  loading.value = true;
  try {
    // First get user info if not already loaded or if user changed
    // if (!user.value || user.value.id !== userId) {
    //   await getInfo(); // This will update user.value and notes.value
    // }

    // Assuming user.value and notes.value are populated after getInfo()
    // If getInfo() was just called, we already have the info.
    // If user was already loaded, we use the cached info.
    if (!user.value || user.value.id !== userId) {
       // If user info is not loaded or user changed, fetch it first
       await getInfo();
       if (!user.value) { // If getInfo failed
         return;
       }
    }


    const res = await window.electronAPI.xhs.noteCheckUser();
    loading.value = false;
    // mainInfo.value = res.info; // Assuming res.info is the updated user and notes detail
    collectionRate.value = res.collectionRate;
    // initUser(mainInfo.value); // Update user and notes with potentially updated info from checkUser

    // Update notes with inclusion status from res.notesDetail
    if (res.notesDetail && Array.isArray(res.notesDetail)) {
        notes.value = notes.value.map(note => {
            const updatedNote = res.notesDetail.find((updated: any) => updated.id === note.id);
            return updatedNote ? { ...note, include: updatedNote.include } : note;
        });
    }


  } catch (error) {
    console.error("用户分析失败", error);
    ElMessage.error("用户分析失败");
  } finally {
    loading.value = false;
  }
};

const getUserId = (text?: string) => {
  console.log(text)
  if (!text) {
    text = userText.value;
  }
  const re = /user\/profile\/(\w+)/;
  let userId: string | undefined = undefined;
  if (re.test(text)) {
    console.log(`输入内容是链接:${text}`);
    userId = re.exec(text)?.[1]; // Use optional chaining
  } else if (/\w+/.test(text)) {
    userId = text;
  }
  return userId;
};

const link = (id: string) => {
  // Replace with window.electronAPI call for opening external link
  // Replace with window.electronAPI call for opening external link
  window.electronAPI.app.openExternal(`https://www.xiaohongshu.com/discovery/item/${id}`);
  // ElMessage.info(`打开笔记链接: https://www.xiaohongshu.com/discovery/item/${id}`); // Remove old message
};
</script>

<style lang="scss" scoped>
@use '@/assets/styles/index.scss' as *;

.page-container {
  padding: $spacing-base;
}

.page-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: $spacing-large;
}

.app-card {
  background-color: $background-card;
  border-radius: $border-radius-base;
  box-shadow: $box-shadow-light;
  padding: $spacing-base;
  margin-bottom: $spacing-base;
  transition: $transition-base;

  &:hover {
    box-shadow: $box-shadow-base;
  }
}

.search-area {
  display: flex;
  align-items: center;
  gap: $spacing-base;

  .search-input {
    flex: 1;
  }

  .action-buttons {
    display: flex;
    gap: $spacing-small;
  }
}

.user-profile-card {
  margin-top: $spacing-large;
}

.user-profile {
  display: flex;
  align-items: flex-start;

  .el-avatar {
    flex-shrink: 0;
    border: 3px solid rgba($primary-color, 0.1);
  }

  .user-info {
    margin-left: $spacing-large;
    flex: 1;
  }
}

.user-name {
  font-size: 20px;
  font-weight: 600;
  color: $text-primary;
  margin: 0 0 $spacing-small 0;
}

.user-desc {
  font-size: 14px;
  color: $text-regular;
  margin: $spacing-base 0;
  max-width: 500px;
  line-height: 1.5;
}

.user-location {
  display: flex;
  align-items: center;
  margin-bottom: $spacing-base;
  color: $text-secondary;
  font-size: 14px;

  .location-icon {
    width: 14px;
    height: 14px;
    margin-right: $spacing-small;
  }
}

.user-stats {
  display: flex;
  margin-top: $spacing-base;

  .stat-block {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-right: $spacing-large;

    .stat-number {
      font-size: 18px;
      font-weight: 600;
      color: $primary-color;
    }

    .stat-label {
      font-size: 12px;
      color: $text-secondary;
      margin-top: 2px;
    }
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-base;

  .section-title {
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: 500;
    margin: 0;

    .el-icon {
      margin-right: $spacing-small;
      color: $primary-color;
    }

    .rate-tag {
      margin-left: $spacing-small;
    }
  }
}

.note-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: $spacing-base;
}

.note-item {
  display: flex;
  padding: $spacing-small;
  border-radius: $border-radius-base;
  transition: $transition-base;
  cursor: pointer;

  &:hover {
    background-color: $background-light;
    transform: translateY(-2px);
  }

  .note-image {
    width: 100px;
    height: 100px;
    border-radius: $border-radius-base;
    overflow: hidden;
    flex-shrink: 0;

    .el-image {
      width: 100%;
      height: 100%;
    }
  }

  .note-content {
    margin-left: $spacing-base;
    flex: 1;

    .note-title {
      font-size: 14px;
      font-weight: 500;
      color: $text-primary;
      margin: 0 0 $spacing-small 0;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .note-status {
      margin: $spacing-small 0;
    }

    .note-stats {
      margin-top: $spacing-small;

      .likes {
        display: flex;
        align-items: center;
        color: $text-secondary;
        font-size: 14px;

        .iconify-icon {
          // Use iconify-icon for heart icon
          color: $primary-color;
          margin-right: 4px;
        }
      }
    }
  }
}

.stat-icon {
  margin-right: 4px;
}

.like-icon {
  color: #ff4757;
}

.likes {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: $text-secondary;
}
</style>
