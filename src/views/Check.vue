<template>
	<div class="page-container">
		<h1 class="page-title">检测工具</h1>

		<!-- 词汇检测卡片 -->
		<div class="app-card">
			<div class="section-header">
				<h3 class="section-title">
					<el-icon>
						<Warning />
					</el-icon> 词汇检测
				</h3>
			</div>

			<div class="check-content">
				<div class="input-output-container">
					<div class="input-area">
						<el-input type="textarea" :rows="10" placeholder="请输入需要检测的内容" v-model="textarea"
							class="text-input">
						</el-input>
					</div>

					<div class="arrow">
						<el-icon>
							<Right />
						</el-icon>
					</div>

					<div class="output-area">
						<div class="result-text" v-html="resText"></div>
					</div>
				</div>

				<div class="legend-area">
					<span class="legend-title">颜色说明：</span>

					<div class="legend-items">
						<span class="legend-item normal">通用词库({{ nums.normal }})</span>
						<span class="legend-item sensitive">敏感词({{ nums.m }})</span>
						<span class="legend-item xhs">小红书词({{ nums.xhs }})</span>
						<span class="legend-item ad">广告词({{ nums.ad }})</span>
						<span class="legend-item medical">医疗词({{ nums.yl }})</span>
					</div>
				</div>

				<div class="action-area">
					<el-button type="primary" @click="checkWords" :icon="Check">
						开始检测
					</el-button>
					<el-button @click="textarea = ''; resText = ''; nums = { normal: 0, m: 0, xhs: 0, ad: 0, yl: 0 };"
						:icon="Delete">
						清空内容
					</el-button>
				</div>
			</div>
		</div>

		<!-- 使用说明卡片 -->
		<div class="app-card info-card">
			<div class="section-header">
				<h3 class="section-title">
					<el-icon>
						<InfoFilled />
					</el-icon> 使用说明
				</h3>
			</div>

			<div class="info-content">
				<p>1. 在左侧文本框中输入需要检测的内容</p>
				<p>2. 点击"开始检测"按钮进行内容检测</p>
				<p>3. 检测结果将在右侧显示，不同类型的敏感词将用不同颜色标记</p>
				<p>4. 检测完成后可以根据结果修改您的内容</p>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { ElIcon } from 'element-plus';
import { Warning, Right, Check, Delete, InfoFilled } from '@element-plus/icons-vue';

const nums = ref({
	normal: 0,
	m: 0,
	xhs: 0,
	ad: 0,
	yl: 0,
});
const textarea = ref<string>('');
const resText = ref<string>('');

const checkWords = async () => {
	if (textarea.value) {
		// Replace with window.electronAPI call for xhs-note-check-word
		// const res = await ipcRenderer.invoke(
		// 	"xhs-note-check-word",
		// 	textarea.value
		// );

		// Mock data for now
		const res = {
			is_pass: false,
			words: [
				{ words: '敏感词', words_type: 1 },
				{ words: '小红书词', words_type: 2 },
				{ words: '广告词', words_type: 3 },
				{ words: '医疗词', words_type: 4 },
				{ words: '通用词', words_type: 0 },
			],
		};


		resText.value = textarea.value
			.replace(/\r\n/g, "<br>")
			.replace(/\n/g, "<br>");
		if (!res.is_pass) {
			nums.value = {
				normal: 0,
				m: 0,
				xhs: 0,
				ad: 0,
				yl: 0,
			};
			res.words.forEach((item) => {
				resText.value = resText.value.replace(
					item.words,
					getColorText(item)
				);
			});
		}
	}
};

const getColorText = (item: { words: string, words_type: number }) => {
	switch (item.words_type) {
		case 0:
			nums.value.normal++;
			return `<font style="background:#4a453e;color:#ffffff">${item.words}</font>`;
		case 1:
			nums.value.m++;
			return `<font style="background:#ff9100;color:#ffffff">${item.words}</font>`;
		case 2:
			nums.value.xhs++;
			return `<font style="background:#ff0000;color:#ffffff;">${item.words}</font>`;
		case 3:
			nums.value.ad++;
			return `<font style="background:#50aaff;color:#ffffff;">${item.words}</font>`;
		case 4:
			nums.value.yl++;
			return `<font style="background:#BC8F8F;color:#ffffff;">${item.words}</font>`;
		case 5:
			return `<font style="background:#ff00b3;color:#ffffff;">${item.words}</font>`;
		default:
			return item.words; // Fallback for unknown types
	}
};
</script>

<style lang="scss" scoped>
@use '@/assets/styles/index.scss' as *;

.page-container {
	padding: $spacing-base;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: $spacing-base;

	.section-title {
		display: flex;
		align-items: center;
		font-size: 18px;
		font-weight: 500;
		margin: 0;

		.el-icon {
			margin-right: $spacing-small;
			color: $primary-color;
		}
	}
}

.check-content {
	.input-output-container {
		display: flex;
		align-items: stretch;
		gap: $spacing-base;
		margin-bottom: $spacing-base;

		@media (max-width: 768px) {
			flex-direction: column;
		}

		.input-area,
		.output-area {
			flex: 1;

			.text-input {
				height: 100%;
			}
		}

		.arrow {
			display: flex;
			align-items: center;
			color: $text-secondary;
			font-size: 20px;

			@media (max-width: 768px) {
				display: none;
			}
		}

		.output-area {
			.result-text {
				height: 240px;
				overflow: auto;
				padding: $spacing-base;
				border: 1px solid $border-base;
				border-radius: $border-radius-base;
				background-color: $background-light;
				font-size: 14px;
				line-height: 1.6;
				text-align: left;
			}
		}
	}

	.legend-area {
		margin: $spacing-base 0;

		.legend-title {
			font-weight: 500;
			margin-right: $spacing-small;
			color: $text-regular;
		}

		.legend-items {
			display: flex;
			flex-wrap: wrap;
			gap: $spacing-small;
			margin-top: $spacing-small;
		}

		.legend-item {
			display: inline-block;
			padding: 4px 8px;
			border-radius: $border-radius-small;
			font-size: 12px;
			color: white;

			&.normal {
				background-color: #4a453e;
			}

			&.sensitive {
				background-color: #ff9100;
			}

			&.xhs {
				background-color: #ff0000;
			}

			&.ad {
				background-color: #50aaff;
			}

			&.medical {
				background-color: #bc8f8f;
			}
		}
	}

	.action-area {
		display: flex;
		gap: $spacing-small;
		margin-top: $spacing-large;
	}
}

.info-card {
	margin-top: $spacing-large;

	.info-content {
		color: $text-regular;
		line-height: 1.6;

		p {
			margin-bottom: $spacing-small;
			position: relative;
			padding-left: $spacing-small;

			&:before {
				content: '•';
				position: absolute;
				left: 0;
				color: $primary-color;
			}
		}
	}
}
</style>
