<template>
	<div class="page-container">
		<h1 class="page-title">笔记信息分析</h1>

		<!-- 笔记链接输入区域 -->
		<div class="app-card search-card">
			<div class="search-box">
				<el-input v-model="input" placeholder="请输入或粘贴小红书笔记链接" class="search-input" :prefix-icon="Link"
					clearable>
				</el-input>

				<div class="action-buttons" v-if="!checking">
					<el-button type="primary" :icon="Search" @click="searchNote">
						笔记详情
					</el-button>
					<el-button :icon="View" @click="show(input)">
						浏览原文
					</el-button>
					<!-- <el-button :icon="Check" @click="checkInclusion">
						收录检测
					</el-button> -->
					<el-button v-if="checkNodeType() == 'normal'" :icon="Download" @click="saveImageToDir">
						保存图片
					</el-button>
					<el-button v-if="checkNodeType() == 'video'" :icon="VideoCamera" @click="saveVideoToDir">
						保存视频
					</el-button>
				</div>

				<div class="loading-container" v-if="checking">
					<el-icon class="is-loading">
						<Loading />
					</el-icon>
					<span class="loading-text">正在加载笔记内容...</span>
				</div>
			</div>
		</div>

		<!-- 笔记内容展示区域 -->
		<el-row :gutter="20" v-if="note">
			<!-- 笔记内容 -->
			<el-col :span="16">
				<div class="app-card">
					<!-- 收录状态显示 -->
					<div class="status-bar">
						<div v-if="include !== undefined" class="status-tag">
							<el-tag :type="include ? 'success' : 'danger'" effect="dark" size="large">
								{{ include ? '已收录' : '未收录' }}
							</el-tag>
						</div>

						<div class="note-stats">
							<div class="stat-item">
								<Icon class="stat-icon like-icon" icon="mdi:heart" />
								<span class="stat-value">{{ note.liked_count || 0 }}</span>
								<span class="stat-label">点赞</span>
							</div>

							<div class="stat-item">
								<!-- 收藏  material-symbols:star-rounded -->
								<Icon class="stat-icon collect-icon" icon="tabler:star-filled" />
								<span class="stat-value">{{ note.collected_count || 0 }}</span>
								<span class="stat-label">收藏</span>
							</div>

							<div class="stat-item">
								<Icon class="stat-icon comment-icon" icon="mdi:comment" />
								<span class="stat-value">{{ note.comments_count || 0 }}</span>
								<span class="stat-label">评论</span>
							</div>
						</div>
					</div>

					<!-- 图片或视频展示 -->
					<div class="note-media">
						<div v-if="checkNodeType() == 'normal'" class="image-gallery">
							<el-carousel class="image-carousel" :interval="4000" type="card" height="400px"
								indicator-position="outside">
								<el-carousel-item v-for="(item, index) in imgs" :key="index">
									<el-image class="image-preview" :src="item.url_default" fit="cover"
										:preview-src-list="getImageList(imgs)" :preview-teleported="true"
										:initial-index="index">
									</el-image>
								</el-carousel-item>
							</el-carousel>
						</div>

						<div v-else-if="checkNodeType() == 'video'" class="video-preview">
							<div class="video-thumbnail">
								<el-image :src="note.video?.first_frame" fit="cover"
									:preview-src-list="[note.video?.first_frame]" :preview-teleported="true">
								</el-image>
								<div class="video-play-icon">
									<el-icon>
										<VideoPlay />
									</el-icon>
								</div>
							</div>
						</div>
					</div>

					<!-- 笔记内容 -->
					<div class="note-content">
						<h2 class="note-title">{{ note.title || '无标题' }}</h2>
						<div class="note-desc">{{ note.desc || '无内容' }}</div>
					</div>
				</div>
			</el-col>

			<!-- 用户信息 -->
			<el-col :span="8" v-if="user">
				<div class="app-card user-card">
					<div class="user-profile">
						<div class="avatar-container" @click="showUser">
							<el-avatar :size="80" :src="user.avatar" class="user-avatar"></el-avatar>
						</div>

						<div class="user-info">
							<h3 class="user-name" @click="showUser">{{ user.nickname }}</h3>

							<div class="user-desc" v-if="user.desc">
								{{ user.desc }}
							</div>

							<div class="user-location" v-if="user.ip_location">
								<el-icon>
									<Location />
								</el-icon>
								<span class="location-text">{{ user.ip_location }}</span>
							</div>
						</div>
					</div>

					<el-divider content-position="center">用户数据</el-divider>

					<div class="user-stats" v-if="user.follows">
						<div class="stat-block">
							<span class="stat-number">{{ user.follows }}</span>
							<span class="stat-label">关注</span>
						</div>

						<div class="stat-block">
							<span class="stat-number">{{ user.fans }}</span>
							<span class="stat-label">粉丝</span>
						</div>

						<div class="stat-block">
							<span class="stat-number">{{ user.interaction }}</span>
							<span class="stat-label">获赞与收藏</span>
						</div>
					</div>

					<el-divider content-position="center" v-if="getUserTags(user.tags)?.length > 0">用户标签</el-divider>

					<div class="tag-list" v-if="getUserTags(user.tags)?.length > 0">
						<el-tag v-for="(tag, index) in getUserTags(user.tags)" :key="index" effect="plain"
							class="user-tag">
							{{ tag.name }}
						</el-tag>
					</div>
				</div>
			</el-col>
		</el-row>

		<!-- 空状态提示 -->
		<div class="empty-state" v-if="!note && !checking">
			<el-icon>
				<Document />
			</el-icon>
			<div class="empty-text">
				<p>请输入小红书笔记链接开始分析</p>
				<p class="hint">例如: https://www.xiaohongshu.com/explore/xxx</p>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Icon } from "@iconify/vue";
import { Link, Search, View, Download, VideoCamera, Loading, VideoPlay, Location, Document } from '@element-plus/icons-vue';
import type { XhsImage, XhsNote } from '../../electron/main/api/models/note'; // Note: This path might need adjustment based on final main process structure

const route = useRoute();

const noteid = ref<string>('');
const input = ref<string>('');
const imgs = ref<XhsImage[]>([]);
const downloadDir = ref<string>('');
const checking = ref<boolean>(false);
const note = ref<XhsNote | null>(null);
const user = ref<any>(null);
const include = ref<boolean | undefined>(undefined);

onMounted(() => {
	console.log(`Home Mounted`);
	const noteUrl = route.query.noteUrl;
	if (noteUrl) {
		console.log(`链接存在：[${noteUrl}]`);
		input.value = noteUrl.toString();
		searchNote();
	} else if (localStorage.getItem("cachedData")) {
		// 从localStorage中获取缓存的数据
		const cachedData = JSON.parse(localStorage.getItem("cachedData") || '{}');
		// 将缓存的数据赋值给组件的相应属性
		input.value = cachedData.input ?? "";
		note.value = cachedData.note;
		imgs.value = cachedData.imgs;
		noteid.value = cachedData.noteid;
		user.value = cachedData.user;
		include.value = cachedData.include;
	}
});

const checkNodeType = () => {
	if (note.value) {
		return note.value.type;
	}
	return "";
};

const getUserTags = (tags: any[]) => {
	// 过滤tag.name为空的
	if (tags) {
		return tags.filter((item) => item.name);
	}
	return [];
};

const getImageList = (imgs: XhsImage[]) => {
	return imgs.map((item) => item.url_default);
};

const saveImageToDir = async () => {
	const dirs = await window.electronAPI.app.openDialog({
		properties: ["openDirectory"],
	});
	if (dirs && dirs.length > 0) {
		downloadDir.value = `${dirs[0]}/${noteid.value}`;
		const cloneObj = JSON.parse(JSON.stringify({
			dir: `${downloadDir.value}`,
			imgs: imgs.value,
			desc: note.value?.desc || '',
			title: note.value?.title || '',
		}));
		await window.electronAPI.xhs.noteImgsSave(cloneObj);
		window.electronAPI.app.showItemInFolder(downloadDir.value);
	}
};

const saveVideoToDir = async () => {
	const dirs = await window.electronAPI.app.openDialog({
		properties: ["openDirectory"],
	});
	if (dirs && dirs.length > 0) {
		downloadDir.value = `${dirs[0]}/${noteid.value}`;
		const cloneObj = JSON.parse(JSON.stringify({
			dir: `${downloadDir.value}`,
			video: note.value?.video,
			desc: note.value?.desc || '',
			title: note.value?.title || '',
		}));
		await window.electronAPI.xhs.noteVideoSave(cloneObj);
		window.electronAPI.app.showItemInFolder(downloadDir.value);
	}
};

const show = (input: string) => {
	// 直接从input中提取出http/https开头的链接
	const url = input.match(/(https?:\/\/[^\s]+)/)?.[0];
	if (url) {
		window.electronAPI.app.showLoginWindow(url);
	} else {
		window.electronAPI.app.showLoginWindow();
	}
};

const showUser = () => {
	if (user.value?.user_id) {
		window.electronAPI.app.showLoginWindow(`https://www.xiaohongshu.com/user/profile/${user.value.user_id}`);
	} else {
		// 弹出提示
		ElMessage.warning("无法打开用户主页");
	}
};

const searchNote = async () => {
	if (input.value) {
		checking.value = true;
		include.value = undefined;

		try {
			const noteData = await window.electronAPI.xhs.noteDetail(input.value); // Assuming xhsNoteImgs can also get detail

			if (noteData) {
				note.value = noteData as XhsNote;
				noteid.value = noteData.id;
				imgs.value = noteData.images_list || [];

				user.value = noteData.user
				const userData = await window.electronAPI.xhs.userInfo(noteData.user.user_id);
				if (userData) {
					user.value = userData;
				}

				// 将数据缓存到localStorage
				localStorage.setItem(
					"cachedData",
					JSON.stringify({
						input: input.value,
						note: note.value,
						imgs: imgs.value,
						noteid: noteid.value,
						user: user.value,
						include: include.value,
					})
				);
			}
		} catch (error) {
			console.error("获取笔记信息失败", error);
			ElMessage.error("获取笔记信息失败");
		} finally {
			checking.value = false;
		}
	} else {
		ElMessage.warning("请输入笔记链接");
	}
};

const checkInclusion = async () => {
	if (noteid.value && user.value?.red_id) {
		checking.value = true;

		try {
			const includeStatus = await window.electronAPI.xhs.noteCheck(
				noteid.value,
				user.value.red_id
			);

			include.value = includeStatus;

			// 更新缓存
			const cachedData = JSON.parse(localStorage.getItem("cachedData") || "{}");
			cachedData.include = include.value;
			localStorage.setItem("cachedData", JSON.stringify(cachedData));

			ElMessage({
				message: include.value ? "该笔记已被收录" : "该笔记未被收录",
				type: include.value ? "success" : "warning",
			});
		} catch (error) {
			console.error("检测收录状态失败", error);
			ElMessage.error("检测收录状态失败");
		} finally {
			checking.value = false;
		}
	} else {
		ElMessage.warning("请先获取笔记信息");
	}
};

</script>

<style lang="scss" scoped>
@use '@/assets/styles/index.scss' as *;

.search-card {
	margin-bottom: $spacing-large;

	.search-box {
		.search-input {
			width: 100%;
			margin-bottom: $spacing-base;

			// ::v-deep .el-input__inner { // Vue 3 does not need ::v-deep
			// 	height: 46px;
			// 	font-size: 16px;
			// }
		}

		.action-buttons {
			display: flex;
			flex-wrap: wrap;
			gap: $spacing-small;
		}

		.loading-container {
			display: flex;
			align-items: center;
			justify-content: center;
			margin: $spacing-base 0;

			.loading-text {
				margin-left: $spacing-small;
				color: $text-regular;
			}
		}
	}
}

.status-bar {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: $spacing-base;

	.status-tag {
		margin-right: $spacing-base;
	}

	.note-stats {
		display: flex;
		gap: $spacing-large;
		border-top: none;

		.stat-item {
			display: flex;
			align-items: center;

			.stat-icon {
				color: $primary-color;
				margin-right: 4px;
			}

			.like-icon {
				color: #ff4757; // 红色系
			}

			.comment-icon {
				color: #1e90ff; // 蓝色系
			}

			.collect-icon {
				color: #ffa502; // 黄色系
			}

			.stat-value {
				font-weight: 600;
				margin-right: 4px;
			}

			.stat-label {
				color: $text-secondary;
				font-size: 12px;
			}
		}
	}
}

.note-media {
	margin-bottom: $spacing-base;

	.image-gallery {
		.image-carousel {
			:deep(.el-carousel__item) {
				border-radius: $border-radius-base;
				overflow: hidden;

				&:hover {
					border-radius: $border-radius-base;
				}
			}
		}

		.image-preview {
			height: 100%;
			width: 100%;
			object-fit: contain;
		}
	}

	.video-preview {
		position: relative;
		width: 100%;
		height: 400px;
		border-radius: $border-radius-base;
		overflow: hidden;

		.video-thumbnail {
			width: 100%;
			height: 100%;
			position: relative;

			.el-image {
				width: 100%;
				height: 100%;
			}

			.video-play-icon {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				font-size: 60px;
				color: rgba(255, 255, 255, 0.8);
				background-color: rgba(0, 0, 0, 0.3);
				border-radius: 50%;
				width: 80px;
				height: 80px;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}
}

.note-content {
	.note-title {
		font-size: 20px;
		margin-bottom: $spacing-small;
	}

	.note-desc {
		color: $text-regular;
		line-height: 1.6;
	}
}

.user-card {
	.user-profile {
		display: flex;
		align-items: center;
		margin-bottom: $spacing-base;

		.avatar-container {
			margin-right: $spacing-base;
			width: 80px;
			height: 80px;
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.user-avatar {
			border-radius: 50%;
			width: 80px;
			height: 80px;
			object-fit: cover;
			overflow: hidden;
		}

		.user-info {
			flex-grow: 1;

			.user-name {
				font-size: 18px;
				margin-bottom: $spacing-small;
			}

			.user-desc {
				color: $text-secondary;
				font-size: 14px;
				margin-bottom: $spacing-small;
			}

			.user-location {
				display: flex;
				align-items: center;
				color: $text-secondary;
				font-size: 14px;

				.el-icon {
					margin-right: 4px;
				}
			}
		}
	}

	.user-stats {
		display: flex;
		justify-content: space-around;
		margin-bottom: $spacing-base;

		.stat-block {
			text-align: center;

			.stat-number {
				font-size: 18px;
				font-weight: 600;
				display: block;
			}

			.stat-label {
				color: $text-secondary;
				font-size: 12px;
			}
		}
	}

	.tag-list {
		display: flex;
		flex-wrap: wrap;
		gap: $spacing-small;

		.user-tag {
			margin-right: 0; // Gap handles spacing
		}
	}
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 400px;
	color: $text-secondary;

	.el-icon {
		font-size: 60px;
		margin-bottom: $spacing-base;
	}

	.empty-text {
		text-align: center;

		p {
			margin: 4px 0;
		}

		.hint {
			font-size: 12px;
		}
	}
}

/* 全局样式，确保图片预览在全屏显示 */
:deep(.el-image-viewer__wrapper) {
	position: fixed !important;
	top: 0 !important;
	right: 0 !important;
	bottom: 0 !important;
	left: 0 !important;
	z-index: 2050 !important;
}

:deep(.el-image-viewer__mask) {
	position: fixed !important;
	width: 100% !important;
	height: 100% !important;
	top: 0 !important;
	left: 0 !important;
	opacity: 0.5 !important;
	background: #000 !important;
}

/* 强制修复Element Plus组件的样式，确保头像圆形 */
:deep(.el-avatar) {
	display: block !important;
	width: 80px !important;
	height: 80px !important;
	border-radius: 50% !important;
	overflow: hidden !important;
}

:deep(.el-avatar img) {
	width: 100% !important;
	height: 100% !important;
	object-fit: cover !important;
	border-radius: 50% !important;
}
</style>
