<template>
  <div class="note-monitor-data-container">
    <!-- 头部操作栏 -->
    <div class="header-bar">
      <el-button type="primary" :icon="ArrowLeft" @click="router.back()">返回监控列表</el-button>
    </div>
    
    <!-- 笔记信息卡片 -->
    <el-card class="note-info-card">
      <div class="note-info-content">
        <div class="note-basic-info">
          <el-image :src="noteInfo.coverUrl" class="note-cover" fit="cover">
            <template #error>
              <div class="cover-placeholder">
                <el-icon><Picture /></el-icon>
              </div>
            </template>
          </el-image>
          <div class="note-details">
            <h2 class="note-title">{{ noteInfo.title || '未知标题' }}</h2>
            <div class="author-info">
              <el-avatar :src="noteInfo.authorAvatar" :size="32" shape="circle" fit="cover"></el-avatar>
              <span class="author-name">{{ noteInfo.authorName || '未知作者' }}</span>
            </div>
          </div>
        </div>
        <div class="task-status-section">
          <el-tag v-if="taskStatus" :type="getStatusType(taskStatus)" size="large">
            {{ getStatusText(taskStatus) }}
          </el-tag>
        </div>
      </div>
    </el-card>

    <!-- 数据图表卡片 -->
    <el-card class="chart-card">
      <template #header>
        <div class="chart-header">
          <span class="chart-title">数据趋势分析</span>
          <el-radio-group v-model="selectedMetric" size="small">
            <el-radio-button label="likes">点赞数</el-radio-button>
            <el-radio-button label="comments">评论数</el-radio-button>
            <el-radio-button label="forwards">转发数</el-radio-button>
          </el-radio-group>
        </div>
      </template>
      <div v-if="historyData.length > 0" ref="chartContainer" class="chart-container"></div>
      <div v-else class="empty-chart">
        <el-empty description="暂无监控数据" />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import * as echarts from 'echarts';
import { ElMessage } from 'element-plus';
import { ArrowLeft, Picture } from '@element-plus/icons-vue';
const ipcRenderer = (window as any).ipcRenderer;
const route = useRoute();
const router = useRouter();

const taskId = route.params.taskId as string;

const noteInfo = ref({
  title: '',
  authorName: '',
  authorAvatar: '',
  coverUrl: '',
});
const historyData = ref<any[]>([]);
const selectedMetric = ref('likes');
const chartContainer = ref<HTMLElement | null>(null);
let chartInstance: echarts.ECharts | null = null;
const taskStatus = ref('');

// 状态相关的辅助方法
const getStatusType = (status: string): string => {
  switch (status) {
    case 'RUNNING':
      return 'success';
    case 'PAUSED':
      return 'warning';
    case 'STOPPED':
      return 'info';
    case 'FAILED':
      return 'danger';
    default:
      return 'info';
  }
};

const getStatusText = (status: string): string => {
  switch (status) {
    case 'RUNNING':
      return '运行中';
    case 'PAUSED':
      return '已暂停';
    case 'STOPPED':
      return '已停止';
    case 'FAILED':
      return '失败';
    default:
      return '未知';
  }
};

const fetchNoteInfo = async () => {
  try {
    const task = await ipcRenderer.invoke('monitor:get-task', taskId);
    if (!task) {
      ElMessage.error('未找到该监控任务');
      router.back();
      return;
    }
    const info = task.data;
    noteInfo.value = {
      title: info?.title || info?.desc || '',
      authorName: info?.authorName || '',
      authorAvatar: info?.authorAvatar || '',
      coverUrl: (info?.coverUrl) || '',
    };
    taskStatus.value = task.status === 'paused' ? 'PAUSED' : (task.status === 'running' ? '' : (task.status === 'stopped' ? 'STOPPED' : (task.status || '')));
    if (task.status === 'failed') taskStatus.value = 'FAILED';
  } catch (e) {
    ElMessage.error('获取笔记信息失败');
    console.error(e);
  }
};

const fetchHistory = async () => {
  try {
    const data = await ipcRenderer.invoke('monitor:get-history', taskId);
    historyData.value = data || [];
  } catch (e) {
    ElMessage.error('获取历史数据失败');
  }
};

const renderChart = () => {
  if (!chartContainer.value) return;
  if (!chartInstance) {
    chartInstance = echarts.init(chartContainer.value);
  }
  const timestamps = historyData.value.map(d => new Date(d.timestamp).toLocaleString());
  const metricData = historyData.value.map(d => d[selectedMetric.value]);
  chartInstance.setOption({
    tooltip: { trigger: 'axis' },
    xAxis: { type: 'category', data: timestamps },
    yAxis: { type: 'value', name: metricLabel(selectedMetric.value) },
    series: [{
      name: metricLabel(selectedMetric.value),
      type: 'line',
      smooth: true,
      data: metricData,
      itemStyle: { color: '#409EFF' },
      lineStyle: { width: 3 },
    }],
    grid: { left: '3%', right: '4%', bottom: '10%', containLabel: true },
  });
};

const metricLabel = (key: string) => {
  return {
    likes: '点赞数',
    comments: '评论数',
    forwards: '转发数',
  }[key] || key;
};

onMounted(async () => {
  await fetchNoteInfo();
  await fetchHistory();
  renderChart();
});

watch(selectedMetric, renderChart);
</script>

<style scoped>
.note-monitor-data-container {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.header-bar {
  margin-bottom: 20px;
  
  .el-button {
    background: linear-gradient(90deg, #355CFF 0%, #3A8DFF 100%);
    border: none;
    border-radius: 20px;
    font-weight: 600;
    color: #fff;
    
    &:hover {
      background: linear-gradient(90deg, #2546b8 0%, #2566b8 100%);
    }
  }
}

.note-info-card {
  border-radius: 16px;
  box-shadow: 0 4px 24px 0 rgba(80, 120, 255, 0.07);
  border: none;
  margin-bottom: 20px;
  
  .note-info-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
  }
  
  .note-basic-info {
    display: flex;
    align-items: center;
    gap: 20px;
    flex: 1;
  }
  
  .note-cover {
    width: 80px;
    height: 80px;
    border-radius: 12px;
    flex-shrink: 0;
    
    .cover-placeholder {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      background: #f5f7fa;
      color: #909399;
      border-radius: 12px;
    }
  }
  
  .note-details {
    flex: 1;
    min-width: 0;
  }
  
  .note-title {
    font-size: 20px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 12px 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .author-info {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .el-avatar {
      flex-shrink: 0;
    }
    
    .author-name {
      color: #606266;
      font-size: 14px;
      font-weight: 500;
    }
  }
  
  .task-status-section {
    flex-shrink: 0;
  }
}

.chart-card {
  border-radius: 16px;
  box-shadow: 0 4px 24px 0 rgba(80, 120, 255, 0.07);
  border: none;
  
  .chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .chart-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }
  
  .chart-container {
    width: 100%;
    height: 400px;
  }
  
  .empty-chart {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300px;
  }
}
</style>