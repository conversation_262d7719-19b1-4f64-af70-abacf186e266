<template>
  <div class="note-monitor-container">
    <div class="header">
      <h2>笔记监控</h2>
      <el-button type="primary" :icon="Plus" @click="addDialogVisible = true">添加监控任务</el-button>
    </div>

    <!-- 笔记监控任务列表组件 -->
    <note-task-list :tasks="noteTasks" @view-data="goToDataPanel" @edit-task="openEditDialog"
      @refresh-tasks="fetchNoteTasks" />

    <!-- 添加监控任务弹窗组件 -->
    <add-note-task-dialog v-model="addDialogVisible" @task-added="fetchNoteTasks" />

    <!-- 编辑任务弹窗组件 -->
    <edit-note-task-dialog v-model="editDialogVisible" :task="currentEditTask as Record<string, any>"
      @task-updated="fetchNoteTasks" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
import { Plus } from '@element-plus/icons-vue';
// 导入组件
import NoteTaskList from '../components/note-monitor/NoteTaskList.vue';
import AddNoteTaskDialog from '../components/note-monitor/AddNoteTaskDialog.vue';
import EditNoteTaskDialog from '../components/note-monitor/EditNoteTaskDialog.vue';

const ipcRenderer = (window as any).ipcRenderer;
const router = useRouter();

// 声明任务类型接口
interface NoteTask {
  id: string;
  name: string;
  noteId: string;
  frequency: number;
  status: 'running' | 'paused' | 'stopped';
  lastRunTime: number;
  coverUrl?: string;
  title?: string;
  authorAvatar?: string;
  authorName?: string;
  [key: string]: any;
}

// 状态管理
const noteTasks = ref<NoteTask[]>([]);
const addDialogVisible = ref(false);
const editDialogVisible = ref(false);
const currentEditTask = ref<NoteTask | null>(null);

// 获取所有笔记监控任务
const fetchNoteTasks = async () => {
  try {
    const tasks = await ipcRenderer.invoke('monitor:get-tasks');
    noteTasks.value = tasks.filter((task: any) => task.type === 'note');
  } catch (error) {
    ElMessage.error('获取任务列表失败');
    console.error('获取笔记监控任务失败:', error);
  }
};

// 打开编辑对话框
const openEditDialog = (task: NoteTask) => {
  currentEditTask.value = task;
  editDialogVisible.value = true;
};

// 导航到数据面板
const goToDataPanel = (taskId: string) => {
  router.push(`/note-monitor/data/${taskId}`);
};

// 任务更新事件监听器
const taskUpdatedListener = (event: any, updateInfo: any) => {
  console.log('收到任务更新事件:', updateInfo);
  if (updateInfo && updateInfo.type === 'task-added' && updateInfo.data) {
    // 如果是添加笔记类型任务，刷新笔记任务列表
    if (updateInfo.data.type === 'note') {
      fetchNoteTasks();
      ElMessage.success('检测到新的笔记监控任务，已自动刷新列表');
    }
  }
};

// 生命周期钩子
onMounted(() => {
  fetchNoteTasks();
  // 监听任务更新事件
  ipcRenderer.on('monitor:task-updated', taskUpdatedListener);
});

onUnmounted(() => {
  // 移除事件监听器
  ipcRenderer.off('monitor:task-updated', taskUpdatedListener);
});
</script>

<style scoped>
.note-monitor-container {
  padding: 24px;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header h2 {
  margin: 0;
  color: #222;
  font-size: 24px;
  font-weight: 700;
}
</style>