<template>
  <div class="monitor-container">
    <el-tabs v-model="activeTab" class="monitor-tabs">
      <el-tab-pane label="笔记监控" name="note">
        <NoteMonitor />
      </el-tab-pane>
      <el-tab-pane label="博主监控" name="blogger">
        <BloggerMonitor />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import NoteMonitor from './NoteMonitor.vue';
import BloggerMonitor from './BloggerMonitor.vue';

const activeTab = ref('note');
</script>

<style scoped>
.monitor-container {
  padding: 20px;
}
.monitor-tabs {
  margin-top: 20px;
}
</style>