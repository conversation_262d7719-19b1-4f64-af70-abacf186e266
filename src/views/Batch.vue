<template>
	<div class="page-container">
		<h1 class="page-title">批量处理</h1>

		<!-- 批量处理配置卡片 -->
		<div class="app-card main-card">
			<div class="section-header">
				<h3 class="section-title">
					<el-icon>
						<Picture />
					</el-icon> 笔记图片批量获取
				</h3>
			</div>

			<!-- 文件选择和配置区域 -->
			<div class="batch-config">
				<div class="config-item">
					<span class="item-label">Excel文件：</span>
					<div class="item-value">
						<el-input v-model="excelFile" placeholder="请选择Excel文件" readonly class="custom-input">
							<template #prefix>
								<el-icon>
									<Document />
								</el-icon>
							</template>
							<template #append>
								<el-button :icon="FolderOpened" @click="chooseFile" class="select-btn"></el-button>
							</template>
						</el-input>
					</div>
				</div>

				<div class="config-item">
					<span class="item-label">下载目录：</span>
					<div class="item-value">
						<el-input v-model="imgsDir" placeholder="请选择保存目录" readonly class="custom-input">
							<template #prefix>
								<el-icon>
									<Folder />
								</el-icon>
							</template>
							<template #append>
								<el-button :icon="FolderOpened" @click="chooseDir" class="select-btn"></el-button>
							</template>
						</el-input>
					</div>
				</div>
			</div>

			<!-- 进度条和按钮 -->
			<div class="progress-section">
				<div class="progress-header">
					<div class="progress-label">处理进度：</div>
					<div class="progress-percentage">{{ percentage }}%</div>
				</div>

				<div class="progress-bar">
					<el-progress :text-inside="false" :stroke-width="12" :percentage="percentage"
						:status="downloading ? 'success' : ''" :striped="downloading" :striped-flow="downloading"
						:show-text="false">
					</el-progress>
				</div>

				<div class="action-buttons">
					<el-button type="primary" :icon="Download" @click="start"
						:disabled="!excelFile || !imgsDir || downloading" class="action-btn start-btn"
						:loading="downloading">
						{{ downloading ? '处理中...' : '开始处理' }}
					</el-button>

					<el-button type="danger" :icon="Close" @click="stop" :disabled="!downloading"
						class="action-btn stop-btn">
						{{ stopBtnMsg }}
					</el-button>
				</div>
			</div>
		</div>

		<!-- 处理说明卡片 -->
		<div class="app-card info-card">
			<div class="section-header">
				<h3 class="section-title">
					<el-icon>
						<InfoFilled />
					</el-icon> 使用说明
				</h3>
			</div>

			<div class="help-text">
				<div class="help-item">
					<div class="step-number">1</div>
					<div class="step-content">选择包含笔记链接的Excel文件，文件应包含链接列</div>
				</div>
				<div class="help-item">
					<div class="step-number">2</div>
					<div class="step-content">选择图片保存的目标文件夹</div>
				</div>
				<div class="help-item">
					<div class="step-number">3</div>
					<div class="step-content">点击"开始处理"按钮开始批量下载图片</div>
				</div>
				<div class="help-item">
					<div class="step-number">4</div>
					<div class="step-content">处理过程中可随时点击"停止"按钮暂停操作</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import { ElMessage, ElIcon } from 'element-plus';
import { FolderOpened, Download, Close, Picture, InfoFilled, Loading, Document, Folder } from '@element-plus/icons-vue';
// import { Ut } from "../../main/utils/sleep"; // Need to migrate or replace

const excelFile = ref<string>('');
const imgsDir = ref<string>('');
const percentage = ref<number>(0);
const downloading = ref<boolean>(false);
const sleeping = ref<boolean>(false);
const stopBtnMsg = ref<string>("停止");

// 在组件创建时从缓存加载数据
onMounted(() => {
	// 从localStorage中恢复上次的设置
	const cachedBatchData = localStorage.getItem("cachedBatchData");
	if (cachedBatchData) {
		const data = JSON.parse(cachedBatchData);
		excelFile.value = data.excelFile || "";
		imgsDir.value = data.imgsDir || "";
	}
});

// 当组件销毁前保存数据到缓存
onBeforeUnmount(() => {
	// 保存当前设置到localStorage
	const batchData = {
		excelFile: excelFile.value,
		imgsDir: imgsDir.value
	};
	localStorage.setItem("cachedBatchData", JSON.stringify(batchData));
});

// 当数据变化时更新缓存
watch([excelFile, imgsDir], () => {
	const batchData = {
		excelFile: excelFile.value,
		imgsDir: imgsDir.value
	};
	localStorage.setItem("cachedBatchData", JSON.stringify(batchData));
});

const chooseFile = async () => {
	const res = await window.electronAPI.app.openDialog({
		properties: ["openFile"],
		filters: [{ name: "xlsx", extensions: ["xlsx"] }],
	});
	if (res && res.length > 0) {
		excelFile.value = res[0];
		initDir(excelFile.value);
	}
};

const initDir = async (filePath: string) => {
	const dir = await window.electronAPI.fileSystem.pathDirname(filePath);
	const name = await window.electronAPI.fileSystem.pathBasename(filePath);
	const sep = await window.electronAPI.fileSystem.pathSep();
	imgsDir.value = dir + sep + name.split(".")[0];
};

const checkNodeType = (note: any) => {
	if (note) {
		return note.type;
	}
	return "";
};

const chooseDir = async () => {
	const res = await window.electronAPI.app.openDialog({
		properties: ["openDirectory"],
	});
	if (res && res.length > 0) {
		imgsDir.value = res[0];
	}
};


const start = async () => {
	if (!excelFile.value || !imgsDir.value) {
		ElMessage.warning("请先选择Excel文件和下载目录");
		return;
	}

	downloading.value = true;
	percentage.value = 0;

	try {
		const obj = await window.electronAPI.excel.fileParse(excelFile.value);

		if (obj.length > 0) {
			const datas = obj[0].data;

			// Create base download directory if it doesn't exist
			const baseDirExists = await window.electronAPI.fileSystem.existsSync(imgsDir.value);
			if (!baseDirExists) {
				await window.electronAPI.fileSystem.mkdirSync(imgsDir.value, { recursive: true });
			}

			for (const arrs of datas) {
				if (downloading.value) {
					const item = arrs[0];
					const index = datas.indexOf(arrs);
					percentage.value = Math.floor(((index + 1) / datas.length) * 100);

					if (item && item.includes("http")) {
						console.log(`开始获取笔记信息图片`);
						const url = arrs[0];
						const nickname = arrs[1];
						if (nickname) {
							console.log(`该笔记已经存在`);
							continue;
						}
						const res = await window.electronAPI.xhs.noteImgs({ text: url });

						if (res.nickname) {
							const sep = await window.electronAPI.fileSystem.pathSep();
							const noteDir = `${imgsDir.value}${sep}${res.nickname}`;
							const noteDirExists = await window.electronAPI.fileSystem.existsSync(noteDir);
							if (!noteDirExists) {
								await window.electronAPI.fileSystem.mkdirSync(noteDir, { recursive: true });
							}

							// 保存视频和图片
							if (checkNodeType(res) == "normal") {
								await window.electronAPI.xhs.noteImgsSave({
									dir: noteDir,
									imgs: res.imgs,
									desc: res.desc,
									title: res.title,
								});
							} else if (checkNodeType(res) == "video") {
								if (res.video) {
									await window.electronAPI.xhs.noteVideoSave({
										dir: noteDir,
										video: res.video,
										title: res.title,
										desc: res.desc,
									});
								}
							}
							arrs[1] = res.nickname;
							await window.electronAPI.excel.fileBuild({
								path: excelFile.value,
								data: obj,
							});
						}
						if (index < datas.length - 1) {
							console.log(`休息5秒中`);
							sleeping.value = true;
							await new Promise(resolve => setTimeout(resolve, 5000)); // Renderer-safe sleep
							sleeping.value = false;
						}
					}
				}
			}
			await window.electronAPI.app.showItemInFolder(imgsDir.value);
			downloading.value = false;
			ElMessage.success("批量处理完成");
		}
	} catch (error) {
		console.error("批量处理失败", error);
		ElMessage.error("批量处理失败");
		downloading.value = false;
	}
};

const stop = async () => {
	while (sleeping.value) {
		stopBtnMsg.value = "停止中";
		await new Promise(resolve => setTimeout(resolve, 100)); // Renderer-safe sleep
	}
	stopBtnMsg.value = "停止";
	downloading.value = false;
};

</script>

<style lang="scss" scoped>
@use '@/assets/styles/index.scss' as *;

.page-container {
	padding: $spacing-base;
}

.main-card {
	border-radius: $border-radius-card;
	transition: $transition-base;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(250, 250, 250, 0.95) 100%);
	box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
	border: 1px solid rgba(0, 0, 0, 0.05);
	padding: $spacing-large;

	&:hover {
		box-shadow: 0 12px 30px rgba(0, 0, 0, 0.1);
		transform: translateY(-2px);
	}
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: $spacing-large;
	border-bottom: 1px solid $border-lighter;
	padding-bottom: $spacing-base;

	.section-title {
		display: flex;
		align-items: center;
		font-size: 20px;
		font-weight: 600;
		margin: 0;
		color: $text-primary;

		.el-icon {
			margin-right: $spacing-base;
			color: $primary-color;
			font-size: 24px;
		}
	}
}

.batch-config {
	margin-bottom: $spacing-large;

	.config-item {
		display: flex;
		align-items: center;
		margin-bottom: $spacing-large;

		.item-label {
			width: 120px;
			font-weight: 500;
			color: $text-regular;
			font-size: 15px;
		}

		.item-value {
			flex: 1;
		}

		.custom-input {
			.el-input__wrapper {
				box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
				border: 1px solid $border-light;
				padding: 8px 12px;
				transition: all 0.3s ease;

				&:hover {
					border-color: $primary-light;
				}

				&:focus-within {
					box-shadow: 0 0 0 2px rgba($primary-color, 0.2) !important;
				}
			}

			.el-input__prefix-inner {
				margin-right: $spacing-small;
				color: $text-secondary;
			}

			.select-btn {
				background-color: $background-light;
				border-color: $border-light;
				color: $text-regular;

				&:hover {
					background-color: $primary-color;
					border-color: $primary-color;
					color: white;
				}
			}
		}
	}
}

.progress-section {
	margin-top: $spacing-xl;
	background-color: rgba(255, 255, 255, 0.7);
	border-radius: $border-radius-large;
	padding: $spacing-large;
	border: 1px solid $border-lighter;

	.progress-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: $spacing-small;

		.progress-label {
			font-weight: 500;
			color: $text-regular;
			font-size: 15px;
		}

		.progress-percentage {
			font-weight: 600;
			color: $primary-color;
			font-size: 18px;
			background: -webkit-linear-gradient($primary-color, $primary-light);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
		}
	}

	.progress-bar {
		margin-bottom: $spacing-large;

		:deep(.el-progress-bar__inner) {
			background: linear-gradient(to right, $primary-color, $primary-light);
			transition: width 0.3s ease-in-out;
			border-radius: 8px;
		}

		:deep(.el-progress-bar__outer) {
			background-color: rgba($primary-color, 0.1);
			border-radius: 8px;
			height: 8px !important;
		}
	}

	.action-buttons {
		display: flex;
		gap: $spacing-base;
		margin-top: $spacing-base;
		justify-content: center;

		.action-btn {
			padding: 12px 24px;
			font-size: 15px;
			font-weight: 500;
			border-radius: $border-radius-base;
			transition: all 0.3s ease;
			min-width: 140px;

			.el-icon {
				margin-right: 6px;
				font-size: 16px;
			}
		}

		.start-btn {
			background-color: $primary-color;
			border-color: $primary-color;

			&:hover:not(:disabled) {
				background-color: $primary-dark;
				transform: translateY(-2px);
				box-shadow: 0 4px 12px rgba($primary-color, 0.3);
			}

			&:disabled {
				opacity: 0.7;
			}
		}

		.stop-btn {
			&:hover:not(:disabled) {
				transform: translateY(-2px);
				box-shadow: 0 4px 12px rgba($danger-color, 0.3);
			}
		}
	}
}

.info-card {
	margin-top: $spacing-large;
	padding: $spacing-large;
	background: linear-gradient(135deg, rgba(255, 250, 250, 0.9) 0%, rgba(255, 245, 245, 0.9) 100%);
	border-left: 4px solid $primary-color;

	.help-text {
		color: $text-regular;
		line-height: 1.6;

		.help-item {
			display: flex;
			align-items: flex-start;
			margin-bottom: $spacing-base;

			.step-number {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 28px;
				height: 28px;
				background-color: $primary-color;
				color: white;
				border-radius: 50%;
				font-weight: 600;
				margin-right: $spacing-base;
				flex-shrink: 0;
			}

			.step-content {
				padding-top: 4px;
			}
		}
	}
}
</style>
