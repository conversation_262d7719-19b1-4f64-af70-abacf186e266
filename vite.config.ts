import { defineConfig, UserConfig } from 'vite' // Import UserConfig type
import path, { resolve } from 'node:path'
import vue from '@vitejs/plugin-vue'
import electron from 'vite-plugin-electron/simple'
import renderer from 'vite-plugin-electron-renderer'
import { readFileSync } from 'node:fs'

// 获取项目根目录的 package.json
const packageJsonPath = resolve(__dirname, 'package.json');
const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf-8'));

// https://vitejs.dev/config/
export default defineConfig(({ command }) => {
  const isServe = command === 'serve'
  const isBuild = command === 'build'
  const sourcemap = isServe || !!process.env.VSCODE_DEBUG

  const config: UserConfig & { electronBuilder?: any } = { // Explicitly type config and allow electronBuilder
    resolve: {
      alias: {
        '@': path.join(__dirname, 'src'),
      },
    },
    base: './',
    plugins: [
      vue(),
      electron({
        main: {
          // Main process entry file of the Electron App.
          entry: 'electron/main/index.ts',
          onstart(options) {
            if (process.env.VSCODE_DEBUG) {
              console.log(/* For `.vscode/.debug.script.mjs` */'[startup] Electron App')
            } else {
              options.startup()
            }
          },
          vite: {
            build: {
              sourcemap,
              minify: isBuild,
              outDir: 'dist-electron/main',
              rollupOptions: {
                external: [
                  'sharp', // sharp needs to be external
                  '@ffmpeg-installer/ffmpeg', // FFmpeg installer needs to be external
                  'fluent-ffmpeg', // fluent-ffmpeg needs to be external
                ], 
              },
            },
          },
        },
        preload: {
          input: 'electron/preload/index.ts',
          vite: {
            build: {
              sourcemap: sourcemap,
              minify: isBuild,
              outDir: 'dist-electron/preload',
              rollupOptions: {
                // external: Object.keys('dependencies' in pkg ? pkg.dependencies : {}),
                external: ['sharp'], // sharp might be used in preload? If not, remove.
              },
            },
          },
        },
        // Ployfill the Electron and Node.js API for Renderer process.
        // If you want use Node.js in Renderer process, the `nodeIntegration` needs to be enabled in the Main process.
        // See 👉 https://github.com/electron-vite/vite-plugin-electron-renderer
        renderer: {},
      }),
      renderer(),
    ],
    define: {
      __APP_VERSION__: JSON.stringify(packageJson.version),
    },
    clearScreen: false,
  }
  return config;
})