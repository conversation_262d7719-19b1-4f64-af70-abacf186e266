# 项目变更日志

## 2025-06-01

- 初始化创建 Cursor Rules 文件，添加以下规则：
  - 01-project-overview.mdc: 项目概述
  - 02-project-structure.mdc: 项目结构
  - 03-architecture.mdc: 架构说明
  - 04-code-conventions.mdc: 代码约定
  - 05-development-workflow.mdc: 开发工作流

这些规则文件将帮助 AI 更好地理解项目结构和约定，提高代码导航和开发效率。

## 2025-06-02

- 代码优化：将 XhsSpider 类中的 camelToSnake 方法抽取到工具类中
  - 创建了新的工具类文件 electron/main/utils/object-utils.ts
  - 实现了 camelToSnake 和 snakeToCamel 两个方法，用于对象键名的命名风格转换
  - 修改 XhsSpider 类，使用新的工具类方法
  - 增加了对循环引用和特殊对象类型的处理，提高了方法的健壮性

## 2025-06-03

- 重构 xhs-spider.ts 文件，优化代码结构和质量：
  - 创建了 electron/main/api/models/api-types.ts 文件，定义了 API 相关的类型接口，减少 any 的使用
  - 创建了 electron/main/utils/url-utils.ts 文件，抽取了 URL 处理相关的功能
  - 创建了 electron/main/utils/transform-utils.ts 文件，抽取了数据转换相关的功能
  - 重构 XhsSpider 类，按功能分组方法，添加详细的 JSDoc 注释
  - 增强了错误处理，为所有方法添加 try-catch
  - 删除了未使用的注释代码，提高代码可读性
  - 代码结构更加清晰，便于维护和扩展

# 开发日志

## 2023 年 9 月 17 日 - 任务服务后端适配新数据结构

### 改造内容

1. **后端任务类改造**

   - 更新 `BloggerMonitorTask` 和 `NoteMonitorTask` 类以适配新的数据结构
   - 将任务特定数据迁移到 `data` 对象中
   - 为属性添加访问器方法，保持向后兼容性

2. **数据迁移机制**

   - 添加兼容性代码，自动迁移旧格式数据到新格式
   - 保留旧字段的支持，确保平滑过渡
   - 在运行时更新博主和笔记的详细信息

3. **功能增强**
   - `BloggerMonitorTask` 现在会定期刷新博主信息（粉丝数、关注数等）
   - `NoteMonitorTask` 增加了更多数据收集（点赞数、评论数等）
   - 完善错误处理和日志记录

### 优化项

1. 自动数据迁移逻辑确保了系统平滑升级
2. 使用访问器方法提供了更清晰的数据访问接口
3. 数据结构调整为未来的扩展提供了灵活性

## 2023 年 9 月 16 日 - 博主监控数据结构优化

### 改造内容

1. **数据结构优化**

   - 重构 MonitorTaskConfig 接口，添加 `data` 字段，根据任务类型存储不同的数据
   - 对于 `blogger` 类型，data 包含：nickname, avatar, desc, gender, fans, follows, interaction, notes 等字段
   - 对于 `note` 类型，data 包含：coverUrl, title, authorAvatar, authorName, likeCount 等字段

2. **URL 解析功能增强**

   - 增加从 URL 中提取博主 ID 的功能
   - 支持处理完整的小红书用户主页 URL
   - 支持处理小红书分享链接
   - 能自动将各种格式转换为标准的博主 ID 格式

3. **组件更新**
   - 更新 AddBloggerTaskDialog 组件，使用新的数据结构
   - 更新 BloggerTaskList 组件，适配新的数据访问方式
   - 保持与后端接口的数据一致性

### 代码优化

1. 提高数据结构的灵活性与可扩展性
2. 统一数据访问模式
3. 保持对旧版结构的兼容性
4. 优化错误处理与用户体验

## 2023 年 9 月 15 日 - 博主监控界面优化改造

### 改造内容

1. **重构与组件化**

   - 将原有的单文件组件拆分为多个独立组件，遵循组件化开发原则
   - 主要组件包括：
     - `BloggerMonitor.vue` - 主视图组件
     - `AddBloggerTaskDialog.vue` - 批量添加监控任务弹框组件
     - `BloggerTaskList.vue` - 博主监控任务列表组件
     - `BloggerWorksList.vue` - 博主作品列表组件

2. **功能增强**

   - 实现批量添加博主监控功能，支持一次添加多个博主
   - 添加进度条显示，直观展示批量操作的进度
   - 优化任务列表显示，增加博主头像、粉丝数、获赞数等信息
   - 增加作品列表点击查看功能

3. **UI/UX 优化**

   - 美化界面设计，使用卡片式布局
   - 改进数据展示形式，使用适合的展示组件
   - 增加动效和过渡，提升用户体验
   - 优化操作流程，更符合用户习惯

4. **代码质量提升**
   - 增加详细代码注释，提高可维护性
   - 修复类型错误，提高类型安全性
   - 优化错误处理，增强用户体验
   - 统一代码风格，提高代码可读性

### 待优化项

1. 考虑加入批量导出功能
2. 支持按多种条件筛选博主列表
3. 考虑加入数据可视化图表分析

# 更新日志

## 2024-03-21

### 修复

- 修复了 `BloggerMonitor.vue` 中 `ipcRenderer.removeListener` 方法不存在的问题
- 将 `removeListener` 替换为 `removeAllListeners` 以适配新版本 Electron 的 API
- 进一步修复：使用正确的 `ipcRenderer.off()` 方法移除事件监听，这是通过 contextBridge 暴露的标准方法
- 最终修复：重构事件监听机制，将监听器函数提取为变量并在移除时传入相同的引用，解决了 "listener must be of type function" 的错误

## 2024-03-22

### 新增功能

- 实现博主作品列表功能
- 添加了 `xhs:get-blogger-notes` IPC 处理程序，允许获取博主的笔记列表
- 在 MonitorService 类中添加 `getBloggerNotes` 方法，用于处理获取博主笔记的请求
- 支持分页加载博主笔记，优化用户体验

### 修复

- 修复了点击查看博主作品列表时出现 "No handler registered for 'xhs:get-blogger-notes'" 错误的问题

## 2024-03-23

### 优化

- 将博主笔记获取逻辑优化为从缓存获取，减少不必要的网络请求
- 修复博主监控任务信息中作品数（`notes`）一直为 0 的问题
- 增强了作品数获取的稳定性，优先从互动信息中获取，兜底使用实际获取到的笔记数
- 即使获取博主详情信息失败，也会尝试更新笔记数，确保信息尽可能完整

## 2024-03-24

### 优化

- 完善了博主笔记列表排序机制：
  - 添加了灵活的排序选项支持（时间排序、原始排序、反向排序）
  - 修复了笔记列表不按原始爬取顺序显示的问题
  - 在前端组件中使用 `original` 排序类型，保持与小红书官方显示的一致顺序
  - 确保排序不会破坏原始笔记的展示顺序

## 2024-03-25

### 界面优化

- 美化了博主监控界面的视觉效果：
  - 优化了作品数显示，添加了圆角背景和醒目的颜色标识
  - 改进了作品列表中点赞数和评论数的显示样式
  - 为点赞数添加了橙色高亮，为评论数添加了蓝色高亮
  - 使用半透明背景和模糊效果提升视觉层次感
  - 统一了数据展示的样式，使界面更加专业和美观

## 2024-03-26

### 界面优化

- 进一步优化了作品数按钮的显示效果：
  - 移除了红色边框，使用更加柔和的蓝色系配色
  - 替换了 el-button 为自定义 div 元素，消除了默认按钮样式的干扰
  - 添加了精致的边框和阴影效果，提升立体感
  - 增加了悬停动画效果，提升交互体验
  - 优化了内边距和字体粗细，使数字显示更加醒目
